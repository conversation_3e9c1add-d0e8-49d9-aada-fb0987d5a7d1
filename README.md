# 中興社工作日曆系統 (Sinotech Work Calendar System)

[![.NET](https://img.shields.io/badge/.NET-8.0-blue.svg)](https://dotnet.microsoft.com/download/dotnet/8.0)
[![Vue.js](https://img.shields.io/badge/Vue.js-3.2-green.svg)](https://vuejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-4.9-blue.svg)](https://www.typescriptlang.org/)
[![License](https://img.shields.io/badge/License-BSD--3--Clause-orange.svg)](LICENSE)

## 專案概述

中興工程顧問社內部專用的工作日曆管理系統，提供完整的班別管理、工作日設定、假日管理和員工排班功能。系統採用前後端分離架構，使用 Windows Authentication 進行身分驗證，部署於企業內部 IIS 環境。

### 主要功能

- 📅 **工作日曆管理**: 記錄和管理各種工作日類型
- 👥 **班別管理**: 支援多種班別設定，每日工作時數可彈性調整
- 🏖️ **假日管理**: 支援國定假日和農曆節日自動轉換
- ⏰ **彈性工時**: 支援彈性上下班時間設定
- 📊 **加班計算**: 根據工作日類型自動計算加班時數與倍率
- 👤 **員工排班**: 個別員工的班別分配和特殊排班

### 技術架構

- **後端**: .NET Core 8.0 C# + ASP.NET Core Web API
- **前端**: Vue 3.2 + TypeScript + Vite
- **UI 框架**: PrimeVue + Bootstrap 5
- **資料庫**: Microsoft SQL Server
- **認證**: Windows Authentication
- **部署**: IIS 伺服器

## 快速開始

### 系統需求

- .NET 8.0 SDK
- Node.js 16+ 
- Microsoft SQL Server 2019+
- IIS 10+ (生產環境)

### 安裝步驟

1. **複製專案**
   ```bash
   git clone <repository-url>
   cd WorkdayCalendar
   ```

2. **設定資料庫**
   ```bash
   # 執行資料庫建立腳本
   sqlcmd -S <server> -d master -i WorkdayCalendar/App_Data/CreateDb.sql
   ```

3. **設定連線字串**
   ```bash
   # 編輯 appsettings.json 中的連線字串
   # 設定 Workday 和 MIS 資料庫連線
   ```

4. **安裝前端相依套件**
   ```bash
   cd WorkdayCalendar/ClientApp
   npm install
   ```

5. **建置與執行**
   ```bash
   # 開發環境
   cd WorkdayCalendar/ClientApp
   npm run dev
   
   # 在另一個終端機執行後端
   cd WorkdayCalendar
   dotnet run
   ```

### 開發環境設定

詳細的開發環境設定請參考 [開發指南](docs/DEVELOPMENT.md)

## 專案結構

```
WorkdayCalendar/
├── WorkdayCalendar/              # 主要後端專案
│   ├── App_Code/                 # 核心業務邏輯與資料存取層
│   ├── Controllers/              # Web API 控制器
│   ├── DTO/                      # 資料傳輸物件
│   ├── Models/                   # 資料模型
│   ├── ClientApp/                # Vue.js 前端應用程式
│   │   ├── src/
│   │   │   ├── components/       # Vue 元件
│   │   │   ├── pages/           # 頁面元件
│   │   │   ├── router/          # 路由設定
│   │   │   ├── store/           # 狀態管理
│   │   │   └── assets/          # 靜態資源
│   │   └── package.json
│   ├── Program.cs               # 應用程式進入點
│   └── Startup.cs               # 服務設定與中介軟體配置
├── TestWorkdayCalendar/         # 單元測試專案
└── docs/                        # 專案文件
```

詳細的檔案結構說明請參考 [Structure.md](Structure.md)

## API 文件

系統提供完整的 RESTful API，支援 Swagger 文件：

- **開發環境**: `https://localhost:5001/swagger`
- **API 端點**: `/api/[controller]/[action]`

主要 API 控制器：
- `WorkdayController`: 工作日曆管理
- `WorkShiftController`: 班別管理
- `ShiftWeekdayController`: 週間班別設定
- `UserController`: 使用者管理
- `LogonUserController`: 登入使用者資訊

詳細的 API 文件請參考 [API 文件](docs/API.md)

## 部署指南

### IIS 部署

1. **建置應用程式**
   ```bash
   dotnet publish -c Release -o ./publish
   ```

2. **設定 IIS**
   - 建立應用程式集區 (.NET Core)
   - 啟用 Windows Authentication
   - 設定應用程式路徑

3. **設定連線字串**
   - 更新生產環境的 `appsettings.json`
   - 確保資料庫連線正常

詳細的部署指南請參考 [部署文件](docs/DEPLOYMENT.md)

## 開發指南

### 程式碼風格

- 所有程式碼必須包含詳細的繁體中文註解
- 函式宣告需要說明用途、參數和回傳值
- Vue 元件必須使用 Composition API 和 `<script setup lang="ts">`
- 遵循 PascalCase (類別/方法) 和 camelCase (變數) 命名規範

### 測試

```bash
# 執行單元測試
cd TestWorkdayCalendar
dotnet test

# 執行前端測試
cd WorkdayCalendar/ClientApp
npm run test
```

### 貢獻指南

1. Fork 專案
2. 建立功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交變更 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 開啟 Pull Request

詳細的開發指南請參考 [開發文件](docs/DEVELOPMENT.md)

## 系統架構

系統採用三層式架構設計：

- **展示層**: Vue.js SPA 應用程式
- **業務邏輯層**: ASP.NET Core Web API
- **資料存取層**: Entity Framework Core + SQL Server

詳細的系統架構說明請參考 [架構文件](Architecture_UML_Analysis.md)

## 安全性考量

- 使用 Windows Authentication 進行身分驗證
- 所有 API 端點都需要授權
- 資料庫連線使用加密連線
- 敏感資訊不會記錄在日誌中

## 故障排除

### 常見問題

1. **資料庫連線失敗**
   - 檢查連線字串設定
   - 確認 SQL Server 服務狀態
   - 驗證使用者權限

2. **Windows Authentication 失敗**
   - 確認 IIS 設定啟用 Windows Authentication
   - 檢查應用程式集區身分設定

3. **前端建置失敗**
   - 清除 node_modules 並重新安裝
   - 檢查 Node.js 版本相容性

更多故障排除資訊請參考 [故障排除指南](docs/TROUBLESHOOTING.md)

## 版本資訊

- **目前版本**: 1.0.3
- **最低需求**: .NET 8.0, Node.js 16+
- **相容性**: Windows Server 2019+, IIS 10+

## 授權條款

本專案採用 BSD-3-Clause 授權條款。詳細資訊請參考 [LICENSE](LICENSE) 檔案。

## 聯絡資訊

- **開發團隊**: 中興工程顧問社 IT 部門
- **專案維護**: [維護者資訊]
- **技術支援**: [支援聯絡方式]

## 相關文件

- [開發指南](docs/DEVELOPMENT.md)
- [API 文件](docs/API.md)
- [部署指南](docs/DEPLOYMENT.md)
- [系統架構](Architecture_UML_Analysis.md)
- [檔案結構](Structure.md)
- [故障排除](docs/TROUBLESHOOTING.md)