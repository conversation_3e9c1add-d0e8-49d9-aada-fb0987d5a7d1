# 中興社工作日曆系統架構分析與 UML 圖表

## 系統概述

中興工程顧問社內部專用的工作日曆系統，基於 .NET Core 8.0 C# 與 Vue 3.2 開發，採用前後端分離架構，使用 Windows Authentication 進行身分驗證。

## 系統架構圖

```mermaid
graph TB
    subgraph "前端層 (Vue 3.2 + TypeScript)"
        A[Vue 應用程式]
        A1[頁面元件<br/>Workday.vue<br/>Shifts.vue<br/>StaffShift.vue]
        A2[共用元件<br/>WorkShift.vue<br/>ShiftWeekday.vue<br/>ChooseShift.vue<br/>ChooseUser.vue]
        A3[狀態管理<br/>Pinia Store]
        A4[路由管理<br/>Vue Router]
        A5[API 服務<br/>api.ts]
    end

    subgraph "後端層 (.NET Core 8.0)"
        B[ASP.NET Core Web API]
        B1[控制器層<br/>WorkdayController<br/>WorkShiftController<br/>ShiftWeekdayController<br/>UserController<br/>LogonUserController]
        B2[資料傳輸物件<br/>WorkdayDTO<br/>WorkShiftDTO<br/>ShiftWeekdayDTO]
        B3[業務邏輯層<br/>App_Code]
        B4[資料模型<br/>Models]
    end

    subgraph "資料存取層"
        C[資料存取物件 DAO]
        C1[EmployeeDAO<br/>員工資料存取]
        C2[JobDAO<br/>職務資料存取]
        C3[SqlUtil<br/>SQL 工具類別]
        C4[單例模式快取<br/>SingletonUserName<br/>SingletonJob]
    end

    subgraph "資料庫層"
        D[(MS SQL Server)]
        D1[(Workday 資料庫<br/>工作日曆資料)]
        D2[(MIS 資料庫<br/>員工資料)]
    end

    subgraph "測試層"
        E[xUnit 測試專案]
        E1[單元測試<br/>FakeItEasy<br/>Moq]
    end

    A --> A1
    A --> A2
    A --> A3
    A --> A4
    A --> A5
    A5 -->|HTTP/HTTPS| B
    B --> B1
    B1 --> B2
    B2 --> B3
    B3 --> B4
    B3 --> C
    C --> C1
    C --> C2
    C --> C3
    C --> C4
    C -->|SQL 查詢| D
    D --> D1
    D --> D2
    E --> E1
    E1 -.->|測試| B
```

## 資料模型類別圖

```mermaid
classDiagram
    class Workday {
        +short ShiftId
        +DateTime WorkDate
        +short DayType
        +DateTime ArrivalTime
        +DateTime DepartureTime
        +DateTime FlexibleArrivalBefore
        +DateTime FlexibleArrivalAfter
        +DateTime FlexibleDepartureBefore
        +DateTime FlexibleDepartureAfter
        +DateTime MiddayBreakStart
        +DateTime MiddayBreakEnd
        +DateTime MorningRestStart
        +DateTime MorningRestEnd
        +DateTime AfternoonRestStart
        +DateTime AfternoonRestEnd
        +double WorkHours
        +short WeekDay
        +string Comment
    }

    class WorkShift {
        +int ShiftId
        +string ShiftName
        +string Workdays
        +int RestDay
        +int FixedDayOff
        +string ArrivalTime
        +string DepartureTime
        +string FlexibleArrivalBefore
        +string FlexibleArrivalAfter
        +string FlexibleDepartureBefore
        +string FlexibleDepartureAfter
        +string MiddayBreakStart
        +string MiddayBreakEnd
        +string MorningRestStart
        +string MorningRestEnd
        +string AfternoonRestStart
        +string AfternoonRestEnd
        +string Comment
    }

    class ShiftWeekday {
        +int ShiftId
        +string ShiftName
        +int WeekDay
        +string ArrivalTime
        +string DepartureTime
        +string FlexibleArrivalBefore
        +string FlexibleArrivalAfter
        +string FlexibleDepartureBefore
        +string FlexibleDepartureAfter
        +string MiddayBreakStart
        +string MiddayBreakEnd
        +string MorningRestStart
        +string MorningRestEnd
        +string AfternoonRestStart
        +string AfternoonRestEnd
        +string Comment
    }

    class Holiday {
        +string Date
        +bool IsLunar
        +string Name
    }

    class WorkdayParam {
        +int year
        +int shiftId
        +string startDate
        +Holiday[] holidays
    }

    Workday --|> WorkShift : "belongs_to"
    WorkShift --o  ShiftWeekday : "has_weekday_settings"
    WorkdayParam --o  Holiday : "contains"
```

## 資料存取層架構圖

```mermaid
graph TB
    subgraph "資料傳輸物件 (DTO)"
        A[WorkdayDto]
        B[WorkShiftDto]
        C[ShiftWeekdayDto]
    end

    subgraph "資料存取物件 (DAO)"
        D[EmployeeDao<br/>員工資料存取]
        E[JobDAO<br/>職務資料存取]
    end

    subgraph "單例模式快取"
        F[SingletonUserName<br/>員工資料快取]
        G[SingletonJob<br/>職務資料快取]
    end

    subgraph "工具類別"
        H[SqlUtil<br/>SQL 工具類別]
        I[Utility<br/>通用工具類別]
    end

    subgraph "資料庫"
        J[(Workday Database<br/>工作日曆資料庫)]
        K[(MIS Database<br/>員工資訊系統)]
    end

    A --> H
    B --> H
    C --> H
    D --> F
    E --> G
    F --> K
    G --> K
    H --> J
    H --> K

    A -->|CRUD 操作| J
    B -->|CRUD 操作| J
    C -->|CRUD 操作| J
    D -->|查詢員工資料| K
    E -->|查詢職務資料| K
```

## 前端元件架構圖

```mermaid
graph TD
    subgraph "Vue 應用程式架構"
        A[App.vue<br/>根元件]
        B[Router<br/>路由管理]
        C[Store<br/>狀態管理 Pinia]
    end

    subgraph "頁面元件 (Pages)"
        D[Workday.vue<br/>工作日曆頁面]
        E[Shifts.vue<br/>班別管理頁面]
        F[StaffShift.vue<br/>員工班別頁面]
    end

    subgraph "共用元件 (Components)"
        G[WorkShift.vue<br/>班別元件]
        H[ShiftWeekday.vue<br/>班表週間日元件]
        I[ChooseShift.vue<br/>選擇班別元件]
        J[ChooseUser.vue<br/>選擇使用者元件]
        K[MyDropdown.vue<br/>自訂下拉選單]
        L[NavMenu.vue<br/>導航選單]
    end

    subgraph "服務層"
        M[api.ts<br/>API 服務]
        N[Employee.ts<br/>員工服務]
    end

    subgraph "第三方元件庫"
        O[PrimeVue<br/>UI 元件庫]
        P[Axios<br/>HTTP 客戶端]
    end

    A --> B
    A --> C
    B --> D
    B --> E
    B --> F
    
    D --> G
    D --> H
    D --> I
    E --> G
    E --> H
    F --> J
    
    G --> K
    H --> K
    I --> K
    J --> K
    
    D --> M
    E --> M
    F --> M
    G --> M
    H --> M
    
    M --> N
    M --> P
    
    G --> O
    H --> O
    I --> O
    J --> O
    K --> O
```

## 員工管理類別圖

```mermaid
classDiagram
    class IEmployee {
        <<interface>>
        +string UserID
        +string Username
        +string Email
        +string Department
        +string JobNo
        +bool IsManager
        +string Job
        +string RankNo
        +string Rank
        +int DepartmentID
        +bool IsValid()
    }

    class Employee {
        <<abstract>>
        #string userid
        #string username
        #string email
        #string department
        #string jobno
        #string job
        #string rankno
        #string rank
        #int departmentID
        +string UserID
        +string Username
        +string Email
        +string Department
        +string JobNo
        +abstract bool IsManager
        +string Job
        +string RankNo
        +string Rank
        +int DepartmentID
    }

    class SinotechEmployee {
        -string ConnectionString
        -string _department
        -string _departmentId
        -bool _isValid
        -string _userid
        +bool IsManager
        +bool IsValid()
    }

    class EmployeeRole {
        +string RoleId
        +string RoleName
        +EmployeeRole(roleId, roleName)
    }

    class EmployeeDao {
        -IConfiguration Configuration
        -static int maxUserId
        -static DateTime LastReadTime
        +DataTable GetEmployee()
        +DataTable GetEmployee(int deptid)
        +DataTable GetEmployeeByTeamid(int teamid)
        +bool IsValidUserid(string userID)
        +string GetName(string userid)
        +string GetJobNo(string userid)
        +string GetRankNo(string userid)
        +string GetRankName(string userid)
        +string GetJobName(string userid)
        +int GetTeamId(string userid)
        +string GetUserID(string name)
        +string GetEmail(string userid)
        +int GetDeptId(string userid)
        +int GetDeptNo(string userid)
        +string GetDeptName(string userid)
        +string GetDeptSName(string userid)
        +void Reload()
    }

    class SingletonUserName {
        -static Hashtable ht
        -static Hashtable htDeptNo
        -static Hashtable htDeptName
        -static Hashtable htDeptSName
        -static Hashtable htEmail
        -static Hashtable htJobNo
        -static Hashtable htRankNo
        -static Hashtable htRankName
        -static Hashtable htTeam
        -static DataTable dt
        -static SingletonUserName instance
        -static readonly object syncRoot
        -readonly IConfiguration Configuration
        +DataTable Users
        +SingletonUserName Instance
        +void Reload()
        +object UserName(string userid)
        +object GetDeptNo(string userid)
        +object GetDeptName(string userid)
        +object GetDeptSName(string userid)
        +object GetEmail(string userid)
        +object GetJobNo(string userid)
        +object GetRankNo(string userid)
        +object GetRankName(string userid)
        +object GetTeamId(string userid)
    }

    IEmployee <|.. Employee
    Employee <|-- SinotechEmployee
    EmployeeDao --> SingletonUserName
    SingletonUserName --> EmployeeRole
```

## 系統時序圖 - 工作日曆查詢流程

```mermaid
sequenceDiagram
    participant U as 使用者
    participant F as Vue 前端
    participant C as WorkdayController
    participant D as WorkdayDto
    participant S as SqlUtil
    participant DB as SQL Server

    U->>F: 選擇年份和班別
    F->>C: GET /api/Workday/GetWorkdays?year=2024&shiftId=1
    C->>D: GetWorkdays(year, shiftId)
    D->>S: GetDataTable(connectionString, sql)
    S->>DB: 執行 SQL 查詢
    DB-->>S: 回傳查詢結果
    S-->>D: 回傳 DataTable
    D-->>C: 回傳 DataTable
    C-->>F: 回傳 JSON 資料
    F-->>U: 顯示工作日曆
```

## 系統時序圖 - 新增工作日流程

```mermaid
sequenceDiagram
    participant U as 使用者
    participant F as Vue 前端
    participant C as WorkdayController
    participant D as WorkdayDto
    participant S as SqlUtil
    participant DB as SQL Server
    participant L as EventLog

    U->>F: 填寫工作日資料並提交
    F->>C: POST /api/Workday/Add (workday)
    C->>C: 取得使用者身分 (Windows Auth)
    C->>D: Add(userId, ip, workday)
    D->>S: ExecuteNonQuery(sql, parameters)
    S->>DB: 執行 INSERT 語句
    DB-->>S: 回傳執行結果
    S->>L: 記錄操作日誌
    S-->>D: 回傳執行結果
    D-->>C: 回傳成功/失敗
    C-->>F: 回傳 boolean 結果
    F-->>U: 顯示操作結果
```

## 部署架構圖

```mermaid
graph TB
    subgraph "企業內部網路環境"
        subgraph "IIS 伺服器"
            A[ASP.NET Core 8.0<br/>Web API 後端]
            B[Vue 3.2 前端<br/>靜態檔案]
        end

        subgraph "資料庫伺服器"
            C[(SQL Server<br/>Workday Database)]
            D[(SQL Server<br/>MIS Database)]
        end

        subgraph "Active Directory"
            E[Windows Authentication<br/>身分驗證服務]
        end

        subgraph "企業內部使用者"
            F[員工電腦<br/>瀏覽器]
        end
    end

    F -->|HTTPS| A
    F -->|HTTPS| B
    A --> C
    A --> D
    A --> E
    E -.->|驗證| F

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#e8f5e8
    style E fill:#fff3e0
    style F fill:#fce4ec
```

## 技術架構說明

### 後端技術堆疊 (Backend Tech Stack)

- **.NET Core 8.0**: 主要開發框架
- **ASP\.NET Core Web API**: RESTful API 服務
- **Entity Framework Core**: 資料存取 (部分使用)
- **ADO\.NET**: 直接資料庫存取
- **Windows Authentication**: 企業內部身分驗證
- **MS SQL Server**: 資料庫系統

### 前端技術堆疊 (Frontend Tech Stack)

- **Vue 3.2**: 前端框架
- **TypeScript**: 型別安全的 JavaScript
- **Vite**: 建置工具
- **PrimeVue**: UI 元件庫
- **Pinia**: 狀態管理
- **Vue Router**: 路由管理
- **Axios**: HTTP 客戶端

### 設計模式

- **單例模式**: 員工資料和職務資料快取
- **資料傳輸物件 (DTO)**: API 資料傳輸
- **資料存取物件 (DAO)**: 資料庫操作封裝
- **MVC 模式**: 控制器-模型-視圖分離

### 主要功能模組

1. **工作日管理**: 記錄與管理工作日類型，用於計算加班時數與倍率
2. **班別管理**: 支援多種班別設定，每個班別可設定不同的工作時間
3. **員工管理**: 整合 Windows Authentication，自動取得員工資訊
4. **假日管理**: 支援國曆和農曆假日設定
5. **權限管理**: 區分一般使用者和管理員權限

### 資料庫設計特點

- **Workday 資料庫**: 儲存工作日曆相關資料
- **MIS 資料庫**: 企業員工資訊系統，提供員工基本資料
- **快取機制**: 使用單例模式快取常用的員工和職務資料
- **日誌記錄**: 完整記錄所有資料異動操作

這個系統採用現代化的前後端分離架構，結合企業內部的 Windows 驗證機制，提供了完整的工作日曆管理功能，特別適合企業內部使用。
