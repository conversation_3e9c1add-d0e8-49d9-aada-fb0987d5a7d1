# 變更日誌 (Changelog)

本文件記錄中興社工作日曆系統的所有重要變更。

格式基於 [Keep a Changelog](https://keepachangelog.com/zh-TW/1.0.0/)，
版本號遵循 [語意化版本](https://semver.org/lang/zh-TW/)。

## [1.0.4] - 2025-06-16

### 新增

- 完整的專案文件系統
- API 文件 (docs/API.md)
- 開發指南 (docs/DEVELOPMENT.md)
- 部署指南 (docs/DEPLOYMENT.md)
- 故障排除指南 (docs/TROUBLESHOOTING.md)
- 貢獻指南 (docs/CONTRIBUTING.md)

### 改善

- 增強程式碼註解和文件
- 改善 README.md 內容
- 更新前端 ClientApp README.md

### 修復

- 修正文件中的連結和格式問題

## [1.0.3] - 2024-01-15

### 新增

- 新增批次新增工作日曆功能
- 支援農曆節日自動轉換為陽曆日期
- 新增彈性上下班時間設定
- 加入工作時數自動計算功能

### 改善

- 優化資料庫查詢效能
- 改善前端使用者介面響應速度
- 增強錯誤處理機制
- 更新 Vue.js 到 3.5.16 版本
- 更新 .NET 到 8.0 版本

### 修復

- 修復班別選擇器在特定瀏覽器的顯示問題
- 解決農曆日期轉換的精確度問題
- 修正工作時數計算的邊界條件錯誤

### 安全性

- 加強 Windows Authentication 驗證機制
- 改善 SQL 查詢的安全性
- 更新相依套件以修復安全漏洞

## [1.0.2] - 2023-12-01

### 新增

- 員工個人排班檢視功能
- 班別衝突檢查機制
- 工作日曆匯出功能 (Excel 格式)

### 改善

- 優化前端元件載入速度
- 改善行動裝置的使用體驗
- 增強日誌記錄功能

### 修復

- 修復週間班別設定的儲存問題
- 解決時區轉換的相關問題
- 修正假日標記的顯示錯誤

## [1.0.1] - 2023-10-15

### 新增

- 新增假日管理功能
- 支援國定假日自動標記
- 加入班別複製功能

### 改善

- 改善資料庫連線穩定性
- 優化 API 回應時間
- 增強前端錯誤處理

### 修復

- 修復工作日曆顯示的日期錯位問題
- 解決班別時間設定的驗證錯誤
- 修正使用者權限檢查的邏輯問題

### 變更

- 調整 API 端點的命名規範
- 更新資料庫索引以提升查詢效能

## [1.0.0] - 2023-09-01

### 新增

- 初始版本發布
- 基本工作日曆管理功能
- 班別管理系統
- 週間班別設定功能
- 使用者管理和權限控制
- Windows Authentication 整合
- RESTful API 設計
- Vue.js 前端應用程式
- 響應式設計支援

### 技術特性

- .NET Core 8.0 後端框架
- Vue 3.2 + TypeScript 前端
- SQL Server 資料庫
- PrimeVue UI 元件庫
- Vite 建置工具
- xUnit 單元測試框架

### 核心功能

- 工作日類型管理
- 彈性工時設定
- 休息時間配置
- 加班時數計算
- 農曆節日支援
- 多班別支援

## 版本說明

### 版本號格式

版本號採用 `主版本.次版本.修訂版本` 格式：

- **主版本**: 不相容的 API 變更
- **次版本**: 向後相容的功能新增
- **修訂版本**: 向後相容的問題修復

### 變更類型

- **新增**: 新功能
- **改善**: 現有功能的改進
- **修復**: 錯誤修復
- **變更**: 現有功能的變更
- **移除**: 功能移除
- **安全性**: 安全性相關的變更
- **棄用**: 即將移除的功能

### 相容性說明

#### 1.0.x 系列

- 向後相容 1.0.0 版本
- 資料庫結構保持相容
- API 端點保持穩定

#### 升級注意事項

##### 從 1.0.2 升級到 1.0.3

1. 更新應用程式檔案
2. 執行資料庫遷移腳本 (如有)
3. 清除瀏覽器快取
4. 重新啟動應用程式服務

##### 從 1.0.1 升級到 1.0.2

1. 備份現有資料庫
2. 更新應用程式檔案
3. 更新前端相依套件
4. 測試核心功能

##### 從 1.0.0 升級到 1.0.1

1. 停止應用程式服務
2. 備份資料庫和設定檔
3. 部署新版本
4. 執行資料庫更新腳本
5. 重新啟動服務

### 已知問題

#### 1.0.3

- 在 Internet Explorer 11 中，部分 CSS 動畫可能不正常顯示
- 大量資料匯出時可能出現記憶體使用量過高的情況

#### 1.0.2

- 行動裝置上的觸控操作偶爾會有延遲
- 某些舊版瀏覽器可能不支援所有功能

#### 1.0.1

- 農曆日期轉換在閏年可能有微小誤差
- 批次操作時的進度顯示不夠準確

### 棄用功能

#### 計劃在 2.0.0 移除

- 舊版 API 端點 `/api/legacy/*`
- Internet Explorer 11 支援
- 舊版資料匯入格式

#### 計劃在 1.1.0 棄用

- 目前無計劃棄用的功能

### 安全性更新

#### 1.0.3

- 更新 Newtonsoft.Json 到 13.0.3 版本
- 修復潛在的 XSS 漏洞
- 加強輸入驗證機制

#### 1.0.2

- 更新 Microsoft.AspNetCore 相關套件
- 修復 SQL 注入的潛在風險
- 改善認證機制的安全性

#### 1.0.1

- 初始安全性檢查和修復
- 實作基本的輸入驗證
- 加入 HTTPS 強制重導向

### 效能改善

#### 1.0.3

- 資料庫查詢最佳化，平均回應時間減少 30%
- 前端打包大小減少 15%
- 記憶體使用量最佳化

#### 1.0.2

- API 快取機制實作
- 前端元件懶載入
- 資料庫索引最佳化

#### 1.0.1

- 初始效能基準建立
- 基本查詢最佳化
- 前端資源壓縮

### 貢獻者

感謝以下貢獻者對本專案的貢獻：

- **開發團隊**: 中興工程顧問社 IT 部門
- **測試團隊**: 品質保證部門
- **使用者回饋**: 各部門同仁

### 支援資訊

- **技術支援**: <<EMAIL>>
- **問題回報**: 請使用 GitHub Issues
- **功能建議**: 請聯絡開發團隊

---

**注意**: 本變更日誌僅記錄重要的變更。完整的變更記錄請參考 Git 提交歷史。
