# 中興社工作日曆專案開發指南

## 專案概述

中興工程顧問社內部專用的工作日曆系統，基於 .NET 8.0 C# 與 Vue 3.2 開發，採用前後端分離架構。

### 主要功能

- 記錄工作日類型，用來判斷加班的時數與倍率
- 具有班別，可以有多種班別
- 班別中週間每日的工作時數可不同
- 每天的資料都儲存在 MS SQL Server 資料庫裡
- 支援農曆節日自動轉換為陽曆日期
- 支援彈性上下班時間設定
- 員工個人排班管理
- 工作時數自動計算與加班倍率

### 技術架構

- **認證方式**: Windows Authentication (無需額外登入)
- **部署環境**: 企業內部 IIS 伺服器
- **資料存取**: Entity Framework Core 8.0 + 傳統 DTO 模式並存

#### 前端 (Vue 3.2 + TypeScript)

- **目錄**: WorkdayCalendar/ClientApp
- **建置工具**: npm, Vite
- **UI 框架**: PrimeVue
- **狀態管理**: Pinia
- **路由**: Vue Router
- **開發伺服器**: Vite Development Server

#### 後端 (.NET 8.0)

- **目錄**: WorkdayCalendar
- **框架**: ASP.NET Core Web API
- **資料庫**: MS SQL Server
- **ORM**: Entity Framework Core 8.0
- **API 文件**: Swagger/OpenAPI

#### 測試

- **目錄**: TestWorkdayCalendar
- **框架**: xUnit, FakeItEasy, Moq

## 重要檔案與目錄

### 後端架構 (WorkdayCalendar/)

- **App_Code/**: 核心業務邏輯與資料存取層 (傳統 DAO/DTO 模式)
- **Controllers/**: Web API 控制器
- **Data/**: Entity Framework Core DbContext 和資料存取層
- **DTO/**: 資料傳輸物件 (傳統模式)
- **Models/**: 資料模型 (EF Core 實體)
- **Program.cs**: 應用程式進入點
- **Startup.cs**: 服務設定與中介軟體配置

### 前端架構 (ClientApp/)

- **src/components/**: Vue 元件
- **src/pages/**: 頁面元件
- **src/router/**: 路由設定
- **src/store/**: 狀態管理
- **src/assets/**: 靜態資源
- **main.ts**: Vue 應用程式進入點
- **App.vue**: 根元件

### 資料庫結構

主要資料表包括：

- **WorkShift**: 班別基本資訊，包含彈性工時設定
- **ShiftWeekday**: 每週工作日的班別設定，支援不同工作時數
- **Workday**: 具體日期的工作安排
- **Holiday**: 假日資訊，支援農曆/陽曆標記
- **EmpWorkday**: 員工特定日期的工作安排
- **EmpWorkShift**: 員工的班別分配
- **Users**: 使用者資訊和權限管理
- **EventLog**: 系統操作日誌
- **EventType**: 事件類型定義

## 開發規範

### 程式碼風格

- 程式都需要有詳細的繁體中文註解
- function 的宣告要說明
- 程式的參數要說明
- 程式內部 Logic 要說明
- Vue 必須使用 Composition API, script setup lang="ts"

### 命名規範

- 類別名稱: PascalCase (如 WorkdayController)
- 方法名稱: PascalCase (如 GetWorkdays)
- 變數名稱: camelCase (如 workdayList)
- 常數名稱: UPPER_CASE (如 MAX_WORK_HOURS)
- 檔案名稱: 與類別名稱一致，使用 PascalCase

### API 設計原則

- RESTful API 設計
- 使用適當的 HTTP 方法 (GET, POST, PUT, DELETE)
- 回傳適當的 HTTP 狀態碼
- 使用 DTO 進行資料傳輸

## 技術細節與最佳實務

### 後端開發

#### 控制器設計

- 所有控制器都需要 `[Authorize]` 屬性進行身分驗證
- 使用 `[ApiController]` 和 `[Route("/api/[controller]/[action]")]` 屬性
- 控制器建構函式注入 `IConfiguration` 來存取設定
- 使用靜態 DTO 物件進行資料存取

#### 資料存取層

- **雙重模式**: Entity Framework Core 8.0 + 傳統 DTO 模式並存
- **EF Core**: 使用 WorkdayDbContext 進行現代化資料存取
- **傳統 DTO**: 所有 DTO 類別都接受連線字串參數
- **SqlUtil**: 傳統資料庫操作工具類別
- **DAO 模式**: 處理員工和職務資料的資料存取物件

#### 身分驗證

- 使用 Windows Authentication (Negotiate)
- 整合 IIS 身分驗證
- 無需額外登入/登出功能

### 前端開發

#### Vue 3 Composition API

- 必須使用 `<script setup lang="ts">` 語法
- 使用 TypeScript 進行型別檢查
- 採用 PrimeVue 作為 UI 元件庫

#### 建置工具

- 使用 Vite 作為建置工具
- 開發環境支援熱重載
- 生產環境建置至 `ClientApp/dist` 目錄

#### 狀態管理

- 使用 Pinia 進行狀態管理
- 支援 Vuex (向後相容)

### 測試策略

#### 單元測試

- 使用 xUnit 測試框架
- 使用 FakeItEasy 和 Moq 進行模擬測試
- 測試類別標記 `[ExcludeFromCodeCoverage]`
- 重點測試農曆轉陽曆功能和假日計算

### 資料庫設計

#### 核心資料表

- **WorkShift**: 班別基本資訊，包含彈性上下班時間
- **ShiftWeekday**: 每週各日的班別設定，支援不同工作時數
- **Workday**: 具體日期的工作安排
- **Holiday**: 假日資訊，支援農曆/陽曆標記
- **EmpWorkday**: 員工特定日期的個別工作安排
- **EmpWorkShift**: 員工班別分配
- **Users**: 使用者權限管理 (IsAdmin 欄位)

#### 特殊功能

- 農曆節日自動轉換為陽曆日期
- 彈性上下班時間設定
- 工作時數計算與加班倍率

## 常見開發任務

### 新增班別

1. 在 WorkShift 資料表中新增記錄
2. 在 ShiftWeekday 資料表中設定每週工作日的班別設定
3. 更新前端班別選擇器

### 新增假日

1. 在 Holiday 資料表中新增記錄
2. 設定是否為農曆日期 (IsLunar 欄位)
3. 設定是否為休假日 (DayOff 欄位)

### 新增員工排班

1. 在 EmpWorkShift 資料表中設定員工的班別
2. 如需特殊安排，在 EmpWorkday 資料表中設定特定日期的工作安排

### 開發環境設定

#### 後端

1. 確保安裝 .NET 8.0 SDK
2. 設定 SQL Server 連線字串
3. 執行 `CreateDb.sql` 建立資料庫結構

#### 前端

1. 確保安裝 Node.js
2. 在 `ClientApp` 目錄執行 `npm install`
3. 開發時執行 `npm run dev`
4. 建置時執行 `npm run build`

### 部署注意事項

- 目標環境: IIS 伺服器
- 需要設定 Windows Authentication
- 前端建置檔案位於 `ClientApp/dist`
- 設定適當的連線字串

## 重要開發注意事項

### 資料存取模式選擇

- **新功能開發**: 優先使用 Entity Framework Core (WorkdayDbContext)
- **現有功能維護**: 保持使用傳統 DTO 模式以確保相容性
- **混合使用**: 可在同一個控制器中同時使用兩種模式

### 命名空間一致性

- 所有 Models 類別必須在 `WorkdayCalendar.Models` 命名空間
- DbContext 在 `WorkdayCalendar.Data` 命名空間
- 控制器在 `WorkdayCalendar.Controllers` 命名空間

### 設定檔管理

- 使用 `Configuration.GetConnectionString("Workday")` 取得連線字串
- 避免在 appsettings.json 中存放明文密碼
- 生產環境使用環境變數或 Azure Key Vault

### Entity Framework Core 最佳實務

- 使用 `AsNoTracking()` 提升查詢效能
- 適當使用 `Include()` 避免 N+1 查詢問題
- 在 DbContext 中正確設定實體關聯和約束

## 參考文件

### 核心文件

- **詳細檔案結構**: Structure.md
- **系統架構分析與 UML 圖表**: Architecture_UML_Analysis.md
- **專案主要說明**: README.md
- **版本變更記錄**: ChangeLog.md

### 開發文件 (docs/)

- **開發指南**: docs/DEVELOPMENT.md
- **API 文件**: docs/API.md
- **部署指南**: docs/DEPLOYMENT.md
- **故障排除**: docs/TROUBLESHOOTING.md
- **貢獻指南**: docs/CONTRIBUTING.md
- **文件中心**: docs/README.md

## 從 .cursor/rules 遷移的內容

### 程式碼風格指南

- 程式都需要有詳細的繁體中文註解
- function 的宣告要說明
- 程式的參數要說明
- 程式內部 Logic 要說明
- Vue 必須使用 Composition API, script setup lang="ts"

### 產品需求文件重點

- 中興工程顧問社 (簡稱 中興社) 內部專用系統
- 基於 .NET 8.0 C# 與 Vue 3.2
- Windows Authentication，無需額外登入與登出
- 全部都在企業內部 IIS 執行
- 前後端分離架構
- 支援 Swagger/OpenAPI 文件
- 整合 Entity Framework Core 8.0

## 最新技術更新 (2024)

### Entity Framework Core 整合

- 新增 `WorkdayDbContext` 類別用於現代化資料存取
- 支援 Code First 模式
- 設定完整的實體關聯和約束
- 與傳統 DTO 模式並存，確保向後相容

### 開發工具改善

- 整合 Swagger UI 用於 API 測試和文件
- 支援 Vite 開發伺服器熱重載
- 完整的 TypeScript 支援

### 文件系統

- 建立完整的 docs/ 目錄結構
- 提供開發、部署、API、故障排除等完整文件
- 支援多角色文件導覽 (開發者、管理員、專案經理)

### 安全性增強

- 改善連線字串管理
- 加強輸入驗證
- 更新相依套件至最新安全版本

## 使用 time MCP Server 取得目前時間

## use context7 取得document
