
# 中興社 企業內部工作日曆

## 中興工程顧問社 (簡稱 中興社) 內部專用

## Always reply in zh-TW.

## Generate git commit messages in zh-TW.

## 主要功能

1. 記錄工作日類型，用來判斷加班的時數與倍率
2. 具有班別，可以有多種班別
3. 班別中週間每日的工作時數可不同
4. 每天的資料都儲存在MS SQLServer 資料庫裏


## 程式架構

- 基於 Dotnet core 8.0 C# 與 Vue3.2 
- Windows Authentication
- 不需要額外的登入與登出，全部都在企業內部IIS執行
- 前後端分離

  ### Frontend
    - Folder: WorkdayCalendar/ClientApp
    - Vue 3.2 + Vite + TypeScript
    - Build tool: npm

  ### Backend
    - Folder: WorkdayCalendar
    - Dotnet 8.0 C#
    - Asp.net Core Web API

  ### Testing
    - Folder: TestWorkdayCalendar
    - Framework: xUnit, FakeItEasy, Moq

## 檔案結構
 - 參考 Structure.md 

## 系統架構分析與 UML 圖表
 - 參考 Architecture_UML_Analysis.md


