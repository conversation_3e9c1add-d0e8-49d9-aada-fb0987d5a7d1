/// <reference types="vitest" />
// import topLevelAwait from "vite-plugin-top-level-await";
import serverOption from "./serverOption";
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";

export default defineConfig({
  server: serverOption,
  plugins: [
    // topLevelAwait({
    //   promiseExportName: "__tla",
    //   promiseImportName: (i) => `__tla_${i}`,
    // }),
    vue(),
  ],
  test: {
    globals: true,
    environment: "jsdom",
  },
});
