{"name": "workdaycalendar", "version": "1.0.3", "scripts": {"build": "vite build --mode production --base=/WorkdayCalendar/", "dev.debug": "vite --host --debug", "dev": "echo Starting the development server && vite --host", "serve": "vite --host", "preview": "vite preview", "test": "vitest", "coverage": "vitest run --coverage"}, "dependencies": {"@fontsource/noto-sans-tc": "^4.5.10", "@fullcalendar/bootstrap": "^5.6.0", "@fullcalendar/core": "^5.6.0", "@fullcalendar/daygrid": "^5.6.0", "@fullcalendar/interaction": "^5.6.0", "@fullcalendar/timegrid": "^5.6.0", "@popperjs/core": "^2.11.5", "axios": "^1.9.0", "bootstrap": "^5.3.6", "bootstrap-icons": "^1.13.1", "chart.js": "^3.7.0", "fullcalendar": "^5.6.0", "pinia": "^2.3.1", "popper": "^1.0.1", "primeflex": "^2.0.0", "primeicons": "^5.0.0", "primevue": "~3.8.2", "vue": "^3.5.16", "vue-router": "^4.5.1", "vue-select": "^4.0.0-beta.3", "vuex": "^4.1.0"}, "devDependencies": {"@testing-library/vue": "^6.6.1", "@vitejs/plugin-vue": "^3.0.2", "c8": "^7.14.0", "jsdom": "^20.0.3", "sass": "^1.89.1", "typescript": "^5.8.3", "vite": "^3.2.11", "vitest": "^0.21.1", "vue-tsc": "^3.0.1"}, "license": "BSD-3-<PERSON><PERSON>"}