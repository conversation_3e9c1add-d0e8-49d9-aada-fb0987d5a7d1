
# 工作日曆前端應用程式 (WorkCalendar Client)

中興社工作日曆系統的前端應用程式，使用 Vue 3.2 + TypeScript + Vite 開發。

## 技術架構

- **框架**: Vue 3.2 with Composition API
- **語言**: TypeScript
- **建置工具**: Vite
- **UI 框架**: PrimeVue + Bootstrap 5
- **狀態管理**: Pinia
- **路由**: Vue Router 4
- **HTTP 客戶端**: Axios
- **測試**: Vitest + Vue Testing Library

## 專案結構

```
src/
├── components/          # 可重用元件
│   ├── WorkShift.vue   # 班別管理元件
│   ├── ShiftWeekday.vue # 週間班別設定元件
│   ├── ChooseShift.vue # 班別選擇器
│   ├── ChooseUser.vue  # 使用者選擇器
│   ├── MyDropdown.vue  # 自定義下拉選單
│   └── NavMenu.vue     # 導航選單
├── pages/              # 頁面元件
│   ├── Workday.vue     # 工作日曆頁面
│   ├── Shifts.vue      # 班別管理頁面
│   └── StaffShift.vue  # 員工排班頁面
├── router/             # 路由設定
│   └── index.ts
├── store/              # 狀態管理
│   └── index.ts
├── types/              # TypeScript 型別定義
│   └── Employee.ts
├── assets/             # 靜態資源
└── components/api/     # API 服務
    └── index.ts
```

## 開發環境設定

### 系統需求

- Node.js 16.0 或更新版本
- npm 8.0 或更新版本

### 安裝相依套件

```bash
# 使用 npm
npm install

# 或使用 yarn (如果偏好)
npm install -g yarn
yarn install
```

### 開發指令

```bash
# 啟動開發伺服器 (支援熱重載)
npm run dev

# 建置生產版本
npm run build

# 預覽建置結果
npm run preview

# 執行測試
npm run test

# 執行測試並產生覆蓋率報告
npm run coverage
```

### 開發伺服器設定

開發伺服器預設在 `http://localhost:3000` 啟動，並會自動代理 API 請求到後端伺服器。

#### Vite 設定 (vite.config.ts)

```typescript
export default defineConfig({
  server: {
    host: true,
    port: 3000,
    proxy: {
      '/api': {
        target: 'https://localhost:5001',
        changeOrigin: true,
        secure: false
      }
    }
  },
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'jsdom'
  }
})
```

## 主要功能模組

### 1. 工作日曆管理 (Workday.vue)

- 顯示月曆檢視
- 新增/編輯/刪除工作日
- 批次設定工作日
- 假日標記和管理

### 2. 班別管理 (Shifts.vue)

- 班別基本資訊管理
- 彈性上下班時間設定
- 休息時間設定
- 班別複製和範本功能

### 3. 週間班別設定 (ShiftWeekday.vue)

- 每週各日的詳細時間設定
- 不同工作日的工時設定
- 特殊日期的例外處理

### 4. 員工排班 (StaffShift.vue)

- 員工班別分配
- 個人排班檢視
- 排班衝突檢查

## 元件開發指南

### Vue 3 Composition API 規範

所有元件必須使用 Composition API 和 `<script setup lang="ts">` 語法：

```vue
<template>
  <div class="my-component">
    <h2>{{ title }}</h2>
    <button @click="handleClick">{{ buttonText }}</button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// Props 定義
interface Props {
  title: string
  initialValue?: number
}

const props = withDefaults(defineProps<Props>(), {
  initialValue: 0
})

// Emits 定義
interface Emits {
  (e: 'update', value: number): void
  (e: 'save'): void
}

const emit = defineEmits<Emits>()

// 響應式資料
const count = ref(props.initialValue)

// 計算屬性
const buttonText = computed(() => `點擊次數: ${count.value}`)

// 方法
function handleClick() {
  count.value++
  emit('update', count.value)
}

// 生命週期
onMounted(() => {
  console.log('元件已掛載')
})
</script>

<style scoped>
.my-component {
  padding: 1rem;
}
</style>
```

### TypeScript 型別定義

```typescript
// types/Workday.ts
export interface Workday {
  shiftId: number
  workDate: string
  dayType: number
  arrivalTime: string
  departureTime: string
  workHours: number
  comment?: string
}

export interface WorkShift {
  shiftId: number
  shiftName: string
  workdays: string
  arrivalTime: string
  departureTime: string
  flexibleArrivalBefore: string
  flexibleArrivalAfter: string
  flexibleDepartureBefore: string
  flexibleDepartureAfter: string
  middayBreakStart: string
  middayBreakEnd: string
  comment?: string
}
```

### API 服務

```typescript
// components/api/index.ts
import axios from 'axios'

const api = axios.create({
  baseURL: '/api',
  withCredentials: true
})

export const workdayApi = {
  getAll: (year?: number, month?: number) => 
    api.get('/Workday/GetAll', { params: { year, month } }),
  
  add: (workday: Workday) => 
    api.post('/Workday/Add', workday),
  
  update: (workday: Workday) => 
    api.put('/Workday/Update', workday),
  
  delete: (shiftId: number, workDate: string) => 
    api.delete('/Workday/Delete', { params: { shiftId, workDate } })
}
```

## 測試

### 單元測試

使用 Vitest 和 Vue Testing Library 進行元件測試：

```typescript
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import WorkShift from '@/components/WorkShift.vue'

describe('WorkShift.vue', () => {
  it('renders shift information correctly', () => {
    const wrapper = mount(WorkShift, {
      props: {
        shift: {
          shiftId: 1,
          shiftName: '正常班',
          arrivalTime: '08:00',
          departureTime: '17:00'
        }
      }
    })
    
    expect(wrapper.text()).toContain('正常班')
    expect(wrapper.text()).toContain('08:00')
    expect(wrapper.text()).toContain('17:00')
  })
})
```

### 執行測試

```bash
# 執行所有測試
npm run test

# 監視模式執行測試
npm run test -- --watch

# 產生覆蓋率報告
npm run coverage
```

## 建置與部署

### 開發建置

```bash
npm run build
```

建置檔案會輸出到 `dist/` 目錄。

### 生產環境設定

建置時會根據環境變數使用不同的設定：

```bash
# 開發環境
npm run build -- --mode development

# 生產環境
npm run build -- --mode production
```

### 環境變數

```bash
# .env.development
VITE_API_BASE_URL=https://localhost:5001/api

# .env.production  
VITE_API_BASE_URL=https://workday.sinotech.org.tw/api
```

## 程式碼品質

### ESLint 設定

專案使用 ESLint 進行程式碼檢查：

```bash
# 檢查程式碼
npm run lint

# 自動修復可修復的問題
npm run lint -- --fix
```

### Prettier 設定

使用 Prettier 進行程式碼格式化：

```javascript
// .prettierrc.js
module.exports = {
  semi: false,
  singleQuote: true,
  tabWidth: 2,
  trailingComma: 'es5'
}
```

## 效能最佳化

### 1. 元件懶載入

```typescript
// router/index.ts
const routes = [
  {
    path: '/workday',
    component: () => import('@/pages/Workday.vue')
  }
]
```

### 2. 圖片最佳化

```vue
<template>
  <!-- 使用適當的圖片格式和大小 -->
  <img 
    src="@/assets/logo.webp" 
    alt="Logo" 
    loading="lazy"
    width="64" 
    height="64"
  />
</template>
```

### 3. API 請求最佳化

```typescript
// 使用防抖減少 API 呼叫
import { debounce } from 'lodash-es'

const searchWorkdays = debounce(async (keyword: string) => {
  const result = await workdayApi.search(keyword)
  workdays.value = result.data
}, 300)
```

## 故障排除

### 常見問題

1. **模組找不到錯誤**
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **型別錯誤**
   - 檢查 TypeScript 設定
   - 確認型別定義檔案

3. **建置失敗**
   - 檢查 Vite 設定
   - 確認相依套件版本相容性

### 除錯技巧

1. **使用 Vue DevTools**
   - 安裝瀏覽器擴充套件
   - 檢視元件狀態和 props

2. **網路請求除錯**
   ```typescript
   // 在 main.ts 中加入請求攔截器
   axios.interceptors.request.use(config => {
     console.log('API Request:', config)
     return config
   })
   ```

## 相關資源

- [Vue 3 官方文件](https://vuejs.org/)
- [TypeScript 手冊](https://www.typescriptlang.org/docs/)
- [Vite 官方文件](https://vitejs.dev/)
- [PrimeVue 元件庫](https://primefaces.org/primevue/)
- [Pinia 狀態管理](https://pinia.vuejs.org/)

## 貢獻指南

1. Fork 專案
2. 建立功能分支
3. 遵循程式碼規範
4. 撰寫測試
5. 提交 Pull Request

## 授權

本專案採用 BSD-3-Clause 授權條款。

