<template>
  <div>    
    <div style="display: inline-block; white-space: nowrap">
      <img alt="行事曆 logo" src="./assets/calendar.png" style="height: 44px; margin-top: 24px; float: left" />
      <h1>行事曆維護作業</h1>
    </div>
    <div class="app-container">
      <TabView>
        <TabPanel>
          <template #header>
            <em class="pi pi-calendar red"></em> &nbsp;
            <span>行事曆</span>
          </template>
          <Workday :year="Year" @updateYear="changeYear" />
        </TabPanel>
        <TabPanel v-if="isAdmin">
          <template #header>
            <em class="pi pi-cog green"></em> &nbsp;
            <span>參數設定</span>
          </template>
          <WorkShift />
          <div><br />
            <hr /><br />
          </div>
          <ShiftWeekday :year="Year" @updateYear="changeYear" />
        </TabPanel>
        <TabPanel v-if="isAdmin">
          <template #header>
            <em class="pi pi-users blue"></em> &nbsp;
            <span>員工班別設定</span>
          </template>
          <StaffShift />
          <div><br />
            <hr /><br />
          </div>
        </TabPanel>
      </TabView>
    </div>
    <ConfirmDialog></ConfirmDialog>
    <Toast />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import ConfirmDialog from "primevue/confirmdialog";
import Toast from "primevue/toast";
import TabView from "primevue/tabview";
import TabPanel from "primevue/tabpanel";
import ShiftWeekday from "./pages/ShiftWeekday.vue";
import Workday from "./pages/Workday.vue";
import WorkShift from "./pages/WorkShift.vue";
import StaffShift from "./pages/StaffShift.vue";
import NavMenu from './components/NavMenu.vue'
import axios from "axios";
import { userIsAdminUrl } from "./components/api";
// import { storeToRefs } from 'pinia';
import { useAuthUserStore } from './store/index';

const isAdmin = ref(false);
const Year = ref(new Date().getFullYear());
function changeYear(year: number) {
  Year.value = year;
}

onMounted(async (): Promise<void> => {
  // this.calendarService.getCalendarsSmall().then(data => this.shifts = data);
  isAdmin.value = false;
  axios.get(userIsAdminUrl).then((response) => {
    isAdmin.value = response.data as boolean;
  });
});


</script>

<style lang="scss">
#app {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  margin: 0px 5px 5px 5px;
}

.app-container {
  text-align: center;
}

body #app .p-button {
  margin-left: 0.2em;
}

form {
  margin-top: 2em;
}

.red {
  color: red;
}

.blue {
  color: blue;
}

.green {
  color: green;
}

.orange {
  color: orange;
}

.norwap {
  white-space: nowrap;
}
</style>
