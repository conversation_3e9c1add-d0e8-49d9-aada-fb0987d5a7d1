// 導入 Vue Router 相關功能
// createWebHistory: 創建基於 HTML5 History API 的路由歷史記錄
// createRouter: 創建路由器實例
// NavigationFailureType: 導航失敗類型枚舉
// isNavigationFailure: 檢查是否為導航失敗的工具函數
import {
  createWebHistory,
  createRouter,
  NavigationFailureType,
  isNavigationFailure,
} from "vue-router";

// 導入認證用戶狀態管理 Store
import { useAuthUserStore } from "../store/index";

// 忽略 TypeScript 類型檢查，因為 import.meta.env 可能在某些環境下無法正確識別
// @ts-ignore
// 獲取應用程式的基礎 URL，通常用於部署在子目錄時
const publicURL = import.meta.env.VITE_APP_BASE_URL;
// 備用的根路徑設定（已註解）
//const publicURL = '/';

// 使用動態導入並加上 chunk 名稱來優化打包
// 這樣可以實現代碼分割，減少初始加載時間，提升應用程式效能

// 工作日管理頁面組件
const Workday = () => import(/* webpackChunkName: "workday" */ "../pages/Workday.vue");
// 工作班次管理頁面組件（已註解，可能暫時不使用）
// const WorkShift = () => import(/* webpackChunkName: "workshift" */ "../pages/WorkShift.vue");
// 班次設定頁面組件
const Shifts = () => import(/* webpackChunkName: "shifts" */ "../pages/Shifts.vue");
// 員工個別化班別管理頁面組件
const EmpShifts = () => import(/* webpackChunkName: "empshifts" */ "../pages/EmpShifts.vue");
// 員工個別化工作日管理頁面組件
const EmpWorkday = () => import(/* webpackChunkName: "empworkday" */ "../pages/EmpWorkday.vue");
// 員工個別化工作日查詢頁面組件
const EmpWorkdayQuery = () => import(/* webpackChunkName: "empworkdayquery" */ "../pages/EmpWorkdayQuery.vue");

// 定義路由配置陣列
// 每個路由物件包含 path（路徑）、name（名稱）、component（對應的組件）
const routes = [
  {
    // 根路徑，當使用者訪問網站首頁時會導向到工作日管理頁面
    path: `${publicURL}`,
    //base: `${process.env.BASE_URL}`, // 加上這行（已註解的備用設定）
    name: "Index",
    component: Workday,
  },
  {
    // 工作日管理頁面路由
    path: `${publicURL}Workday`,
    name: "Workday",
    component: Workday,
  },
  {
    // 班別設定頁面路由
    path: `${publicURL}Shifts`,
    name: "Shifts",
    component: Shifts,
  },
  // {
  //   // 員工個別化班別管理頁面路由
  //   path: `${publicURL}EmpShifts`,
  //   name: "EmpShifts",
  //   component: EmpShifts,
  // },
    {
    // 員工個別化工作日查詢頁面路由
    path: `${publicURL}EmpWorkdayQuery`,
    name: "EmpWorkdayQuery",
    component: EmpWorkdayQuery,
  },
  {
    // 員工個別化工作日管理頁面路由
    path: `${publicURL}EmpWorkday`,
    name: "EmpWorkday",
    component: EmpWorkday,
  }
];

// 創建 Vue Router 實例
// 使用 HTML5 History 模式，提供乾淨的 URL（沒有 # 符號）
const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 以下是已註解的使用者驗證邏輯
// 可能用於在路由初始化時檢查使用者是否為管理員
// try {
//   const userStore = useAuthUserStore();
//    //await userStore.checkIsAdmin();
//   userStore.checkIsAdmin();
// } catch (err: unknown) {
//   console.log(`userStore.checkIsAdmin() 發生錯誤: ${err}`);
// }

// 設定路由錯誤處理器
// 當動態導入的模組載入失敗時（通常是因為檔案更新或網路問題），
// 會重新載入當前頁面以嘗試解決問題
router.onError((error: any, to: any, from: any) => {
  if (error.message.includes("Failed to fetch dynamically imported module")) {
    window.location = to.fullPath;
  }
});

// 定義路由導航的輔助函數
// 這個函數提供了更安全的路由跳轉方式，包含錯誤處理
const pushHandler = (routerName: string) => {
  router
    .push({
      name: routerName, // 使用路由名稱進行導航
    })
    .then((failure) => {
      // 檢查導航是否失敗
      if (failure instanceof Error) {
        // 如果是因為導航被取消而失敗，記錄錯誤
        if (isNavigationFailure(failure, NavigationFailureType.cancelled)) {
          console.error(failure);
        }
      }
    })
    .catch((err: Error): void => {
      // 捕獲任何其他導航錯誤並記錄
      console.error(err);
    });
};

// 擴展路由器物件，添加自定義的 pushHandler 方法
// 這樣可以在應用程式中使用更安全的路由導航方式
let routerExtend = Object.assign(router, { pushHandler });

// 導出擴展後的路由器
export { routerExtend };

// 導出原始的路由器實例作為預設導出
export default router;
