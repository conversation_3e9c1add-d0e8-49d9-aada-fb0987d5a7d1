<template>
  <div>
    <!-- <div style="display: inline-block; white-space: nowrap">
      <img alt="行事曆維護作業" src="./assets/calendar.png" style="height: 44px; margin-top: 24px; float: left" />
      <h1>行事曆維護作業</h1>
    </div> -->
    <div class="app-container">
      <NavMenu v-if="useAuthUserStore().isAdmin" />
      <router-view />
    </div>
    <ConfirmDialog></ConfirmDialog>
    <Toast />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import ConfirmDialog from "primevue/confirmdialog";
import Toast from "primevue/toast";
import NavMenu from './components/NavMenu.vue'
import { useAuthUserStore } from './store/index';

onMounted(async (): Promise<void> => {
  const userStore = useAuthUserStore();
  // this.calendarService.getCalendarsSmall().then(data => this.shifts = data);

  if(userStore.logonUser == "")
  {
    userStore.getUserId();
    userStore.checkIsAdmin();
  }

});

</script>

<style lang="scss">
#app {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  margin: 0px 5px 5px 5px;
}

.app-container {
  text-align: center;
}

body #app .p-button {
  margin-left: 0.2em;
}

form {
  margin-top: 2em;
}

.red {
  color: red;
}

.blue {
  color: blue;
}

.green {
  color: green;
}

.orange {
  color: orange;
}

.norwap {
  white-space: nowrap;
}
</style>
