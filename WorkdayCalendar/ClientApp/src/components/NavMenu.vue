<template>
  <Menubar :model="menuItems" v-if="userStore.isAdmin">
    <template #start>
      <router-link to="/"><img alt="行事曆維護作業" src="../assets/calendar.png"
          style="height: 44px; margin-top: 24px; float: left" class="mr-2" /></router-link>
      <h1 class="nowrap">行事曆維護作業 &nbsp; &nbsp; &nbsp;</h1>
    </template>
    <template #end>
      <div class="">
      </div>
    </template>
  </Menubar>
</template>

<script setup lang="ts">
// 引入必要的 Vue 組件和功能
import { ref, onMounted } from 'vue';
// 引入使用者驗證相關的存儲庫
import { useAuthUserStore } from '../store/index';
// 引入 PrimeVue 的選單組件
import Menubar from 'primevue/menubar';

// 初始化使用者驗證存儲庫實例
const userStore = useAuthUserStore();
// 獲取應用程式的基礎 URL，用於路由導航
const publicURL = import.meta.env.VITE_APP_BASE_URL;

// 定義導航選單項目
// 使用 ref 來建立響應式的選單項目陣列
const menuItems = ref([
  {
    label: '行事曆', // 選單項目顯示名稱
    to: `${publicURL}Workday`, // 導航目標路徑
    icon: 'pi pi-calendar' // PrimeVue 日曆圖示
  },
  {
    label: '班別設定', // 管理各種班別的設定頁面
    icon: 'pi pi-cog', // 設定齒輪圖示
    to: `${publicURL}Shifts`
  },
  // {
  //   label: '員工班別設定', // 設定員工的班別分配
  //   icon: 'pi pi-user-edit', // 使用者編輯圖示
  //   to: `${publicURL}EmpShifts`
  // },
  {
    label: '員工行事曆設定', // 查看員工的行事曆
    icon: 'pi pi-calendar-plus red', // 帶有紅色的日曆新增圖示
    to: `${publicURL}EmpWorkday`
  },
  {
    label: '員工行事曆查詢', // 查看員工的特殊行事曆
    icon: 'pi pi-user-edit', // 使用者編輯圖示
    to: `${publicURL}EmpWorkdayQuery`
  }
]);

// 組件掛載時執行的生命週期鉤子
onMounted(() => {
  // 目前沒有初始化邏輯
});
</script>

<style lang="scss" scoped>
.red {
  color: red;
}

.nowrap {
  white-space: nowrap;
}

.p-menubar {
  border: none;
}

:deep(.p-overlay-badge .p-badge) {
  opacity: 0.75;
}
</style>