<template>
    <div class="p-field p-grid" style="align-items: center; ">
        <label for="shiftDropdown" class="p-col-fixed"
            style="width: 100px; display: flex; align-items: center; margin-top:5px;">班別名稱：</label>
        <div class=" p-col-fixed" style="display: flex; align-items: center;">
            <Dropdown id="shiftDropdown" v-model="ShiftId" :options="shifts" optionValue="ShiftId" @change="shiftChange"
                optionLabel="ShiftName" placeholder="請選擇 班別" :loading="isLoading" />
        </div>
    </div>
</template>
<script setup lang="ts">
// 引入 Vue 3 Composition API 相關函數
import { ref, Ref, onMounted, watch } from "vue";
// 引入 PrimeVue 下拉選單元件
import Dropdown from "primevue/dropdown";
// 引入 HTTP 請求函式庫
import axios from "axios";
// 引入 API 端點 URL 常數
import { workShiftGetUrl } from "./api";

// 定義班別資料介面
interface WorkShift {
    ShiftId: number;
    ShiftName: string;
    // 可根據實際 API 回傳結構添加其他屬性
}

// 定義 Props 介面
interface Props {
    modelValue?: number | null; // v-model 綁定的值（班別 ID）
}

// 定義 Props 並設定預設值
const props = withDefaults(defineProps<Props>(), {
    modelValue: null,
});

// 定義響應式變數
const shifts: Ref<WorkShift[]> = ref([]); // 儲存所有班別資料的陣列
const ShiftId: Ref<number | null> = ref(props.modelValue); // 目前選中的班別 ID
const ShiftName: Ref<string> = ref(""); // 目前選中的班別名稱
const currentShift: Ref<WorkShift | null> = ref(null); // 目前選中的完整班別物件
const isLoading: Ref<boolean> = ref(false); // 載入狀態
const errorMessage: Ref<string> = ref(""); // 錯誤訊息

// 定義 emit 事件
const emit = defineEmits<{
    'update:modelValue': [value: number | null];
    shiftChanged: [shift: WorkShift | null];
}>();

// 定義 expose 給父組件的屬性
defineExpose({
    currentShift,
    ShiftId,
    ShiftName
});

/**
 * 從伺服器取得所有班別資料
 * 使用 axios 發送 GET 請求到工作班別 API 端點，包含錯誤處理
 */
async function getShifts() {
    try {
        isLoading.value = true;
        errorMessage.value = "";

        const response = await axios.get(workShiftGetUrl);
        shifts.value = response.data;

        // 如果有班別資料且沒有預設值，則預設選擇第一個班別
        if (shifts.value && shifts.value.length > 0) {
            // 如果有 modelValue，優先使用 modelValue
            if (props.modelValue !== null) {
                const selectedShift = shifts.value.find(shift => shift.ShiftId === props.modelValue);
                if (selectedShift) {
                    ShiftId.value = selectedShift.ShiftId;
                    ShiftName.value = selectedShift.ShiftName;
                    currentShift.value = selectedShift;
                } else {
                    // 如果找不到對應的班別，使用第一個
                    ShiftId.value = shifts.value[0].ShiftId;
                    ShiftName.value = shifts.value[0].ShiftName;
                    currentShift.value = shifts.value[0];
                    // 更新父組件的值
                    emit('update:modelValue', ShiftId.value);
                }
            } else {
                // 沒有 modelValue 時，預設選擇第一個班別
                ShiftId.value = shifts.value[0].ShiftId;
                ShiftName.value = shifts.value[0].ShiftName;
                currentShift.value = shifts.value[0];
                // 更新父組件的值
                emit('update:modelValue', ShiftId.value);
            }
            // 發送事件通知父組件
            emit('shiftChanged', currentShift.value);
        } else {
            // 如果沒有班別資料，重置所有值
            ShiftId.value = null;
            ShiftName.value = "";
            currentShift.value = null;
            emit('update:modelValue', null);
            emit('shiftChanged', null);
        }
    } catch (error) {
        console.error('取得班別資料失敗:', error);
        errorMessage.value = "無法載入班別資料，請稍後再試";
        // 重置資料
        shifts.value = [];
        ShiftId.value = null;
        ShiftName.value = "";
        currentShift.value = null;
        emit('shiftChanged', null);
    } finally {
        isLoading.value = false;
    }
}

/**
 * 處理班別下拉選單變更事件
 * 當使用者選擇不同班別時，更新相關的響應式變數
 * 使用 find 方法提高效率
 */
function shiftChange() {
    if (ShiftId.value === null) {
        ShiftName.value = "";
        currentShift.value = null;
        emit('update:modelValue', null);
        emit('shiftChanged', null);
        return;
    }

    // 使用 find 方法找到選中的班別，比迴圈更有效率
    const selectedShift = shifts.value.find(shift => shift.ShiftId === ShiftId.value);

    if (selectedShift) {
        ShiftName.value = selectedShift.ShiftName;
        currentShift.value = selectedShift;
        // 更新父組件的 v-model 值
        emit('update:modelValue', ShiftId.value);
        // 發送事件通知父組件班別已變更
        emit('shiftChanged', selectedShift);
    }
}

// 監聽 ShiftId 的變化，自動更新相關資料
watch(ShiftId, () => {
    shiftChange();
}, { immediate: false });

// 監聽 props.modelValue 的變化，同步更新內部狀態
watch(() => props.modelValue, (newValue) => {
    if (newValue !== ShiftId.value) {
        ShiftId.value = newValue;
        // 如果有新值且班別資料已載入，更新相關資訊
        if (newValue !== null && shifts.value.length > 0) {
            const selectedShift = shifts.value.find(shift => shift.ShiftId === newValue);
            if (selectedShift) {
                ShiftName.value = selectedShift.ShiftName;
                currentShift.value = selectedShift;
                emit('shiftChanged', selectedShift);
            }
        } else if (newValue === null) {
            ShiftName.value = "";
            currentShift.value = null;
            emit('shiftChanged', null);
        }
    }
}, { immediate: true });

// 元件掛載時執行的生命週期鉤子
onMounted(() => {
    getShifts(); // 載入班別資料
});
</script>