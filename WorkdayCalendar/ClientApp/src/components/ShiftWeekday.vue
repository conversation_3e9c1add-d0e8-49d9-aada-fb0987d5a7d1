<template>
    <div><Button class="p-button-raised p-button-rounded" v-if="isAdmin" icon="pi pi-plus" iconPos="left" label="新增週間工時"
            v-on:click="showAddDialog" />
        <div>
            <DataTable :value="weekdays" stripedRows responsiveLayout="scroll" class="p-datatable-customers"
                dataKey="ShiftId" sortMode="multiple">
                <Column field="WeekDay" header="週間" :sortable="true">
                    <template #body="slotProps">
                        {{ toWeekDay((slotProps as any).data.WeekDay) }}
                    </template>
                </Column>
                <Column field="ShiftName" header="班別名稱"></Column>
                <Column field="FlexibleArrivalBefore" header="最早彈性上班時間"></Column>
                <Column field="ArrivalTime" :sortable="true" header="正常上班時間"></Column>
                <Column field="FlexibleArrivalAfter" header="最晚彈性上班時間"></Column>
                <Column field="MorningRestStart" header="前段休息開始時間"></Column>
                <Column field="MorningRestEnd" header="前段休息結束時間"></Column>
                <Column field="MiddayBreakStart" header="用餐休息開始時間"></Column>
                <Column field="MiddayBreakEnd" header="用餐休息結束時間"></Column>
                <Column field="AfternoonRestStart" header="後段休息開始時間"></Column>
                <Column field="AfternoonRestEnd" header="後段休息結束時間"></Column>
                <Column field="FlexibleDepartureBefore" header="最早彈性下班時間"></Column>
                <Column field="DepartureTime" header="正常下班時間"></Column>
                <Column field="FlexibleDepartureAfter" header="最晚彈性下班時間"></Column>
                <Column field="WorkHours" header="工時"></Column>
                <Column class="nowrap" :bodyStyle="{ 'text-align': 'center', 'overflow': 'visible' }">
                    <template #body="x">
                        <Button type="button" v-if="isAdmin" class="p-button-raised p-button-rounded" icon="pi pi-pencil"
                            iconPos="left" @click="showUpdateDialog(x.data.ShiftId, x.data.WeekDay)">
                        </Button>
                        <Button type="button" v-if="isAdmin" v-on:click="deleteWeekday(x.data.ShiftId, x.data.WeekDay)"
                            label="" alt="刪除" icon="pi pi-trash" iconPos="left"
                            class="p-button-danger p-button-raised p-button-rounded" />
                    </template>
                </Column>
            </DataTable>
        </div>
        <Dialog position="center" v-model:visible="dialogDisplay" v-bind:modal="true">
            <template #header>
                <h3>{{ header }}</h3>
            </template>

            <div class="p-fluid">
                <div class="p-field p-grid">
                    <label for="ShiftName" class="p-col-fixed" style="width: 220px">班別名稱：</label>
                    <div class="p-col-fixed" style="width: 300px">
                        <Dropdown v-model="ShiftId" v-if="addOrUpdate == '新增'" :options="shifts" optionValue="ShiftId"
                            v-on:change="shiftChange" optionLabel="ShiftName" placeholder="請選擇 班別" />
                        <label v-if="addOrUpdate !== '新增'">{{ ShiftName }}</label>
                    </div>
                </div>
                <div class="p-field p-grid">
                    <label for="FlexibleArrivalBefore" class="p-col-fixed" style="width: 220px">最早彈性上班時間(時:分)：</label>
                    <div class="p-col-fixed" style="width: 300px">
                        <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="FlexibleArrivalBefore" />
                    </div>
                </div>
                <div class="p-field p-grid">
                    <label for="ArrivalTime" class="p-col-fixed" style="width: 220px">正常上班時間(時:分)：</label>
                    <div class="p-col-fixed" style="width: 300px">
                        <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="ArrivalTime" />
                    </div>
                </div>
                <div class="p-field p-grid">
                    <label for="FlexibleArrivalAfter" class="p-col-fixed" style="width: 220px">最晚彈性上班時間(時:分)：</label>
                    <div class="p-col-fixed" style="width: 300px">
                        <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="FlexibleArrivalAfter" />
                    </div>
                </div>
                <div class="p-field p-grid">
                    <label for="MiddayBreakStart" class="p-col-fixed" style="width: 220px">用餐休息開始時間(時:分)：</label>
                    <div class="p-col-fixed" style="width: 300px">
                        <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="MiddayBreakStart" />
                    </div>
                </div>
                <div class="p-field p-grid">
                    <label for="MiddayBreakEnd" class="p-col-fixed" style="width: 220px">用餐休息結束時間(時:分)：</label>
                    <div class="p-col-fixed" style="width: 300px">
                        <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="MiddayBreakEnd" />
                    </div>
                </div>
                <div class="p-field p-grid">
                    <label for="MorningRestStart" class="p-col-fixed" style="width: 220px">前段休息開始時間(時:分)：</label>
                    <div class="p-col-fixed" style="width: 300px">
                        <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="MorningRestStart" />
                    </div>
                </div>
                <div class="p-field p-grid">
                    <label for="MorningRestEnd" class="p-col-fixed" style="width: 220px">前段休息結束時間(時:分)：</label>
                    <div class="p-col-fixed" style="width: 300px">
                        <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="MorningRestEnd" />
                    </div>
                </div>
                <div class="p-field p-grid">
                    <label for="AfternoonRestStart" class="p-col-fixed" style="width: 220px">後段休息開始時間(時:分)：</label>
                    <div class="p-col-fixed" style="width: 300px">
                        <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="AfternoonRestStart" />
                    </div>
                </div>
                <div class="p-field p-grid">
                    <label for="AfternoonRestEnd" class="p-col-fixed" style="width: 220px">後段休息結束時間(時:分)：</label>
                    <div class="p-col-fixed" style="width: 300px">
                        <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="AfternoonRestEnd" />
                    </div>
                </div>
                <div class="p-field p-grid">
                    <label for="FlexibleDepartureBefore" class="p-col-fixed" style="width: 220px">最早彈性下班時間(時:分)：</label>
                    <div class="p-col-fixed" style="width: 300px">
                        <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="FlexibleDepartureBefore" />
                    </div>
                </div>
                <div class="p-field p-grid">
                    <label for="DepartureTime" class="p-col-fixed" style="width: 220px">正常下班時間(時:分)：</label>
                    <div class="p-col-fixed" style="width: 300px">
                        <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="DepartureTime" />
                    </div>
                </div>
                <div class="p-field p-grid">
                    <label for="FlexibleDepartureAfter" class="p-col-fixed" style="width: 220px">最晚彈性下班時間(時:分)：</label>
                    <div class="p-col-fixed" style="width: 300px">
                        <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="FlexibleDepartureAfter" />
                    </div>
                </div>
                <div class="p-field p-grid" v-if="addOrUpdate == '新增'">
                    <label for="Workdays" class="p-col-fixed" style="width: 220px">工作日</label>
                    <div class="p-col-fixed" style="width: 300px">
                        <Checkbox name="Workdays" value="0" v-model="Workdays" />日
                        <Checkbox name="Workdays" value="1" v-model="Workdays" />一
                        <Checkbox name="Workdays" value="2" v-model="Workdays" />二
                        <Checkbox name="Workdays" value="3" v-model="Workdays" />三
                        <Checkbox name="Workdays" value="4" v-model="Workdays" />四
                        <Checkbox name="Workdays" value="5" v-model="Workdays" />五
                        <Checkbox name="Workdays" value="6" v-model="Workdays" />六
                    </div>
                </div>
                <!-- <div class="p-field p-grid"><label for="Workdays" class="p-col-fixed" style="width: 220px">休息日</label>
                    <div class="p-col-fixed" style="width: 300px">
                        <Dropdown v-model="RestDay" :options="WeekDays" optionLabel="name" placeholder="選擇休息日"
                            class="w-full md:w-14rem" />
                    </div>
                </div>
                <div class="p-field p-grid"><label for="Workdays" class="p-col-fixed" style="width: 220px">例假日</label>
                    <div class="p-col-fixed" style="width: 300px">
                        <Dropdown v-model="FixedDayOff" :options="WeekDays" optionLabel="name" placeholder="選擇例假日"
                            class="w-full md:w-14rem" />
                    </div>
                </div> -->
                <div class="p-field p-grid">
                    <label for="AfternoonRestStart" class="p-col-fixed" style="width: 220px">備註：</label>
                    <div class="p-col-fixed" style="width: 300px">
                        <InputText v-model="Comment" type="text" placeholder="備註" />
                    </div>
                </div>
            </div>
            <Button :label="addOrUpdate" @click="addUpdate" class="p-button-raised p-button-rounded" icon="pi pi-check"
                autofocus />
            &nbsp;&nbsp;
            <Button label="取消" v-on:click="cancelWeekday" class="p-button-raised p-button-danger p-button-rounded"
                icon="pi pi-times" />
        </Dialog>
        <Dialog position="center" v-model:visible="showWarning" v-bind:modal="true">
            <h3>{{ warningMessage }}</h3>
        </Dialog>
    </div>
</template>

<script setup lang="ts">
// === Vue 核心功能導入 ===
import { ref, onMounted, nextTick } from "vue";

// === PrimeVue 相關導入 ===
import { useToast } from "primevue/usetoast"; // 訊息提示功能
import { useConfirm } from "primevue/useconfirm"; // 確認對話框功能
import Checkbox from "primevue/checkbox"; // 核取方塊元件
import DataTable from "primevue/datatable"; // 資料表格元件
import Column from "primevue/column"; // 表格欄位元件
import Button from "primevue/button"; // 按鈕元件
import Dropdown from "primevue/dropdown"; // 下拉選單元件
import Calendar from "primevue/calendar"; // 日期時間選擇器元件
import Dialog from "primevue/dialog"; // 對話框元件
import InputText from "primevue/inputtext"; // 文字輸入框元件

// === 第三方套件導入 ===
import axios from "axios"; // HTTP 請求工具

// === 自定義模組導入 ===
import { workShiftGetUrl, weekdayGetUrl, weekdayAddUrl, weekdayUpdateUrl, weekdayDeleteUrl } from "../components/api" // API 端點設定
import { useAuthUserStore } from '../store/index'; // 使用者權限狀態管理

// === 初始化共用功能 ===
const userStore = useAuthUserStore(); // 使用者狀態存儲
const toast = useToast(); // 訊息提示工具
const confirm = useConfirm(); // 確認對話框工具

// === 常數定義 ===
const WeekDayNames = ["日","一","二","三","四","五","六"]; // 星期名稱對照表

// === 響應式資料定義 ===
// --- 頁面狀態控制 ---
const isAdmin = ref(false); // 是否為管理員身份
const header = ref("新增"); // 對話框標題
const addOrUpdate = ref("新增"); // 當前操作模式（新增/修改）
const dialogDisplay = ref(false); // 主對話框顯示狀態
const showWarning = ref(false); // 警告對話框顯示狀態
const warningMessage = ref(""); // 警告訊息內容

// --- 資料陣列 ---
const weekdays = ref<Array<any>>([]); // 週間工時資料列表
const shifts = ref<Array<any>>([]); // 班別資料列表

// --- 當前編輯資料 ---
const currentWeekday = ref<any>(null); // 正在編輯的週間工時記錄
const ShiftId = ref(1); // 班別編號
const ShiftName = ref(""); // 班別名稱
const WeekDay = ref(1); // 星期幾（0-6）

// --- 工作日設定 ---
const Workdays = ref(["1", "2", "3", "4", "5"]); // 預設工作日（週一到週五）
const WeekDays = ref([ // 星期選項清單
    { name: "日", value: 0 },
    { name: "一", value: 1 },
    { name: "二", value: 2 },
    { name: "三", value: 3 },
    { name: "四", value: 4 },
    { name: "五", value: 5 },
    { name: "六", value: 6 }
]);
// const RestDay = ref({ name: "六", value: 6 }); // 預設休息日
// const FixedDayOff = ref({ name: "日", value: 0 }); // 預設例假日

// --- 時間設定（使用固定日期 2049-01-01 作為基準日期） ---
const ArrivalTime = ref(new Date("2049-01-01T08:00")); // 正常上班時間
const DepartureTime = ref(new Date("2049-01-01T17:00")); // 正常下班時間
const FlexibleArrivalBefore = ref(new Date("2049-01-01T07:30")); // 最早彈性上班時間
const FlexibleArrivalAfter = ref(new Date("2049-01-01T09:00")); // 最晚彈性上班時間
const FlexibleDepartureBefore = ref(new Date("2049-01-01T16:30")); // 最早彈性下班時間
const FlexibleDepartureAfter = ref(new Date("2049-01-01T18:00")); // 最晚彈性下班時間
const MiddayBreakStart = ref(new Date("2049-01-01T12:00")); // 用餐休息開始時間
const MiddayBreakEnd = ref(new Date("2049-01-01T13:00")); // 用餐休息結束時間
const MorningRestStart = ref(new Date("2049-01-01T10:00")); // 前段休息開始時間
const MorningRestEnd = ref(new Date("2049-01-01T10:10")); // 前段休息結束時間
const AfternoonRestStart = ref(new Date("2049-01-01T15:00")); // 後段休息開始時間
const AfternoonRestEnd = ref(new Date("2049-01-01T15:10")); // 後段休息結束時間
const Comment = ref(""); // 備註欄位

// === 主要業務邏輯函數 ===

/**
 * 決定執行新增或更新操作
 * 根據當前操作模式（addOrUpdate.value）來決定呼叫新增或更新函數
 */
const addUpdate = () => {
    if (addOrUpdate.value === "新增") {
        return addWeekday(); // 執行新增週間工時
    } else {
        return updateWeekdayArray(); // 執行更新週間工時
    }
};

/**
 * 顯示新增週間工時對話框
 * 初始化新增模式的對話框狀態和預設值
 */
const showAddDialog = () => {
    getShifts(); // 載入班別資料
    addOrUpdate.value = "新增"; // 設定為新增模式
    dialogDisplay.value = true; // 顯示對話框
    header.value = "新增週間工時"; // 設定對話框標題
    ShiftName.value = ""; // 清空班別名稱
};

/**
 * 顯示編輯週間工時對話框
 * @param shiftId 班別編號
 * @param weekDay 星期幾（字串格式）
 */
const showUpdateDialog = (shiftId: number, weekDay: string) => {
    let theArray = null;
    
    // 從週間工時列表中找到要編輯的記錄
    for (let i = 0; i < weekdays.value.length; i++) {
        if (
            weekdays.value[i].ShiftId === shiftId &&
            weekdays.value[i].WeekDay === parseInt(weekDay, 10)
        ) {
            theArray = weekdays.value[i];
            currentWeekday.value = weekdays.value[i]; // 設定當前編輯的記錄
            break;
        }
    }

    // 如果找不到對應記錄，直接返回
    if (theArray === null) {
        return;
    }
    
    // 填入編輯表單的初始值
    WeekDay.value = parseInt(weekDay, 10);
    ShiftId.value = theArray.ShiftId;
    ShiftName.value = trimNull(theArray.ShiftName);
    header.value = "修改 星期" + toWeekDay(weekDay) + " 工時";
    addOrUpdate.value = "修改"; // 設定為修改模式
    
    // 將時間字串轉換為 Date 物件（加上基準日期前綴）
    ArrivalTime.value = new Date("2049-01-01T" + theArray.ArrivalTime);
    DepartureTime.value = new Date("2049-01-01T" + theArray.DepartureTime);
    FlexibleArrivalBefore.value = new Date("2049-01-01T" + theArray.FlexibleArrivalBefore);
    FlexibleArrivalAfter.value = new Date("2049-01-01T" + theArray.FlexibleArrivalAfter);
    FlexibleDepartureBefore.value = new Date("2049-01-01T" + theArray.FlexibleDepartureBefore);
    FlexibleDepartureAfter.value = new Date("2049-01-01T" + theArray.FlexibleDepartureAfter);
    MiddayBreakStart.value = new Date("2049-01-01T" + theArray.MiddayBreakStart);
    MiddayBreakEnd.value = new Date("2049-01-01T" + theArray.MiddayBreakEnd);
    MorningRestStart.value = new Date("2049-01-01T" + theArray.MorningRestStart);
    MorningRestEnd.value = new Date("2049-01-01T" + theArray.MorningRestEnd);
    AfternoonRestStart.value = new Date("2049-01-01T" + theArray.AfternoonRestStart);
    AfternoonRestEnd.value = new Date("2049-01-01T" + theArray.AfternoonRestEnd);
    Comment.value = trimNull(theArray.Comment);
    
    // 設定工作日和休假日資訊
    Workdays.value = theArray.WorkDays;
    // RestDay.value.value = theArray.RestDay;
    // RestDay.value.name = WeekDayNames[theArray.RestDay];
    // FixedDayOff.value.value = theArray.FixedDayOff;
    // FixedDayOff.value.name = WeekDayNames[theArray.FixedDayOff];
    
    dialogDisplay.value = true; // 顯示編輯對話框
};

// === 輔助工具函數 ===

/**
 * 將日期轉換為日期字串格式 (YYYY/MM/DD)
 * @param d 要轉換的日期物件
 * @returns 格式化的日期字串
 */
const toDateString = (d: Date) => {
    return d.getFullYear() + "/" + (d.getMonth() + 1) + "/" + d.getDate();
};

/**
 * 將日期轉換為日期時間字串格式 (YYYY-MM-DDTHH:mm:ss)
 * @param d 要轉換的日期物件
 * @returns 格式化的日期時間字串
 */
const toDateTimeString = (d: Date) => {
    return (
        d.getFullYear() +
        "-" +
        (d.getMonth() + 1) +
        "-" +
        d.getDate() +
        "T" +
        d.getHours() +
        ":" +
        d.getMinutes() +
        ":" +
        d.getSeconds()
    );
};

/**
 * 顯示訊息提示
 * @param message 要顯示的訊息內容
 */
const toastMessage = (message: string) => {
    toast.add({ severity: "info", summary: message, life: 3000 });
};

/**
 * 取得月份名稱（中文）
 * @param dtStr 日期字串
 * @returns 中文月份名稱或錯誤訊息
 */
const getMonthName = (dtStr: string) => {
    const date = new Date(dtStr);
    if (date.toString() !== "Invalid Date")
        return date.toLocaleString("zh-tw", { month: "long" });
    else return "不知道";
};

/**
 * 處理空字串或 null 值
 * @param theStr 要處理的字串
 * @returns 處理後的字串（去除前後空白，null 或空字串返回空字串）
 */
const trimNull = (theStr: string) => {
    if (theStr === null || theStr === "") {
        return "";
    }
    return theStr.trim();
};

/**
 * 將日期物件轉換為時間格式字串 (HH:MM)
 * @param dateTime 要轉換的日期時間物件
 * @returns 格式化的時間字串
 */
const getHHMM = (dateTime: Date) => {
    const hh = dateTime.getHours();
    const mm = dateTime.getMinutes();
    let ret = "";
    
    // 小時部分補零
    if (hh < 10) {
        ret = "0";
    }
    ret += hh + ":";
    
    // 分鐘部分補零
    if (mm < 10) {
        ret += "0";
    }
    ret += mm;
    return ret;
};

/**
 * 新增週間工時資料
 * 根據選定的工作日批次新增多筆週間工時記錄
 */
const addWeekday = () => {
    // 檢查班別名稱是否有效
    if (ShiftName.value !== null && ShiftName.value !== "") {
        // 整理並過濾工作日陣列（排序並移除空字串）
        const dayArray = Workdays.value.sort().filter((word) => word.length > 0);

        // 為每個選定的工作日新增週間工時記錄
        for (const d in dayArray) {
            // 建立新增資料物件
            const newRow = {
                ShiftId: ShiftId.value, // 班別編號
                ShiftName: ShiftName.value, // 班別名稱
                WeekDay: parseInt(Workdays.value[d], 10), // 星期幾（轉換為數字）
                ArrivalTime: getHHMM(ArrivalTime.value), // 正常上班時間
                DepartureTime: getHHMM(DepartureTime.value), // 正常下班時間
                FlexibleArrivalBefore: getHHMM(FlexibleArrivalBefore.value), // 最早彈性上班時間
                FlexibleArrivalAfter: getHHMM(FlexibleArrivalAfter.value), // 最晚彈性上班時間
                FlexibleDepartureBefore: getHHMM(FlexibleDepartureBefore.value), // 最早彈性下班時間
                FlexibleDepartureAfter: getHHMM(FlexibleDepartureAfter.value), // 最晚彈性下班時間
                MiddayBreakStart: getHHMM(MiddayBreakStart.value), // 用餐休息開始時間
                MiddayBreakEnd: getHHMM(MiddayBreakEnd.value), // 用餐休息結束時間
                MorningRestStart: getHHMM(MorningRestStart.value), // 前段休息開始時間
                MorningRestEnd: getHHMM(MorningRestEnd.value), // 前段休息結束時間
                AfternoonRestStart: getHHMM(AfternoonRestStart.value), // 後段休息開始時間
                AfternoonRestEnd: getHHMM(AfternoonRestEnd.value), // 後段休息結束時間
                Comment: trimNull(Comment.value), // 備註（去除空白）
            };

            // 透過 API 新增週間工時資料
            axios
                .post(weekdayAddUrl, newRow)
                .then((response) => {
                    if (response.data == true) {
                        toastMessage(`新增 ${ShiftName.value} 成功`);
                        getWeekdays(); // 重新載入週間工時資料
                    }
                })
                .catch((error) => {
                    console.log(error);
                    alert(error);
                    toastMessage(`新增 ${ShiftName.value} 失敗`);
                    getWeekdays(); // 即使失敗也重新載入資料
                });
        }
    } else {
        // 班別名稱為空時顯示警告
        showWarning.value = true;
        warningMessage.value = "班別名稱不可為空白";
        return;
    }
    dialogDisplay.value = false; // 關閉對話框
};

/**
 * 更新週間工時資料
 * 修改現有的週間工時記錄
 */
const updateWeekdayArray = () => {
    // 檢查是否有選定要更新的記錄
    if (currentWeekday.value === null) {
        return;
    }

    // 更新當前記錄的時間資料（轉換為 HH:MM 格式）
    currentWeekday.value.ArrivalTime = getHHMM(ArrivalTime.value);
    currentWeekday.value.DepartureTime = getHHMM(DepartureTime.value);
    currentWeekday.value.FlexibleArrivalBefore = getHHMM(FlexibleArrivalBefore.value);
    currentWeekday.value.FlexibleArrivalAfter = getHHMM(FlexibleArrivalAfter.value);
    currentWeekday.value.FlexibleDepartureBefore = getHHMM(FlexibleDepartureBefore.value);
    currentWeekday.value.FlexibleDepartureAfter = getHHMM(FlexibleDepartureAfter.value);
    currentWeekday.value.MiddayBreakStart = getHHMM(MiddayBreakStart.value);
    currentWeekday.value.MiddayBreakEnd = getHHMM(MiddayBreakEnd.value);
    currentWeekday.value.MorningRestStart = getHHMM(MorningRestStart.value);
    currentWeekday.value.MorningRestEnd = getHHMM(MorningRestEnd.value);
    currentWeekday.value.AfternoonRestStart = getHHMM(AfternoonRestStart.value);
    currentWeekday.value.AfternoonRestEnd = getHHMM(AfternoonRestEnd.value);
    currentWeekday.value.Comment = trimNull(Comment.value);

    // 建立更新資料物件
    const newRow = {
        ShiftId: ShiftId.value, // 班別編號
        ShiftName: ShiftName.value, // 班別名稱
        WeekDay: WeekDay.value, // 星期幾
        ArrivalTime: getHHMM(ArrivalTime.value), // 正常上班時間
        DepartureTime: getHHMM(DepartureTime.value), // 正常下班時間
        FlexibleArrivalBefore: getHHMM(FlexibleArrivalBefore.value), // 最早彈性上班時間
        FlexibleArrivalAfter: getHHMM(FlexibleArrivalAfter.value), // 最晚彈性上班時間
        FlexibleDepartureBefore: getHHMM(FlexibleDepartureBefore.value), // 最早彈性下班時間
        FlexibleDepartureAfter: getHHMM(FlexibleDepartureAfter.value), // 最晚彈性下班時間
        MiddayBreakStart: getHHMM(MiddayBreakStart.value), // 用餐休息開始時間
        MiddayBreakEnd: getHHMM(MiddayBreakEnd.value), // 用餐休息結束時間
        MorningRestStart: getHHMM(MorningRestStart.value), // 前段休息開始時間
        MorningRestEnd: getHHMM(MorningRestEnd.value), // 前段休息結束時間
        AfternoonRestStart: getHHMM(AfternoonRestStart.value), // 後段休息開始時間
        AfternoonRestEnd: getHHMM(AfternoonRestEnd.value), // 後段休息結束時間
        Comment: trimNull(Comment.value), // 備註（去除空白）
    };

    // 透過 API 更新週間工時資料
    axios
        .put(weekdayUpdateUrl, newRow)
        .then((response) => {
            if (response.data == true) {
                toastMessage(`更新週間工時: ${newRow.ShiftName} 成功`);
                getWeekdays(); // 重新載入週間工時資料
            } else {
                toastMessage(`更新週間工時: ${newRow.ShiftName} 失敗`);
            }
        })
        .catch((error) => {
            toastMessage("更新週間工時:" + newRow.ShiftName + " 失敗 " + error);
        });
    dialogDisplay.value = false; // 關閉對話框
};

/**
 * 刪除週間工時資料
 * @param shiftId 班別編號
 * @param weekDay 星期幾（字串格式）
 */
const deleteWeekday = (shiftId: number, weekDay: string) => {
    let index = -1;
    
    // 在週間工時列表中尋找要刪除的記錄
    for (let i = 0; i < weekdays.value.length; i++) {
        if (
            weekdays.value[i].ShiftId === shiftId &&
            weekdays.value[i].WeekDay === parseInt(weekDay, 10)
        ) {
            index = i;
            break;
        }
    }
    
    // 如果找不到記錄則直接返回
    if (index === -1) {
        return;
    }
    
    const theArray = weekdays.value[index]; // 取得要刪除的記錄
    
    // 建立刪除資料物件
    const newRow = {
        ShiftId: theArray.ShiftId, // 班別編號
        ShiftName: theArray.ShiftName, // 班別名稱
        WeekDay: parseInt(theArray.WeekDay, 10), // 星期幾（轉換為數字）
        ArrivalTime: theArray.ArrivalTime, // 正常上班時間
        DepartureTime: theArray.DepartureTime, // 正常下班時間
        FlexibleArrivalBefore: theArray.FlexibleArrivalBefore, // 最早彈性上班時間
        FlexibleArrivalAfter: theArray.FlexibleArrivalAfter, // 最晚彈性上班時間
        FlexibleDepartureBefore: theArray.FlexibleDepartureBefore, // 最早彈性下班時間
        FlexibleDepartureAfter: theArray.FlexibleDepartureAfter, // 最晚彈性下班時間
        MiddayBreakStart: theArray.MiddayBreakStart, // 用餐休息開始時間
        MiddayBreakEnd: theArray.MiddayBreakEnd, // 用餐休息結束時間
        MorningRestStart: theArray.MorningRestStart, // 前段休息開始時間
        MorningRestEnd: theArray.MorningRestEnd, // 前段休息結束時間
        AfternoonRestStart: theArray.AfternoonRestStart, // 後段休息開始時間
        AfternoonRestEnd: theArray.AfternoonRestEnd, // 後段休息結束時間
        Comment: trimNull(theArray.Comment), // 備註（去除空白）
    };
    
    // 顯示確認刪除對話框
    confirm.require({
        message: "刪除後無法復原，確定嗎？",
        header: "確認刪除",
        icon: "pi pi-exclamation-triangle",
        accept: () => {
            // 使用者確認刪除時執行的回調函數
            axios
                .delete(weekdayDeleteUrl, { data: newRow })
                .then((response) => {
                    if (response.data == true) {
                        toastMessage(`刪除 ${newRow.ShiftName} 工時成功`);
                        getWeekdays(); // 重新載入週間工時資料
                    } else {
                        toastMessage(`刪除 ${newRow.ShiftName} 工時失敗`);
                    }
                })
                .catch((error) => {
                    toastMessage(`刪除 ${newRow.ShiftName} 工時失敗 ${error}`);
                    getWeekdays(); // 即使失敗也重新載入資料
                });
        },
        reject: () => {
            // 使用者取消刪除時執行的回調函數（目前為空）
        },
    });
    dialogDisplay.value = false; // 關閉對話框
    getWeekdays(); // 重新載入週間工時資料
};

/**
 * 取消編輯操作
 * 關閉週間工時編輯對話框
 */
const cancelWeekday = () => {
    dialogDisplay.value = false;
};

// === 資料載入與處理函數 ===

/**
 * 載入班別資料
 * 從伺服器取得所有班別清單，並設定預設班別
 */
const getShifts = () => {
    axios.get(workShiftGetUrl).then((response) => {
        shifts.value = response.data; // 設定班別列表
        
        // 如果有班別資料，設定第一個班別為預設選項
        if (shifts.value !== null && shifts.value.length > 0) {
            ShiftId.value = shifts.value[0].ShiftId;
            shiftChange(); // 載入該班別的預設時間設定
        }
    });
};

/**
 * 班別變更處理
 * 當使用者選擇不同班別時，載入該班別的預設時間設定
 */
const shiftChange = () => {
    // 在班別列表中尋找選定的班別
    for (let i = 0; i < shifts.value.length; i++) {
        if (shifts.value[i].ShiftId === ShiftId.value) {
            // 設定班別名稱
            ShiftName.value = shifts.value[i].ShiftName;
            
            // 載入該班別的預設時間設定（加上基準日期前綴）
            ArrivalTime.value = new Date("2049-01-01T" + shifts.value[i].ArrivalTime);
            DepartureTime.value = new Date("2049-01-01T" + shifts.value[i].DepartureTime);
            FlexibleArrivalBefore.value = new Date("2049-01-01T" + shifts.value[i].FlexibleArrivalBefore);
            FlexibleArrivalAfter.value = new Date("2049-01-01T" + shifts.value[i].FlexibleArrivalAfter);
            FlexibleDepartureBefore.value = new Date("2049-01-01T" + shifts.value[i].FlexibleDepartureBefore);
            FlexibleDepartureAfter.value = new Date("2049-01-01T" + shifts.value[i].FlexibleDepartureAfter);
            MiddayBreakStart.value = new Date("2049-01-01T" + shifts.value[i].MiddayBreakStart);
            MiddayBreakEnd.value = new Date("2049-01-01T" + shifts.value[i].MiddayBreakEnd);
            MorningRestStart.value = new Date("2049-01-01T" + shifts.value[i].MorningRestStart);
            MorningRestEnd.value = new Date("2049-01-01T" + shifts.value[i].MorningRestEnd);
            AfternoonRestStart.value = new Date("2049-01-01T" + shifts.value[i].AfternoonRestStart);
            AfternoonRestEnd.value = new Date("2049-01-01T" + shifts.value[i].AfternoonRestEnd);
            break;
        }
    }
};

/**
 * 載入週間工時資料
 * 從伺服器取得所有週間工時設定資料
 */
const getWeekdays = async () => {
    try {
        const response = await axios.get(weekdayGetUrl);
        weekdays.value = response.data; // 設定週間工時資料列表
        await nextTick(); // 等待 DOM 更新
    } catch (error) {
        console.error(error);
        toastMessage("取得週間工時資料失敗：" + error);
    }
};

/**
 * 將數字或字串轉換為星期名稱
 * @param no 星期幾的數字或字串（0-6）
 * @returns 對應的中文星期名稱
 */
const toWeekDay = (no: string | number) => {
    let ret = null;
    if (typeof no === "string") {
        ret = WeekDayNames[parseInt(no, 10)]; // 字串轉數字後查找
    } else if (typeof no === "number") {
        ret = WeekDayNames[no]; // 直接查找
    }
    return ret;
};

// === 生命週期鉤子 ===
/**
 * 元件掛載後執行
 * 初始化頁面資料和使用者權限狀態
 */
onMounted(async () => {
    isAdmin.value = userStore.isAdmin; // 設定管理員權限狀態
    await getShifts(); // 載入班別資料
    await getWeekdays(); // 載入週間工時資料
    await nextTick(); // 等待 DOM 更新完成
});
</script>

<style scoped>
.working-day {
    color: blue;
}

.rest-day {
    color: red;
}

.nowrap {
    white-space: nowrap;
}
</style>
