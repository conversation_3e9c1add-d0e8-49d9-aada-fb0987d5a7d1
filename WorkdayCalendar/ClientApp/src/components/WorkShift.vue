<template>
  <div>
    <img alt="行事曆" src="../assets/calendar.png" />
    <!-- <Button v-on:click="test" iconPos="left" label="test" /> -->
    <Button v-on:click="showAddDialog" v-if="isAdmin" icon="pi pi-plus" iconPos="left"
      class="p-button-raised p-button-rounded" label="新增班別" />

    <div>
      <!-- <DataTable :value="shifts" editMode="row" stripedRows responsiveLayout="scroll" class="p-datatable-customers" :loading="loading" dataKey="ShiftId"> -->
      <DataTable :value="shifts" editMode="row" stripedRows responsiveLayout="scroll" class="p-datatable-customers"
        dataKey="ShiftId">
        <!--
           :rows="10" :paginator="true"
           paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown" :rowsPerPageOptions="[10,25,50]"
            currentPageReportTemplate="從 {first} 到 {last} 共 {totalRecords} 筆"
        -->
        <Column field="ShiftName" header="班別"></Column>
        <Column field="FlexibleArrivalBefore" header="最早彈性上班時間"></Column>
        <Column field="ArrivalTime" header="正常上班時間"></Column>
        <Column field="FlexibleArrivalAfter" header="最晚彈性上班時間"></Column>
        <Column field="MorningRestStart" header="前段休息開始時間"></Column>
        <Column field="MorningRestEnd" header="前段休息結束時間"></Column>
        <Column field="MiddayBreakStart" header="用餐休息開始時間"></Column>
        <Column field="MiddayBreakEnd" header="用餐休息結束時間"></Column>
        <Column field="AfternoonRestStart" header="後段休息開始時間"></Column>
        <Column field="AfternoonRestEnd" header="後段休息結束時間"></Column>
        <Column field="FlexibleDepartureBefore" header="最早彈性下班時間"></Column>
        <Column field="DepartureTime" header="正常下班時間"></Column>
        <Column field="FlexibleDepartureAfter" header="最晚彈性下班時間"></Column>

        <!-- <Column :rowEditor="true" bodyStyle="text-align:center" class="p-button-raised p-button-rounded" icon="pi pi-pencil"></Column> -->
        <Column field="ShiftId" bodyStyle="text-align: center; overflow: visible;" class="nowrap">
          <template #body="slotProps">
            <div class="p-field p-grid">
              <Button type="button" v-if="isAdmin" v-on:click="showUpdateDialog(slotProps.data.ShiftId)" label="" alt="編輯"
                icon="pi pi-pencil" iconPos="left" class="p-button-raised p-button-rounded" />
              <Button type="button" v-if="isAdmin" v-on:click="deleteCalendar(slotProps.data.ShiftId)" label="" alt="刪除"
                icon="pi pi-trash" iconPos="left" class="p-button-danger p-button-raised p-button-rounded" />
            </div>
          </template>
        </Column>
      </DataTable>
    </div>

    <Dialog position="center" v-model:visible="dialogDisplay" v-bind:modal="true">
      <template #header>
        <h3>{{ header }}</h3>
      </template>
      <div class="p-fluid">
        <div class="p-field p-grid">
          <label for="ShiftName" class="p-col-fixed" style="width: 220px">班別名稱：</label>
          <div class="p-col-fixed" style="width: 300px">
            <InputText type="text" v-model="ShiftName" placeholder="班別名稱" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="ArrivalTime" class="p-col-fixed" style="width: 220px">正常上班時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="ArrivalTime" />
          </div>
        </div>

        <div class="p-field p-grid">
          <label for="FlexibleArrivalBefore" class="p-col-fixed" style="width: 220px">最早彈性上班時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="FlexibleArrivalBefore" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="FlexibleArrivalAfter" class="p-col-fixed" style="width: 220px">最晚彈性上班時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="FlexibleArrivalAfter" />
          </div>
        </div>

        <div class="p-field p-grid">
          <label for="MiddayBreakStart" class="p-col-fixed" style="width: 220px">用餐休息開始時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="MiddayBreakStart" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="MiddayBreakEnd" class="p-col-fixed" style="width: 220px">用餐休息結束時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="MiddayBreakEnd" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="MorningRestStart" class="p-col-fixed" style="width: 220px">前段休息開始時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="MorningRestStart" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="MorningRestEnd" class="p-col-fixed" style="width: 220px">前段休息結束時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="MorningRestEnd" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="AfternoonRestStart" class="p-col-fixed" style="width: 220px">後段休息開始時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="AfternoonRestStart" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="AfternoonRestEnd" class="p-col-fixed" style="width: 220px">後段休息結束時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="AfternoonRestEnd" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="FlexibleDepartureBefore" class="p-col-fixed" style="width: 220px">最早彈性下班時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="FlexibleDepartureBefore" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="DepartureTime" class="p-col-fixed" style="width: 220px">正常下班時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="DepartureTime" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="FlexibleDepartureAfter" class="p-col-fixed" style="width: 220px">最晚彈性下班時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="FlexibleDepartureAfter" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="Workdays" class="p-col-fixed" style="width: 220px">工作日</label>
          <div class="p-col-fixed" style="width: 300px">
            <Checkbox name="Workdays" value="0" v-model="Workdays" />日
            <Checkbox name="Workdays" value="1" v-model="Workdays" />一
            <Checkbox name="Workdays" value="2" v-model="Workdays" />二
            <Checkbox name="Workdays" value="3" v-model="Workdays" />三
            <Checkbox name="Workdays" value="4" v-model="Workdays" />四
            <Checkbox name="Workdays" value="5" v-model="Workdays" />五
            <Checkbox name="Workdays" value="6" v-model="Workdays" />六
          </div>
        </div>
        <div class="p-field p-grid"><label for="Workdays" class="p-col-fixed" style="width: 220px">休息日</label>
          <div class="p-col-fixed" style="width: 300px">
            <Dropdown v-model="RestDay" :options="WeekDays" optionLabel="name" placeholder="選擇休息日"
              class="w-full md:w-14rem" />
          </div>
        </div>
        <div class="p-field p-grid"><label for="Workdays" class="p-col-fixed" style="width: 220px">例假日</label>
          <div class="p-col-fixed" style="width: 300px">
            <Dropdown v-model="FixedDayOff" :options="WeekDays" optionLabel="name" placeholder="選擇例假日"
              class="w-full md:w-14rem" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="AfternoonRestStart" class="p-col-fixed" style="width: 220px">備註：</label>
          <div class="p-col-fixed" style="width: 300px">
            <InputText v-model="Comment" type="text" placeholder="備註" />
          </div>
        </div>
      </div>
      <Button v-bind:label="addOrUpdate" v-on:click="addUpdate" class="p-button-raised p-button-rounded"
        icon="pi pi-check" autofocus />
      &nbsp;&nbsp;
      <Button label="取消" v-on:click="cancelCalendar" class="p-button-raised p-button-danger p-button-rounded"
        icon="pi pi-times" />
    </Dialog>
    <Dialog position="center" v-model:visible="showWarning" v-bind:modal="true">
      <h3>{{ warningMessage }}</h3>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
// ===== Vue 3 Composition API 導入 =====
import { ref, reactive, onMounted, nextTick } from "vue";

// ===== PrimeVue 組件與服務導入 =====
import { useToast } from "primevue/usetoast";        // Toast 通知服務
import { useConfirm } from "primevue/useconfirm";    // 確認對話框服務
import Checkbox from "primevue/checkbox";            // 複選框組件
import DataTable from "primevue/datatable";          // 資料表格組件
import Calendar from "primevue/calendar";            // 日期時間選擇器組件
import Dialog from "primevue/dialog";                // 對話框組件
import InputText from "primevue/inputtext";          // 文字輸入框組件

// ===== HTTP 請求與 API 導入 =====
import axios from "axios";                           // HTTP 請求庫
import { 
  userIsAdminUrl,                                    // 驗證管理員身份 API
  workShiftGetUrl,                                   // 取得班別清單 API
  workShiftAddUrl,                                   // 新增班別 API
  workShiftUpdateUrl,                               // 更新班別 API
  workShiftDeleteUrl                                // 刪除班別 API
} from "../components/api";

// ===== 狀態管理導入 =====
import { useAuthUserStore } from '../store/index';   // 使用者認證狀態管理

// ===== 初始化服務與全域狀態 =====
const userStore = useAuthUserStore();                // 使用者狀態管理實例
const toast = useToast();                           // Toast 通知服務實例
const confirm = useConfirm();                       // 確認對話框服務實例

// ===== 常數定義 =====
const WeekDayNames = ["日","一","二","三","四","五","六"];  // 星期名稱對應陣列

// ===== 響應式狀態變數定義 =====

// --- 權限與介面控制相關 ---
const isAdmin = ref(false);                         // 是否為管理員身份
const dialogDisplay = ref(false);                   // 控制新增/編輯對話框顯示狀態
const showWarning = ref(false);                     // 控制警告對話框顯示狀態
const warningMessage = ref("");                     // 警告訊息內容

// --- 表單狀態與標題 ---
const header = ref("新增");                         // 對話框標題文字
const addOrUpdate = ref("新增");                    // 操作模式：新增或修改

// --- 資料管理相關 ---
const shifts = ref<Array<any>>([]);                // 班別清單資料陣列
const currentCalendar = ref<any>(null);            // 目前選中的班別物件（用於編輯）

// --- 班別基本資訊 ---
const ShiftId = ref(1);                            // 班別 ID
const ShiftName = ref("");                         // 班別名稱
const Comment = ref("");                           // 班別備註說明

// --- 工作日與休假設定 ---
const Workdays = ref(["1", "2", "3", "4", "5"]);  // 工作日陣列（預設週一到週五）
const WeekDays = reactive([                        // 星期選項清單
  { name: "日", value: 0 },
  { name: "一", value: 1 },
  { name: "二", value: 2 },
  { name: "三", value: 3 },
  { name: "四", value: 4 },
  { name: "五", value: 5 },
  { name: "六", value: 6 }
]);
const RestDay = ref({ name: "六", value: 6 });     // 休息日設定（預設星期六）
const FixedDayOff = ref({ name: "日", value: 0 }); // 例假日設定（預設星期日）

// --- 工作時間設定 ---
const ArrivalTime = ref(new Date("2049-01-01T08:00"));              // 正常上班時間
const DepartureTime = ref(new Date("2049-01-01T17:00"));            // 正常下班時間
const FlexibleArrivalBefore = ref(new Date("2049-01-01T07:30"));    // 最早彈性上班時間
const FlexibleArrivalAfter = ref(new Date("2049-01-01T09:00"));     // 最晚彈性上班時間
const FlexibleDepartureBefore = ref(new Date("2049-01-01T16:30"));  // 最早彈性下班時間
const FlexibleDepartureAfter = ref(new Date("2049-01-01T18:00"));   // 最晚彈性下班時間

// --- 休息時間設定 ---
const MiddayBreakStart = ref(new Date("2049-01-01T12:00"));         // 用餐休息開始時間
const MiddayBreakEnd = ref(new Date("2049-01-01T13:00"));           // 用餐休息結束時間
const MorningRestStart = ref(new Date("2049-01-01T10:00"));         // 前段休息開始時間
const MorningRestEnd = ref(new Date("2049-01-01T10:10"));           // 前段休息結束時間
const AfternoonRestStart = ref(new Date("2049-01-01T15:00"));       // 後段休息開始時間
const AfternoonRestEnd = ref(new Date("2049-01-01T15:10"));         // 後段休息結束時間

// ===== 輔助工具函數 =====

/**
 * 顯示 Toast 通知訊息
 * @param message - 要顯示的訊息內容
 */
const toastMessage = (message: string): void => {
  toast.add({ severity: "info", summary: message, life: 3000 });
};

/**
 * 統一的新增/修改操作處理
 * 根據當前操作模式決定執行新增或修改
 */
const addUpdate = () => {
  if (addOrUpdate.value === "新增") {
    return addCalendar();
  } else {
    return updateCalendarArray();
  }
};

/**
 * 將 Date 物件轉換為 HH:MM 格式字串
 * @param dateTime - 要轉換的日期時間物件
 * @returns 格式化後的時間字串 (HH:MM)
 */
const getHHMM = (dateTime: Date) => {
  const hh = dateTime.getHours();
  const mm = dateTime.getMinutes();
  let ret = "";
  
  // 小時補零處理
  if (hh < 10) {
    ret = "0";
  }
  ret += hh + ":";
  
  // 分鐘補零處理
  if (mm < 10) {
    ret += "0";
  }
  ret += mm;
  
  return ret;
};

/**
 * 處理空值或 null 字串，進行修剪處理
 * @param theStr - 要處理的字串（可能為 null）
 * @returns 修剪後的字串，null 或空字串則回傳空字串
 */
const trimNull = (theStr: string | null) => {
  if (theStr === null || theStr === "") {
    return "";
  } else {
    return theStr.trim();
  }
};

// ===== 對話框操作函數 =====

/**
 * 顯示新增班別對話框
 * 重置表單欄位並設定為新增模式
 */
const showAddDialog = () => {
  dialogDisplay.value = true;                       // 顯示對話框
  header.value = "新增班別";                        // 設定標題
  addOrUpdate.value = "新增";                       // 設定操作模式
  ShiftName.value = "";                            // 清空班別名稱
  //alert(shifts.value[0].ArrivalTime+ " "+ (typeof shifts.value[0].ArrivalTime));
};

/**
 * 顯示修改班別對話框
 * 根據班別 ID 查詢資料並填入表單欄位
 * @param index - 班別 ID
 */
const showUpdateDialog = (index: number) => {
  let theArray = null;
  
  // 根據 ShiftId 查詢對應的班別資料
  for (let i = 0; i < shifts.value.length; i++) {
    if (shifts.value[i].ShiftId === index) {
      theArray = shifts.value[i];
      currentCalendar.value = shifts.value[i];      // 儲存目前編輯的班別物件
      break;
    }
  }

  // 如果找不到對應的班別資料，直接返回
  if (theArray === null) {
    return;
  }
  
  // 設定表單資料
  ShiftId.value = theArray.ShiftId;
  ShiftName.value = trimNull(theArray.ShiftName);
  header.value = "修改班別";                        // 設定對話框標題
  addOrUpdate.value = "修改";                       // 設定操作模式為修改
  
  // 處理工作日字串：分割、排序、過濾空值
  Workdays.value = theArray.Workdays.split(",")
    .sort()
    .filter((word: string) => word.length > 0);
  //console.log("theArray.Workdays=" + theArray.Workdays);
  
  // 設定休息日和例假日
  RestDay.value.value = theArray.RestDay;
  RestDay.value.name = WeekDayNames[theArray.RestDay];
  FixedDayOff.value.value = theArray.FixedDayOff;
  FixedDayOff.value.name = WeekDayNames[theArray.FixedDayOff];
  
  // 設定各種時間（將字串時間轉換為 Date 物件）
  ArrivalTime.value = new Date("2049-01-01T" + theArray.ArrivalTime);
  DepartureTime.value = new Date("2049-01-01T" + theArray.DepartureTime);
  FlexibleArrivalBefore.value = new Date("2049-01-01T" + theArray.FlexibleArrivalBefore);
  FlexibleArrivalAfter.value = new Date("2049-01-01T" + theArray.FlexibleArrivalAfter);
  FlexibleDepartureBefore.value = new Date("2049-01-01T" + theArray.FlexibleDepartureBefore);
  FlexibleDepartureAfter.value = new Date("2049-01-01T" + theArray.FlexibleDepartureAfter);
  MiddayBreakStart.value = new Date("2049-01-01T" + theArray.MiddayBreakStart);
  MiddayBreakEnd.value = new Date("2049-01-01T" + theArray.MiddayBreakEnd);
  MorningRestStart.value = new Date("2049-01-01T" + theArray.MorningRestStart);
  MorningRestEnd.value = new Date("2049-01-01T" + theArray.MorningRestEnd);
  AfternoonRestStart.value = new Date("2049-01-01T" + theArray.AfternoonRestStart);
  AfternoonRestEnd.value = new Date("2049-01-01T" + theArray.AfternoonRestEnd);
  
  // 設定備註
  Comment.value = trimNull(theArray.Comment);
  
  // 顯示對話框
  dialogDisplay.value = true;
};

/**
 * 新增班別處理函數
 * 驗證表單資料並向後端 API 發送新增請求
 */
const addCalendar = () => {
  // 驗證班別名稱是否為空
  if (ShiftName.value !== null && ShiftName.value !== "") {
    // 組建新班別資料物件
    const newRow = {
      ShiftId: shifts.value.length + 100,           // 暫時 ID（後端會回傳正確的 ID）
      ShiftName: trimNull(ShiftName.value),         // 班別名稱
      Workdays: Workdays.value.sort()              // 工作日陣列轉字串
        .filter((word) => word.length > 0)
        .toString(),
      RestDay: RestDay.value.value,                 // 休息日
      FixedDayOff: FixedDayOff.value.value,        // 例假日
      
      // 各種時間轉換為 HH:MM 格式
      ArrivalTime: getHHMM(ArrivalTime.value),
      DepartureTime: getHHMM(DepartureTime.value),
      FlexibleArrivalBefore: getHHMM(FlexibleArrivalBefore.value),
      FlexibleArrivalAfter: getHHMM(FlexibleArrivalAfter.value),
      FlexibleDepartureBefore: getHHMM(FlexibleDepartureBefore.value),
      FlexibleDepartureAfter: getHHMM(FlexibleDepartureAfter.value),
      MiddayBreakStart: getHHMM(MiddayBreakStart.value),
      MiddayBreakEnd: getHHMM(MiddayBreakEnd.value),
      MorningRestStart: getHHMM(MorningRestStart.value),
      MorningRestEnd: getHHMM(MorningRestEnd.value),
      AfternoonRestStart: getHHMM(AfternoonRestStart.value),
      AfternoonRestEnd: getHHMM(AfternoonRestEnd.value),
      Comment: trimNull(Comment.value),             // 備註
    };

    //console.log("newRow.Workdays="+newRow.Workdays);
    const mCal = shifts.value;                      // 取得班別清單參考
    
    // 發送新增請求到後端 API
    axios
      .post(workShiftAddUrl, newRow)
      .then((response) => {
        console.log("shiftId=" + response.data);
        if (response.data > 0) {
          newRow.ShiftId = response.data;           // 更新為後端回傳的正確 ID
          mCal.push(newRow);                        // 加入到本地清單
          toastMessage("新增 班別:" + ShiftName.value + " 成功");
        } else {
          toastMessage("新增 班別:" + ShiftName.value + " 成功");
        }
      })
      .catch((error) => {
        toastMessage("新增 班別:" + ShiftName.value + " 失敗" + error);
      });
  } else {
    // 班別名稱為空時顯示警告
    showWarning.value = true;
    warningMessage.value = "班別名稱不可為空白";
    return;
  }
  
  // 關閉對話框
  dialogDisplay.value = false;
};

/**
 * 更新班別處理函數（根據 ID 查詢）
 * 先查詢指定 ID 的班別資料，然後呼叫更新陣列函數
 * @param shiftId - 要更新的班別 ID
 */
const updateCalendar = (shiftId: number) => {
  let theArray = null;
  
  // 根據 ShiftId 查詢對應的班別資料
  for (let i = 0; i < shifts.value.length; i++) {
    if (shifts.value[i].ShiftId === shiftId) {
      theArray = shifts.value[i];
      currentCalendar.value = shifts.value[i];      // 設定目前編輯的班別
      break;
    }
  }
  
  // 如果找不到對應的班別，直接返回
  if (theArray === null) {
    return;
  }

  // 執行更新操作
  updateCalendarArray();
};

/**
 * 更新班別陣列資料處理函數
 * 將表單資料更新到目前編輯的班別物件，並發送更新請求到後端
 */
const updateCalendarArray = () => {
  // 檢查是否有目前編輯的班別
  if (currentCalendar.value === null) {
    return;
  }
  
  const theArray = currentCalendar.value;           // 取得目前編輯的班別物件
  
  // 更新班別基本資訊
  theArray.ShiftId = ShiftId.value;
  theArray.ShiftName = trimNull(ShiftName.value);
  
  // 處理工作日陣列：排序並過濾
  Workdays.value = Workdays.value.sort().filter(function () {
    return true;
  });
  theArray.Workdays = Workdays.value.toString();   // 轉換為字串儲存
  //alert(theArray.Workdays + "  " + Workdays.value);
  
  // 更新休息日和例假日設定
  theArray.RestDay = RestDay.value.value;
  theArray.FixedDayOff = FixedDayOff.value.value;
  
  // 更新所有時間設定（轉換為 HH:MM 格式）
  theArray.ArrivalTime = getHHMM(ArrivalTime.value);
  theArray.DepartureTime = getHHMM(DepartureTime.value);
  theArray.FlexibleArrivalBefore = getHHMM(FlexibleArrivalBefore.value);
  theArray.FlexibleArrivalAfter = getHHMM(FlexibleArrivalAfter.value);
  theArray.FlexibleDepartureBefore = getHHMM(FlexibleDepartureBefore.value);
  theArray.FlexibleDepartureAfter = getHHMM(FlexibleDepartureAfter.value);
  theArray.MiddayBreakStart = getHHMM(MiddayBreakStart.value);
  theArray.MiddayBreakEnd = getHHMM(MiddayBreakEnd.value);
  theArray.MorningRestStart = getHHMM(MorningRestStart.value);
  theArray.MorningRestEnd = getHHMM(MorningRestEnd.value);
  theArray.AfternoonRestStart = getHHMM(AfternoonRestStart.value);
  theArray.AfternoonRestEnd = getHHMM(AfternoonRestEnd.value);
  theArray.Comment = trimNull(Comment.value);
  
  // 發送更新請求到後端 API
  axios
    .put(workShiftUpdateUrl, theArray)
    .then((response) => {
      if (response.data == true) {
        toastMessage("更新 班別:" + ShiftName.value + " 成功");
      } else {
        toastMessage("更新 班別:" + ShiftName.value + " 失敗");
      }
    })
    .catch((error) => {
      //console.log(error);
      toastMessage("更新 班別:" + ShiftName.value + " 失敗 " + error);
    });
    
  // 關閉對話框
  dialogDisplay.value = false;
};

/**
 * 刪除班別處理函數
 * 顯示確認對話框，確認後向後端發送刪除請求
 * @param shiftId - 要刪除的班別 ID
 */
const deleteCalendar = (shiftId: number) => {
  let index = -1;
  
  // 根據 ShiftId 查詢對應的班別資料和索引
  for (let i = 0; i < shifts.value.length; i++) {
    if (shifts.value[i].ShiftId === shiftId) {
      index = i;
      ShiftId.value = shiftId;                      // 儲存要刪除的班別 ID
      ShiftName.value = shifts.value[i].ShiftName;  // 儲存班別名稱供訊息顯示
      break;
    }
  }
  
  //alert(index);
  // 如果找不到對應的班別，直接返回
  if (index === -1) {
    return;
  }

  // let ddd = true;
  // let success = "成功";
  
  // 顯示確認刪除對話框
  confirm.require({
    message: "刪除後無法復原，確定嗎？",
    header: "確認刪除",
    icon: "pi pi-exclamation-triangle",
    accept: () => {
      // 使用者確認刪除時的回調函數
      axios
        .delete(workShiftDeleteUrl + shiftId)       // 發送刪除請求
        .then((response) => {
          console.log(response);
          if (response.data == true) {
            //shifts.value.splice(index, 1);         // 從本地陣列移除（已註解，改為重新載入）
            toastMessage("刪除 班別:" + ShiftName.value + " 成功");
            getShifts();                            // 重新載入班別清單
          } else {
            toastMessage("刪除 班別:" + ShiftName.value + " 失敗");
          }
          //alert(response);
        })
        .catch((error) => {
          // console.log(error);
          //alert(error);
          toastMessage("刪除 班別:" + ShiftName.value + " 失敗 " + error);
        });
      //console.log("shifts.value.length=" + shifts.value.length);
    },
    reject: () => {
      // 使用者取消刪除時的回調函數
      // ddd = false;
    },
  });

  //dialogDisplay.value = false;
};

/**
 * 取消對話框操作
 * 關閉新增/編輯對話框
 */
const cancelCalendar = () => {
  dialogDisplay.value = false;
};

/**
 * 取得班別清單資料
 * 從後端 API 載入所有班別資料
 */
const getShifts = () => {
  axios
    .get(workShiftGetUrl)                           // 發送取得班別清單請求
    .then((response) => {
      // console.log(response.data);
      shifts.value = response.data;                 // 更新本地班別清單
    })
    .catch((error) => {
      console.error(error);                         // 記錄錯誤訊息
    });
  // .finally(() => {});
};

// ===== 生命週期鉤子 =====

/**
 * 組件掛載完成後的初始化處理
 * 設定管理員權限並載入班別清單資料
 */
onMounted(async () => {
  // 從使用者狀態管理取得管理員權限
  isAdmin.value = userStore.isAdmin;

  // 載入班別清單資料
  await getShifts();
  
  // 確保 DOM 更新完成
  nextTick();
});
</script>
<style scoped>
.norwap {
  white-space: nowrap !important;
}

h3 {
  color: blue;
}
</style>
