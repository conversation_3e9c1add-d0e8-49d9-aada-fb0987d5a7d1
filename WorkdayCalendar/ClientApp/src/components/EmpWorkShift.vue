<template>
  <div>
    <!-- 年份選擇標題區域 -->
    <h1>
      <!-- 前一年按鈕 -->
      <a @click="prevYear"><span class="pi pi-chevron-left p-link"></span></a>
      &nbsp;{{ year }} 年 &nbsp;
      <!-- 後一年按鈕 -->
      <a @click="nextYear" class="pi pi-chevron-right p-link"></a>
    </h1>
    <!-- 員工班別設定表單 -->
    <div class="p-card p-4">
      <div class="p-fluid">
        <!-- 選擇員工 -->
        <div class="p-field p-grid">
          <label for="employee" class="p-col-fixed" style="width: 150px">選擇員工：</label>
          <div class="p-col-fixed" style="width: 300px">
            <ChooseUser v-model="selectedEmpNo" v-on:change="onEmpChanged" :showName="true" :selectDept="true" />
          </div>
        </div>

        <!-- 選擇班別 -->
        <div class="p-field p-grid">
          <label for="shift" class="p-col-fixed" style="width: 150px">選擇班別：</label>
          <div class="p-col-fixed" style="width: 300px">
            <ChooseShift v-model="selectedShiftId" @shiftChanged="onShiftChanged" />
          </div>
        </div>

        <!-- 選擇開始日期 -->
        <div class="p-field p-grid">
          <label for="startDate" class="p-col-fixed" style="width: 150px">開始日期：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar id="startDate" v-model="startDate" dateFormat="yy/mm/dd" placeholder="選擇開始日期" :showIcon="true" />
          </div>
        </div>

        <!-- 選擇結束日期 -->
        <div class="p-field p-grid">
          <label for="endDate" class="p-col-fixed" style="width: 150px">結束日期：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar id="endDate" v-model="endDate" dateFormat="yy/mm/dd" placeholder="選擇結束日期" :showIcon="true" />
          </div>
        </div>

        <!-- 產生按鈕 -->
        <div class="p-field p-grid">
          <div class="p-col-fixed" style="width: 150px"></div>
          <div class="p-col-fixed" style="width: 300px">
            <Button label="產生員工班別" icon="pi pi-plus" class="p-button-raised p-button-success"
              @click="generateEmpWorkShift" :disabled="!canGenerate" />
          </div>
        </div>
      </div>
    </div>

    <!-- 現有員工班別清單 -->
    <div class="p-mt-4">
      <h3>現有員工班別清單</h3>
      <DataTable :value="filteredEmpWorkShifts" stripedRows responsiveLayout="scroll" class="p-datatable-customers"
        dataKey="ID">
        <Column field="EmpNo" header="員工編號"></Column>
        <Column field="ShiftId" header="班別編號"></Column>
        <Column field="WorkDate" header="工作日期">
          <template #body="slotProps">
            {{ formatDate(slotProps.data.WorkDate) }}
          </template>
        </Column>
        <Column :bodyStyle="{ textAlign: 'center', overflow: 'visible' }" class="nowrap">
          <template #body="slotProps">
            <Button type="button" v-if="isAdmin" @click="deleteEmpWorkShift(slotProps.data)" label="" alt="刪除"
              icon="pi pi-trash" class="p-button-danger p-button-raised p-button-rounded p-button-sm" />
          </template>
        </Column>
      </DataTable>
    </div>

    <!-- 警告對話框 -->
    <Dialog position="center" v-model:visible="showWarning" :modal="true">
      <template #header>
        <h3>警告</h3>
      </template>
      <p>{{ warningMessage }}</p>
      <template #footer>
        <Button label="確定" @click="showWarning = false" class="p-button-raised" />
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
// ===== Vue 3 Composition API 導入 =====
import { ref, computed, onMounted } from "vue";

// ===== PrimeVue 組件導入 =====
import { useToast } from "primevue/usetoast";
import { useConfirm } from "primevue/useconfirm";
import DataTable from "primevue/datatable";
import Column from "primevue/column";
import Calendar from "primevue/calendar";
import Dialog from "primevue/dialog";
import Button from "primevue/button";

// ===== HTTP 請求與 API 導入 =====
import axios from "axios";
import {
  empWorkShiftGetUrl,
  empWorkShiftAddUrl,
  empWorkShiftDeleteUrl,
  type EmpWorkShift,
  type EmpWorkShiftParam
} from "../components/api";

// ===== 自訂組件導入 =====
import ChooseUser from "./ChooseUser.vue";
import ChooseShift from "./ChooseShift.vue";

// ===== 狀態管理導入 =====
import { useAuthUserStore } from '../store/index';   // 使用者認證狀態管理

// ===== 初始化服務與全域狀態 =====
const userStore = useAuthUserStore();                // 使用者狀態管理實例
const toast = useToast();                           // Toast 通知服務實例
const confirm = useConfirm();                       // 確認對話框服務實例

// ===== 響應式狀態變數定義 =====

// --- 權限控制 ---
const isAdmin = ref(false);

// --- 表單資料 ---
const selectedEmpNo = ref<string>(""); // 目前選中的員工編號
const selectedShiftId = ref<number | null>(null); // 目前選中的班別編號
const startDate = ref<Date | undefined>(undefined); // 開始日期
const endDate = ref<Date | undefined>(undefined); // 結束日期
const year = ref<number>(userStore.Year);                    // 當前顯示的年份
// --- 資料管理 ---
const empWorkShifts = ref<Array<EmpWorkShift>>([]);

// --- 介面控制 ---
const showWarning = ref(false);
const warningMessage = ref("");

// ===== 計算屬性 =====

/**
 * 檢查是否可以產生員工班別
 * 需要選擇員工、班別、開始日期和結束日期
 */
const canGenerate = computed(() => {
  return selectedEmpNo.value !== "" &&
    selectedShiftId.value !== null &&
    startDate.value !== undefined &&
    endDate.value !== undefined &&
    startDate.value <= endDate.value;
});

/**
 * 篩選符合選定班別的員工班別資料
 * 只顯示與 selectedShiftId 相符的資料
 */
const filteredEmpWorkShifts = computed(() => {
  if (selectedShiftId.value === null) {
    return empWorkShifts.value; // 如果沒有選擇班別，顯示所有資料
  }
  return empWorkShifts.value.filter(shift => shift.ShiftId === selectedShiftId.value);
});

// ===== 方法定義 =====

/**
 * 顯示 Toast 通知訊息
 */
const toastMessage = (message: string): void => {
  toast.add({ severity: "info", summary: message, life: 3000 });
};
// ===== 年份操作函數 =====

/**
 * 切換到前一年
 * 將當前顯示年份減1並重新載入資料
 */
const prevYear = (): void => {
  year.value--;
  changeYear();
};

/**
 * 切換到後一年
 * 將當前顯示年份加1並重新載入資料
 */
const nextYear = (): void => {
  year.value++;
  changeYear();
};

/**
 * 年份變更處理
 * 當年份改變時，更新狀態管理中的年份並重新初始化所有相關資料
 */
const changeYear = (): void => {
  userStore.setYear(year.value);        // 更新狀態管理中的年份
  selectedShiftId.value = null;            // 清空班別選項
  // 重新載入所有相關資料
  getEmpWorkShifts(selectedEmpNo.value, year.value); // 重新載入員工班別清單
};
// ===== API 呼叫函數 =====

/**
 * 處理員工選擇改變事件
 */
const onEmpChanged = (event: { originalEvent: Event; value: string; }) => {
  // 當員工改變時，重新載入該員工的班別清單
  const empNo = event.value;
  if (empNo) {
    getEmpWorkShifts(empNo, year.value);
  } else {
    empWorkShifts.value = [];
  }
};

/**
 * 處理班別選擇改變事件
 */
const onShiftChanged = () => {
  // 班別選擇改變時的處理邏輯（如需要）
};

/**
 * 格式化日期顯示
 */
const formatDate = (date: Date | string): string => {
  if (!date) return "";
  const d = new Date(date);
  return d.toLocaleDateString('zh-TW');
};

/**
 * 產生員工班別設定
 */
const generateEmpWorkShift = async () => {
  if (!canGenerate.value) {
    showWarning.value = true;
    warningMessage.value = "請完整填寫所有必要欄位";
    return;
  }

  try {
    const param: EmpWorkShiftParam = {
      EmpNo: selectedEmpNo.value,
      ShiftId: selectedShiftId.value!,
      StartDate: startDate.value!,
      EndDate: endDate.value!
    };

    const response = await axios.post(empWorkShiftAddUrl, param);

    if (response.data) {
      toastMessage(`成功為員工 ${selectedEmpNo.value} 設定班別`);
      await getEmpWorkShifts(selectedEmpNo.value, year.value); // 重新載入清單

      // 清空表單
      selectedEmpNo.value = "";
      selectedShiftId.value = null;
      startDate.value = undefined;
      endDate.value = undefined;
    } else {
      toastMessage("設定失敗，請檢查資料是否正確");
    }
  } catch (error) {
    console.error("產生員工班別失敗:", error);
    toastMessage("產生員工班別失敗: " + error);
  }
};

/**
 * 刪除員工班別設定
 */
const deleteEmpWorkShift = (empWorkShift: EmpWorkShift) => {
  confirm.require({
    message: "刪除後無法復原，確定嗎？",
    header: "確認刪除",
    icon: "pi pi-exclamation-triangle",
    accept: async () => {
      try {

        let shift: EmpWorkShift = {
          EmpNo: empWorkShift.EmpNo,
          ShiftId: empWorkShift.ShiftId,
          WorkDate: empWorkShift.WorkDate
        };
        const response = await axios.delete(empWorkShiftDeleteUrl, { data: shift });
        if (response.data) {
          toastMessage("刪除成功");
          await getEmpWorkShifts(selectedEmpNo.value, year.value); // 重新載入清單
        } else {
          toastMessage("刪除失敗");
        }
      } catch (error) {
        console.error("刪除失敗:", error);
        toastMessage("刪除失敗: " + error);
      }
    }
  });
};

/**
 * 取得員工班別清單
 */
const getEmpWorkShifts = async (empNo: string, year: number) => {
  try {
    const response = await axios.get(`${empWorkShiftGetUrl}?empNo=${empNo}&year=${year}`);
    empWorkShifts.value = response.data;
  } catch (error) {
    console.error("載入員工班別清單失敗:", error);
  }
};

// ===== 生命週期鉤子 =====

/**
 * 組件掛載完成後的初始化處理
 */
onMounted(async () => {
  // 從使用者狀態管理取得管理員權限
  isAdmin.value = userStore.isAdmin;

  // 載入員工班別清單
  await getEmpWorkShifts(selectedEmpNo.value, year.value);
});






</script>

<style scoped>
.nowrap {
  white-space: nowrap !important;
}

h2,
h3 {
  color: blue;
}

.p-card {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1rem;
}
</style>