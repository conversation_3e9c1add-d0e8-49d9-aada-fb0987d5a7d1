<template>
  <div ref="container" :class="containerClass" @click="onClick($event)">
    <div class="p-hidden-accessible">
      <input ref="focusInput" type="text" :id="inputId" readonly :disabled="disabled" @focus="onFocus" @blur="onBlur"
        @keydown="onKeyDown" :tabindex="tabindex" aria-haspopup="true" :aria-expanded="overlayVisible"
        :aria-labelledby="ariaLabelledBy || undefined" v-bind="$attrs" />
    </div>
    <input v-if="editable" type="text" class="p-dropdown-label p-inputtext" :disabled="disabled" @focus="onFocus"
      @blur="onBlur" :placeholder="placeholder" :value="editableInputValue" @input="onEditableInput"
      aria-haspopup="listbox" :aria-expanded="overlayVisible" />
    <span v-if="!editable" :class="labelClass">
      <slot name="value" :value="modelValue" :placeholder="placeholder">{{ label }}</slot>
    </span>
    <em v-if="showClear && modelValue != null" class="p-dropdown-clear-icon pi pi-times"
      @click="onClearClick($event)"></em>
    <div class="p-dropdown-trigger" role="button" aria-haspopup="listbox" :aria-expanded="overlayVisible">
      <span :class="dropdownIconClass"></span>
    </div>
    <Teleport :to="appendTarget" :disabled="appendDisabled">
      <transition name="p-connected-overlay" @enter="onOverlayEnter" @leave="onOverlayLeave"
        @after-leave="onOverlayAfterLeave">
        <div :ref="overlayRef" :class="panelStyleClass" v-if="overlayVisible" @click="onOverlayClick">
          <slot name="header" :value="modelValue" :options="visibleOptions"></slot>
          <div class="p-dropdown-header" v-if="filter">
            <div class="p-dropdown-filter-container">
              <input type="text" ref="filterInput" v-model="filterValue" autoComplete="off"
                class="p-dropdown-filter p-inputtext p-component" :placeholder="filterPlaceholder"
                @keydown="onFilterKeyDown" @input="onFilterChange" />
              <span class="p-dropdown-filter-icon pi pi-search"></span>
            </div>
          </div>
          <div :ref="itemsWrapperRef" class="p-dropdown-items-wrapper" :style="{ 'max-height': scrollHeight }">
            <ul class="p-dropdown-items" role="listbox">
              <template v-if="!optionGroupLabel">
                <li v-for="(option, i) of visibleOptions"
                  :class="['p-dropdown-item', { 'p-highlight': isSelected(option), 'p-disabled': isOptionDisabled(option) }]"
                  v-ripple :key="getOptionRenderKey(option)" @click="onOptionSelect($event, option)" role="option"
                  :aria-label="getOptionLabel(option)" :aria-selected="isSelected(option)">
                  <slot name="option" :option="option" :index="i">{{ getOptionLabel(option) }}</slot>
                </li>
              </template>
              <template v-else>
                <template v-for="(optionGroup, i) of visibleOptions" :key="getOptionGroupRenderKey(optionGroup)">
                  <li class="p-dropdown-item-group">
                    <slot name="optiongroup" :option="optionGroup" :index="i">{{ getOptionGroupLabel(optionGroup) }}
                    </slot>
                  </li>
                  <li v-for="(option, i) of getOptionGroupChildren(optionGroup)"
                    :class="['p-dropdown-item', { 'p-highlight': isSelected(option), 'p-disabled': isOptionDisabled(option) }]"
                    v-ripple :key="getOptionRenderKey(option)" @click="onOptionSelect($event, option)" role="option"
                    :aria-label="getOptionLabel(option)" :aria-selected="isSelected(option)">
                    <slot name="option" :option="option" :index="i">{{ getOptionLabel(option) }}</slot>
                  </li>
                </template>
              </template>
              <li v-if="filterValue && (!visibleOptions || (visibleOptions && visibleOptions.length === 0))"
                class="p-dropdown-empty-message">
                <slot name="emptyfilter">{{ emptyFilterMessageText }}</slot>
              </li>
              <li v-else-if="!options || (options && options.length === 0)" class="p-dropdown-empty-message">
                <slot name="empty">{{ emptyMessageText }}</slot>
              </li>
            </ul>
          </div>
          <slot name="footer" :value="modelValue" :options="visibleOptions"></slot>
        </div>
      </transition>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeUnmount, getCurrentInstance } from 'vue';
import { ConnectedOverlayScrollHandler, ObjectUtils, DomHandler, ZIndexUtils } from "primevue/utils";
// @ts-ignore - OverlayEventBus doesn't have TypeScript declarations
import OverlayEventBus from "primevue/overlayeventbus";
import { FilterService } from "primevue/api";
import Ripple from "primevue/ripple";

// Props 屬性介面定義
interface Props {
  modelValue?: any;                    // 選中的值
  options?: any[];                     // 下拉選項陣列
  optionLabel?: string | null;         // 選項標籤欄位名稱
  optionValue?: string | null;         // 選項值欄位名稱  
  optionDisabled?: string | null;      // 選項禁用狀態欄位名稱
  optionGroupLabel?: string | null;    // 選項群組標籤欄位名稱
  optionGroupChildren?: string | null; // 選項群組子項目欄位名稱
  scrollHeight?: string;               // 下拉面板最大捲動高度
  filter?: boolean;                    // 是否啟用篩選功能
  filterPlaceholder?: string;          // 篩選輸入框提示文字
  filterLocale?: string;               // 篩選語言區域設定
  filterMatchMode?: string;            // 篩選匹配模式
  filterFields?: string[] | null;      // 篩選欄位陣列
  shrinkLabel?: boolean;               // 是否縮短標籤顯示
  editable?: boolean;                  // 是否可編輯
  placeholder?: string;                // 佔位提示文字
  disabled?: boolean;                  // 是否禁用
  dataKey?: string | null;             // 資料鍵值欄位名稱
  showClear?: boolean;                 // 是否顯示清除按鈕
  inputId?: string;                    // 輸入框 ID
  tabindex?: string;                   // Tab 索引
  ariaLabelledBy?: string | null;      // ARIA 標籤關聯
  appendTo?: string;                   // 面板附加目標
  emptyFilterMessage?: string | null;  // 篩選無結果訊息
  emptyMessage?: string | null;        // 無資料訊息
  panelClass?: any;                    // 面板自訂樣式類別
  loading?: boolean;                   // 載入狀態
  loadingIcon?: string;                // 載入圖示
}

// Props 預設值設定
const props = withDefaults(defineProps<Props>(), {
  scrollHeight: "200px",              // 預設捲動高度
  filterMatchMode: "contains",        // 預設篩選模式為包含
  filterFields: null,                 // 預設無篩選欄位
  shrinkLabel: false,                 // 預設不縮短標籤
  appendTo: "body",                   // 預設附加到 body
  emptyFilterMessage: null,           // 預設無篩選空訊息
  emptyMessage: null,                 // 預設無空訊息
  loading: false,                     // 預設非載入狀態
  loadingIcon: "pi pi-spinner pi-spin" // 預設載入圖示
});

// 事件發射定義
const emit = defineEmits<{
  'update:modelValue': [value: any];                           // 更新選中值
  'before-show': [];                                           // 面板顯示前
  'before-hide': [];                                           // 面板隱藏前
  'show': [];                                                  // 面板已顯示
  'hide': [];                                                  // 面板已隱藏
  'change': [event: { originalEvent: Event; value: any; }];    // 選項變更
  'filter': [event: { originalEvent: Event; value: string; }]; // 篩選事件
  'focus': [event: Event];                                     // 獲得焦點
  'blur': [event: Event];                                      // 失去焦點
}>();

// 響應式資料
const focused = ref(false);          // 是否獲得焦點
const filterValue = ref<string | null>(null); // 篩選值
const overlayVisible = ref(false);   // 下拉面板是否可見

// 非響應式變數
let outsideClickListener: ((event: Event) => void) | null = null; // 外部點擊監聽器
let scrollHandler: ConnectedOverlayScrollHandler | null = null;   // 捲動處理器
let resizeListener: (() => void) | null = null;                   // 視窗調整監聽器
let searchTimeout: NodeJS.Timeout | null = null;                  // 搜尋超時計時器
let currentSearchChar: string | null = null;                      // 當前搜尋字元
let previousSearchChar: string | null = null;                     // 前一個搜尋字元
let searchValue: string | null = null;                            // 搜尋值
let overlay: HTMLElement | null = null;                           // 下拉面板元素
let itemsWrapper: HTMLElement | null = null;                      // 選項容器元素

// 模板引用
const container = ref<HTMLElement>();     // 容器元素引用
const focusInput = ref<HTMLInputElement>(); // 焦點輸入框引用
const filterInput = ref<HTMLInputElement>(); // 篩選輸入框引用

// Vue 實例
const instance = getCurrentInstance();

// 方法定義

/**
 * 取得選項標籤文字
 * @param option 選項物件
 * @returns 標籤文字
 */
const getOptionLabel = (option: any) => {
  return props.optionLabel ? ObjectUtils.resolveFieldData(option, props.optionLabel) : option;
};

/**
 * 取得選項值
 * @param option 選項物件
 * @returns 選項值
 */
const getOptionValue = (option: any) => {
  return props.optionValue ? ObjectUtils.resolveFieldData(option, props.optionValue) : option;
};

/**
 * 取得選項渲染鍵值
 * @param option 選項物件
 * @returns 渲染鍵值
 */
const getOptionRenderKey = (option: any) => {
  return props.dataKey ? ObjectUtils.resolveFieldData(option, props.dataKey) : getOptionLabel(option);
};

/**
 * 檢查選項是否被禁用
 * @param option 選項物件
 * @returns 是否禁用
 */
const isOptionDisabled = (option: any) => {
  return props.optionDisabled ? ObjectUtils.resolveFieldData(option, props.optionDisabled) : false;
};

/**
 * 取得選項群組渲染鍵值
 * @param optionGroup 選項群組物件
 * @returns 群組渲染鍵值
 */
const getOptionGroupRenderKey = (optionGroup: any) => {
  return ObjectUtils.resolveFieldData(optionGroup, props.optionGroupLabel || '');
};

/**
 * 取得選項群組標籤
 * @param optionGroup 選項群組物件
 * @returns 群組標籤
 */
const getOptionGroupLabel = (optionGroup: any) => {
  return ObjectUtils.resolveFieldData(optionGroup, props.optionGroupLabel || '');
};

/**
 * 取得選項群組的子項目
 * @param optionGroup 選項群組物件
 * @returns 子項目陣列
 */
const getOptionGroupChildren = (optionGroup: any) => {
  return ObjectUtils.resolveFieldData(optionGroup, props.optionGroupChildren || '');
};

/**
 * 取得目前選中的選項
 * @returns 選中的選項物件或 null
 */
const getSelectedOption = () => {
  const index = getSelectedOptionIndex();
  if (typeof index === 'number' && index !== -1) {
    return props.optionGroupLabel
      ? null // 群組模式下此情況不應發生
      : props.options![index];
  } else if (typeof index === 'object' && index.group !== -1) {
    return getOptionGroupChildren(props.options![index.group])[index.option];
  }
  return null;
};

/**
 * 取得選中選項的索引位置
 * @returns 索引數字或群組索引物件
 */
const getSelectedOptionIndex = (): number | { group: number; option: number; } => {
  if (props.modelValue != null && props.options) {
    if (props.optionGroupLabel) {
      // 群組模式：遍歷每個群組尋找匹配項
      for (let i = 0; i < props.options.length; i++) {
        const selectedOptionIndex = findOptionIndexInList(props.modelValue, getOptionGroupChildren(props.options[i]));
        if (selectedOptionIndex !== -1) {
          return { group: i, option: selectedOptionIndex };
        }
      }
    } else {
      // 非群組模式：直接在選項列表中尋找
      return findOptionIndexInList(props.modelValue, props.options);
    }
  }
  return -1;
};

/**
 * 在指定列表中尋找選項索引
 * @param value 要尋找的值
 * @param list 選項列表
 * @returns 索引位置或 -1
 */
const findOptionIndexInList = (value: any, list: any[]) => {
  for (let i = 0; i < list.length; i++) {
    if ((ObjectUtils as any).equals(value, getOptionValue(list[i]), equalityKey.value)) {
      return i;
    }
  }
  return -1;
};

/**
 * 檢查選項是否被選中
 * @param option 選項物件
 * @returns 是否選中
 */
const isSelected = (option: any) => {
  return (ObjectUtils as any).equals(props.modelValue, getOptionValue(option), equalityKey.value);
};

/**
 * 顯示下拉面板
 */
const show = () => {
  emit("before-show");
  overlayVisible.value = true;
};

/**
 * 隱藏下拉面板
 */
const hide = () => {
  emit("before-hide");
  overlayVisible.value = false;
};

/**
 * 處理焦點事件
 * @param event 事件物件
 */
const onFocus = (event: Event) => {
  focused.value = true;
  emit("focus", event);
};

/**
 * 處理失焦事件
 * @param event 事件物件
 */
const onBlur = (event: Event) => {
  focused.value = false;
  emit("blur", event);
};

/**
 * 處理鍵盤按下事件
 * @param event 鍵盤事件
 */
const onKeyDown = (event: KeyboardEvent) => {
  switch (event.which) {
    case 40: // 下箭頭鍵
      onDownKey(event);
      break;
    case 38: // 上箭頭鍵
      onUpKey(event);
      break;
    case 32: // 空白鍵
      if (!overlayVisible.value) {
        show();
        event.preventDefault();
      }
      break;
    case 13: // Enter 鍵
    case 27: // Escape 鍵
      if (overlayVisible.value) {
        hide();
        event.preventDefault();
      }
      break;
    case 9: // Tab 鍵
      hide();
      break;
    default:
      search(event); // 其他鍵進行搜尋
      break;
  }
};

/**
 * 處理篩選輸入框的鍵盤事件
 * @param event 鍵盤事件
 */
const onFilterKeyDown = (event: KeyboardEvent) => {
  switch (event.which) {
    case 40: // 下箭頭鍵
      onDownKey(event);
      break;
    case 38: // 上箭頭鍵
      onUpKey(event);
      break;
    case 13: // Enter 鍵
    case 27: // Escape 鍵
      overlayVisible.value = false;
      event.preventDefault();
      break;
    default:
      break;
  }
};

/**
 * 處理下箭頭鍵事件
 * @param event 鍵盤事件
 */
const onDownKey = (event: KeyboardEvent) => {
  if (visibleOptions.value) {
    if (!overlayVisible.value && event.altKey) {
      show(); // Alt + 下箭頭顯示面板
    } else {
      // 尋找下一個選項
      const nextOption =
        visibleOptions.value && visibleOptions.value.length > 0 ? findNextOption(getSelectedOptionIndex()) : null;
      if (nextOption) {
        updateModel(event, getOptionValue(nextOption));
      }
    }
  }
  event.preventDefault();
};

/**
 * 處理上箭頭鍵事件
 * @param event 鍵盤事件
 */
const onUpKey = (event: KeyboardEvent) => {
  if (visibleOptions.value) {
    // 尋找上一個選項
    const prevOption = findPrevOption(getSelectedOptionIndex());
    if (prevOption) {
      updateModel(event, getOptionValue(prevOption));
    }
  }
  event.preventDefault();
};

/**
 * 尋找下一個可用選項
 * @param index 當前索引
 * @returns 下一個選項或 null
 */
const findNextOption = (index: any) => {
  if (props.optionGroupLabel) {
    // 群組模式處理
    const groupIndex = index === -1 ? 0 : (typeof index === 'object' ? index.group : 0);
    const optionIndex = index === -1 ? -1 : (typeof index === 'object' ? index.option : -1);
    const option = findNextOptionInList(getOptionGroupChildren(visibleOptions.value![groupIndex]), optionIndex);

    if (option) return option;
    else if (groupIndex + 1 !== visibleOptions.value!.length)
      return findNextOption({ group: groupIndex + 1, option: -1 });
    else return null;
  } else {
    // 非群組模式處理
    return findNextOptionInList(visibleOptions.value!, typeof index === 'number' ? index : -1);
  }
};

/**
 * 在列表中尋找下一個可用選項
 * @param list 選項列表
 * @param index 當前索引
 * @returns 下一個選項或 null
 */
const findNextOptionInList = (list: any[], index: number) => {
  const i = index + 1;
  if (i === list.length) {
    return null;
  }

  const option = list[i];
  if (isOptionDisabled(option))
    return findNextOptionInList(list, i); // 跳過禁用選項
  else
    return option;
};

/**
 * 尋找上一個可用選項
 * @param index 當前索引
 * @returns 上一個選項或 null
 */
const findPrevOption = (index: any) => {
  if (index === -1) {
    return null;
  }

  if (props.optionGroupLabel) {
    // 群組模式處理
    const indexObj = typeof index === 'object' ? index : { group: 0, option: index };
    const groupIndex = indexObj.group;
    const optionIndex = indexObj.option;
    const option = findPrevOptionInList(getOptionGroupChildren(visibleOptions.value![groupIndex]), optionIndex);

    if (option) return option;
    else if (groupIndex > 0)
      return findPrevOption({
        group: groupIndex - 1,
        option: getOptionGroupChildren(visibleOptions.value![groupIndex - 1]).length,
      });
    else return null;
  } else {
    // 非群組模式處理
    return findPrevOptionInList(visibleOptions.value!, typeof index === 'number' ? index : -1);
  }
};

/**
 * 在列表中尋找上一個可用選項
 * @param list 選項列表
 * @param index 當前索引
 * @returns 上一個選項或 null
 */
const findPrevOptionInList = (list: any[], index: number) => {
  const i = index - 1;
  if (i < 0) {
    return null;
  }

  const option = list[i];
  if (isOptionDisabled(option))
    return findPrevOptionInList(list, i); // 跳過禁用選項
  else
    return option;
};

/**
 * 處理清除按鈕點擊事件
 * @param event 事件物件
 */
const onClearClick = (event: Event) => {
  updateModel(event, null);
};

/**
 * 處理主要點擊事件
 * @param event 事件物件
 */
const onClick = (event: Event) => {
  if (props.disabled || props.loading) {
    return;
  }

  const target = event.target as HTMLElement;
  // 點擊清除圖示或輸入框時不處理
  if (DomHandler.hasClass(target, "p-dropdown-clear-icon") || target.tagName === "INPUT") {
    return;
  } else if (!overlay || !overlay.contains(target)) {
    // 切換面板顯示/隱藏狀態
    if (overlayVisible.value) hide();
    else show();

    focusInput.value?.focus();
  }
};

/**
 * 處理選項選擇事件
 * @param event 事件物件
 * @param option 選中的選項
 */
const onOptionSelect = (event: Event, option: any) => {
  const value = getOptionValue(option);
  updateModel(event, value);
  focusInput.value?.focus();

  // 延遲隱藏面板以確保動畫流暢
  setTimeout(() => {
    hide();
  }, 200);
};

/**
 * 處理可編輯輸入事件
 * @param event 事件物件
 */
const onEditableInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  emit("update:modelValue", target.value);
};

/**
 * 面板進入動畫完成時的處理
 * @param el 面板元素
 */
const onOverlayEnter = (el: Element) => {
  const htmlEl = el as HTMLElement;
  // 設定 z-index 層級
  ZIndexUtils.set("overlay", htmlEl, (instance?.appContext.config.globalProperties.$primevue as any).config.zIndex.overlay);
  scrollValueInView();     // 滾動到選中項
  alignOverlay();          // 對齊面板位置
  bindOutsideClickListener();  // 綁定外部點擊監聽
  bindScrollListener();    // 綁定滾動監聽
  bindResizeListener();    // 綁定視窗調整監聽

  // 如果啟用篩選，自動聚焦到篩選輸入框
  if (props.filter) {
    filterInput.value?.focus();
  }

  emit("show");
};

/**
 * 面板離開動畫開始時的處理
 */
const onOverlayLeave = () => {
  unbindOutsideClickListener(); // 解除外部點擊監聽
  unbindScrollListener();       // 解除滾動監聽
  unbindResizeListener();       // 解除視窗調整監聽
  emit("hide");
  itemsWrapper = null;
  overlay = null;
};

/**
 * 面板離開動畫完成後的處理
 * @param el 面板元素
 */
const onOverlayAfterLeave = (el: Element) => {
  ZIndexUtils.clear(el as HTMLElement);
};

/**
 * 對齊面板位置
 */
const alignOverlay = () => {
  if (appendDisabled.value) {
    // 相對定位模式
    DomHandler.relativePosition(overlay!, container.value!);
  } else {
    // 絕對定位模式
    overlay!.style.minWidth = DomHandler.getOuterWidth(container.value!, true) + "px";
    DomHandler.absolutePosition(overlay!, container.value!);
  }
};

/**
 * 更新模型值並發射事件
 * @param event 事件物件
 * @param value 新值
 */
const updateModel = (event: Event, value: any) => {
  emit("update:modelValue", value);
  emit("change", { originalEvent: event, value: value });
};

/**
 * 綁定外部點擊監聽器
 */
const bindOutsideClickListener = () => {
  if (!outsideClickListener) {
    outsideClickListener = (event: Event) => {
      const target = event.target as HTMLElement;
      // 點擊面板外部時隱藏面板
      if (overlayVisible.value && overlay && !container.value!.contains(target) && !overlay.contains(target)) {
        hide();
      }
    };
    document.addEventListener("click", outsideClickListener);
  }
};

/**
 * 解除外部點擊監聽器
 */
const unbindOutsideClickListener = () => {
  if (outsideClickListener) {
    document.removeEventListener("click", outsideClickListener);
    outsideClickListener = null;
  }
};

/**
 * 綁定滾動監聽器
 */
const bindScrollListener = () => {
  if (!scrollHandler) {
    scrollHandler = new ConnectedOverlayScrollHandler(container.value!, () => {
      if (overlayVisible.value) {
        hide();
      }
    });
  }

  scrollHandler.bindScrollListener();
};

/**
 * 解除滾動監聽器
 */
const unbindScrollListener = () => {
  if (scrollHandler) {
    scrollHandler.unbindScrollListener();
  }
};

/**
 * 綁定視窗調整監聽器
 */
const bindResizeListener = () => {
  if (!resizeListener) {
    resizeListener = () => {
      // 視窗調整時隱藏面板（Android 裝置除外）
      if (overlayVisible.value && !DomHandler.isAndroid()) {
        hide();
      }
    };
    window.addEventListener("resize", resizeListener);
  }
};

/**
 * 解除視窗調整監聽器
 */
const unbindResizeListener = () => {
  if (resizeListener) {
    window.removeEventListener("resize", resizeListener);
    resizeListener = null;
  }
};

/**
 * 鍵盤搜尋功能
 * @param event 鍵盤事件
 */
const search = (event: KeyboardEvent) => {
  if (!visibleOptions.value) {
    return;
  }

  // 清除之前的搜尋計時器
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }

  const char = String.fromCharCode(event.keyCode);
  previousSearchChar = currentSearchChar;
  currentSearchChar = char;

  // 連續輸入相同字元時重置搜尋值，否則累加
  if (previousSearchChar === currentSearchChar)
    searchValue = currentSearchChar;
  else
    searchValue = searchValue ? searchValue + char : char;

  if (searchValue) {
    let searchIndex = getSelectedOptionIndex();
    const newOption = props.optionGroupLabel ?
      searchOptionInGroup(searchIndex) :
      searchOption(typeof searchIndex === 'number' ? searchIndex + 1 : 0);

    if (newOption) {
      updateModel(event, getOptionValue(newOption));
    }
  }

  // 設定搜尋超時，250ms 後清除搜尋值
  searchTimeout = setTimeout(() => {
    searchValue = null;
  }, 250);
};

/**
 * 在選項中搜尋匹配項
 * @param index 開始搜尋的索引
 * @returns 匹配的選項或 null
 */
const searchOption = (index: number) => {
  let option;

  if (searchValue) {
    // 先從指定索引往後搜尋
    option = searchOptionInRange(index, visibleOptions.value!.length);

    // 如果沒找到，從頭開始搜尋到指定索引
    if (!option) {
      option = searchOptionInRange(0, index);
    }
  }

  return option;
};

/**
 * 在指定範圍內搜尋選項
 * @param start 開始索引
 * @param end 結束索引
 * @returns 匹配的選項或 null
 */
const searchOptionInRange = (start: number, end: number) => {
  for (let i = start; i < end; i++) {
    const opt = visibleOptions.value![i];
    if (matchesSearchValue(opt)) {
      return opt;
    }
  }

  return null;
};

/**
 * 在群組中搜尋選項
 * @param index 當前索引
 * @returns 匹配的選項或 null
 */
const searchOptionInGroup = (index: any) => {
  const searchIndex = index === -1 ? { group: 0, option: -1 } :
    (typeof index === 'object' ? index : { group: 0, option: index });

  // 從當前位置往後搜尋
  for (let i = searchIndex.group; i < visibleOptions.value!.length; i++) {
    const groupOptions = getOptionGroupChildren(visibleOptions.value![i]);
    for (let j = searchIndex.group === i ? searchIndex.option + 1 : 0; j < groupOptions.length; j++) {
      if (matchesSearchValue(groupOptions[j])) {
        return groupOptions[j];
      }
    }
  }

  // 從頭開始搜尋到當前位置
  for (let i = 0; i <= searchIndex.group; i++) {
    const groupOptions = getOptionGroupChildren(visibleOptions.value![i]);
    for (let j = 0; j < (searchIndex.group === i ? searchIndex.option : groupOptions.length); j++) {
      if (matchesSearchValue(groupOptions[j])) {
        return groupOptions[j];
      }
    }
  }

  return null;
};

/**
 * 檢查選項是否匹配搜尋值
 * @param option 選項物件
 * @returns 是否匹配
 */
const matchesSearchValue = (option: any) => {
  const label = getOptionLabel(option).toLocaleLowerCase(props.filterLocale);
  return label.startsWith(searchValue!.toLocaleLowerCase(props.filterLocale));
};

/**
 * 處理篩選輸入變更事件
 * @param event 事件物件
 */
const onFilterChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  emit("filter", { originalEvent: event, value: target.value });
  if (overlayVisible.value) {
    alignOverlay(); // 重新對齊面板位置
  }
};

/**
 * 面板元素引用回調
 * @param el 面板元素
 */
const overlayRef = (el: any) => {
  overlay = el as HTMLElement;
};

/**
 * 選項容器元素引用回調
 * @param el 容器元素
 */
const itemsWrapperRef = (el: any) => {
  itemsWrapper = el as HTMLElement;
};

/**
 * 滾動到選中項目的位置
 */
const scrollValueInView = () => {
  if (overlay) {
    const selectedItem = DomHandler.findSingle(overlay, "li.p-highlight");
    if (selectedItem) {
      itemsWrapper!.scrollTop = selectedItem.offsetTop;
    }
  }
};

/**
 * 處理面板點擊事件
 * @param event 事件物件
 */
const onOverlayClick = (event: Event) => {
  OverlayEventBus.emit("overlay-click", {
    originalEvent: event,
    target: container.value,
  });
};

// 計算屬性

/**
 * 可見選項列表（經過篩選）
 */
const visibleOptions = computed(() => {
  if (filterValue.value) {
    if (props.optionGroupLabel) {
      // 群組模式篩選
      const filteredGroups = [];
      for (const optgroup of props.options!) {
        const filteredSubOptions = FilterService.filter?.(
          getOptionGroupChildren(optgroup),
          searchFields.value,
          filterValue.value,
          props.filterMatchMode,
          props.filterLocale || 'en'
        );
        if (filteredSubOptions && filteredSubOptions.length) {
          const filteredGroup = { ...optgroup };
          filteredGroup[props.optionGroupChildren!] = filteredSubOptions;
          filteredGroups.push(filteredGroup);
        }
      }
      return filteredGroups;
    } else {
      // 非群組模式篩選
      return FilterService.filter?.(props.options!, searchFields.value, filterValue.value, "contains", props.filterLocale || 'en') || [];
    }
  } else {
    return props.options;
  }
});

/**
 * 容器樣式類別
 */
const containerClass = computed(() => [
  "p-dropdown p-component p-inputwrapper",
  {
    "p-disabled": props.disabled,                                    // 禁用狀態
    "p-dropdown-clearable": props.showClear && !props.disabled,     // 可清除狀態
    "p-focus": focused.value,                                        // 焦點狀態
    "p-inputwrapper-filled": props.modelValue,                       // 已填入狀態
    "p-inputwrapper-focus": focused.value || overlayVisible.value,   // 焦點或面板開啟狀態
  },
]);

/**
 * 標籤樣式類別
 */
const labelClass = computed(() => [
  "p-dropdown-label p-inputtext",
  {
    "p-placeholder": label.value === props.placeholder,              // 佔位提示狀態
    "p-dropdown-label-empty": !(instance?.slots as any)["value"] &&
      (label.value === "p-emptylabel" || label.value.length === 0), // 空標籤狀態
  },
]);

/**
 * 面板樣式類別
 */
const panelStyleClass = computed(() => [
  "p-dropdown-panel p-component",
  props.panelClass,
  {
    "p-input-filled": (instance?.appContext.config.globalProperties.$primevue as any).config.inputStyle === "filled",
    "p-ripple-disabled": (instance?.appContext.config.globalProperties.$primevue as any).config.ripple === false,
  },
]);

/**
 * 顯示標籤文字
 */
const label = computed(() => {
  const selectedOption = getSelectedOption();

  if (selectedOption) {
    if (props.shrinkLabel) {
      return getOptionValue(selectedOption) || "p-emptylabel";
    } else {
      return getOptionLabel(selectedOption);
    }
  } else {
    return props.placeholder || "p-emptylabel";
  }
});

/**
 * 可編輯輸入框的值
 */
const editableInputValue = computed(() => {
  const selectedOption = getSelectedOption();
  if (selectedOption) return getOptionLabel(selectedOption);
  else return props.modelValue;
});

/**
 * 相等性比較鍵值
 */
const equalityKey = computed(() => {
  return props.optionValue ? null : props.dataKey;
});

/**
 * 搜尋欄位陣列
 */
const searchFields = computed(() => {
  return props.filterFields || [props.optionLabel];
});

/**
 * 篩選無結果時的訊息文字
 */
const emptyFilterMessageText = computed(() => {
  return props.emptyFilterMessage || (instance?.appContext.config.globalProperties.$primevue as any).config.locale.emptyFilterMessage;
});

/**
 * 無資料時的訊息文字
 */
const emptyMessageText = computed(() => {
  return props.emptyMessage || (instance?.appContext.config.globalProperties.$primevue as any).config.locale.emptyMessage;
});

/**
 * 是否禁用附加功能
 */
const appendDisabled = computed(() => {
  return props.appendTo === "self";
});

/**
 * 附加目標元素
 */
const appendTarget = computed(() => {
  return appendDisabled.value ? null : props.appendTo;
});

/**
 * 下拉圖示樣式類別
 */
const dropdownIconClass = computed(() => {
  return ["p-dropdown-trigger-icon", props.loading ? props.loadingIcon : "pi pi-chevron-down"];
});

// 組件卸載前的清理工作
onBeforeUnmount(() => {
  unbindOutsideClickListener(); // 解除外部點擊監聽
  unbindResizeListener();       // 解除視窗調整監聽

  // 銷毀滾動處理器
  if (scrollHandler) {
    scrollHandler.destroy();
    scrollHandler = null;
  }

  itemsWrapper = null;

  // 清理面板 z-index
  if (overlay) {
    ZIndexUtils.clear(overlay);
    overlay = null;
  }
});

// 定義組件名稱用於偵錯
defineOptions({
  name: 'MyDropdown'
});
</script>

<style>
.p-dropdown {
  display: inline-flex;
  cursor: pointer;
  position: relative;
  user-select: none;
}

.p-dropdown-clear-icon {
  position: absolute;
  top: 50%;
  margin-top: -0.5rem;
}

.p-dropdown-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.p-dropdown-label {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  flex: 1 1 auto;
  width: 1%;
  text-overflow: ellipsis;
  cursor: pointer;
}

.p-dropdown-label-empty {
  overflow: hidden;
  visibility: hidden;
}

input.p-dropdown-label {
  cursor: default;
}

.p-dropdown .p-dropdown-panel {
  min-width: 100%;
}

.p-dropdown-panel {
  position: absolute;
  top: 0;
  left: 0;
}

.p-dropdown-items-wrapper {
  overflow: auto;
}

.p-dropdown-item {
  cursor: pointer;
  font-weight: normal;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.p-dropdown-item-group {
  cursor: auto;
}

.p-dropdown-items {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

.p-dropdown-filter {
  width: 100%;
}

.p-dropdown-filter-container {
  position: relative;
}

.p-dropdown-filter-icon {
  position: absolute;
  top: 50%;
  margin-top: -0.5rem;
}

.p-fluid .p-dropdown {
  display: flex;
}

.p-fluid .p-dropdown .p-dropdown-label {
  width: 1%;
}
</style>
