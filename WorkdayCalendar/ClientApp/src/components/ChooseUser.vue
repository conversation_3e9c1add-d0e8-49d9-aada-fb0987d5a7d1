/* eslint-disable vue/no-mutating-props */
<template>
  <div class="choose-user-container">
    <MyDropdown v-model="empNo" :options="deptUsers" optionLabel="CName" :class="cssClass" :filter="true"
      optionValue="EmpNo" @change="onChange" :shrinkLabel="!showName" placeholder="選擇員工" />
    <Button v-if="selectDept" icon="pi pi-ellipsis-h" class="p-button-raised p-button-rounded choose-button"
      @click="choose" />
    <Dialog header="選擇員工" position="center" :modal="true" v-model:visible="showDialog" :style="{ width: '60vw' }">
      部門
      <Dropdown v-model="selectedDept" :options="depts" :class="cssClass" optionLabel="DeptName" optionValue="DeptNo"
        data-key="DeptNo" @change="onChangeDept" placeholder="選擇部門" />
      <span v-if="teams.length > 2">
        組別
        <Dropdown v-model="selectedTeam" :options="teams" :class="cssClass" optionLabel="TeamCName" optionValue="TeamID"
          data-key="TeamID" @change="onChangeTeam" placeholder="選擇組別" />
      </span>
      員工
      <Dropdown v-model="selectedEmpNo" :options="deptUsers" :class="cssClass" :filter="true" optionLabel="CName"
        optionValue="EmpNo" datekey="EmpNo" @change="onChooseEmp" placeholder="選擇員工" />
    </Dialog>
  </div>
</template>

<script setup lang="ts">
// 匯入所需的 Vue 組件和函式
import Dialog from "primevue/dialog";
import { ref, onMounted, watch } from "vue";
import { getUsersUrl } from "./api";
import axios from "axios";
import Dropdown from "primevue/dropdown";
import MyDropdown from "../components/MyDropdown.vue";
import type { Employee } from "../types/Employee";

// 部門介面定義
interface Dept {
  DeptNo: number;    // 部門編號
  DeptName: string;  // 部門名稱
}

// 組別介面定義
interface Team {
  TeamID: number;      // 組別 ID
  TeamCName: string;   // 組別中文名稱
}

// 定義 Props 介面
interface Props {
  detail?: any;        // 詳細資料物件（用於回填資料）
  showEmpNo?: boolean; // 是否顯示員工編號
  cssClass?: string;   // CSS 類別名稱
  deptNo?: number;     // 部門編號（預設篩選條件）
  showName?: boolean;  // 是否顯示員工姓名
  selectDept?: boolean; // 是否顯示部門選擇按鈕
  modelValue?: string; // v-model 綁定的值（員工編號）
}

// 定義 Props 並設定預設值
const props = withDefaults(defineProps<Props>(), {
  detail: null,
  showEmpNo: true,
  cssClass: "",
  deptNo: 0,
  showName: true,
  selectDept: true,
  modelValue: "",
});

// 定義 Emit 事件
const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;  // 更新 v-model 值
  (e: "change", event: { originalEvent: Event; value: string; }): void;  // 值改變事件
}>();

// 響應式資料定義
const users = ref<Array<Employee>>([]);           // 所有員工資料
const empNo = ref<string>("");                    // 目前選中的員工編號
const showDialog = ref<boolean>(false);           // 是否顯示選擇對話框
const depts = ref<Array<Dept>>([{ DeptNo: 0, DeptName: "全部" }]);  // 部門列表
const teams = ref<Array<Team>>([                  // 組別列表
  { TeamID: 0, TeamCName: "全部" },
  { TeamID: 1, TeamCName: "未分組" },
]);
const allTeams = ref<Array<Team>>([{ TeamID: 0, TeamCName: "全部" }]);  // 所有組別的完整列表
const selectedDept = ref<number>(props.deptNo);   // 對話框中選中的部門
const selectedTeam = ref<number>(0);              // 對話框中選中的組別
const selectedEmpNo = ref<string>("");            // 對話框中選中的員工編號
const deptUsers = ref<Array<Employee>>([]);       // 依據篩選條件顯示的員工列表

// 方法定義

/**
 * 開啟員工選擇對話框
 */
const choose = (): void => {
  //selectedDept.value = 0;  // 不重置部門選擇
  selectedTeam.value = 0;    // 重置組別選擇為「全部」

  // 如果目前選擇的是「全部」部門，顯示所有員工
  if (selectedDept.value === 0) {
    deptUsers.value = users.value;
    teams.value = allTeams.value;
  } else {
    // 否則根據部門篩選員工
    onChangeDept();
  }

  selectedEmpNo.value = "";     // 清空員工選擇
  showDialog.value = true;      // 顯示選擇對話框
};

/**
 * 處理員工選擇改變事件
 * @param event - 包含原始事件和值的物件
 */
const onChange = (event: { originalEvent: Event; value: any; }): void => {
  // 向父組件發送更新事件
  emit("update:modelValue", empNo.value);
  emit("change", { originalEvent: event.originalEvent, value: empNo.value });

  // 如果有傳入詳細資料物件，則更新相關資訊
  if (props.detail !== null) {
    deptUsers.value.forEach((element: Employee) => {
      if (element.EmpNo === empNo.value) {
        // 更新職位名稱
        // eslint-disable-next-line vue/no-mutating-props
        props.detail.RankName = element.RankName;
        // 更新部門簡稱
        // eslint-disable-next-line vue/no-mutating-props
        props.detail.DeptSName = element.DeptSName;
        // 以下欄位暫時註解掉
        // eslint-disable-next-line vue/no-mutating-props
        // props.detail.DeptNo = element.DeptNo;
        // eslint-disable-next-line vue/no-mutating-props
        // props.detail.CName = element.CName;
        return;
      }
    });
  }
};

/**
 * 處理對話框中的員工選擇事件
 * @param event - 包含原始事件和值的物件
 */
const onChooseEmp = (event: { originalEvent: Event; value: string; }): void => {
  // 如果有選擇員工
  if (selectedEmpNo.value !== "") {
    empNo.value = selectedEmpNo.value;  // 設定選中的員工編號
    onChange(event);                    // 觸發改變事件
    selectedDept.value = 0;             // 重置部門選擇
    showDialog.value = false;           // 關閉對話框
  }
};

/**
 * 處理部門選擇改變事件
 */
const onChangeDept = (): void => {
  if (selectedDept.value !== 0) {
    // 如果選擇了特定部門
    deptUsers.value = [];  // 清空員工列表

    // 重置組別選項為基本選項
    teams.value = [
      { TeamID: 0, TeamCName: "全部" },
      { TeamID: 1, TeamCName: "未分組" },
    ];

    const teamsMap = new Map<number, string>();  // 用於收集該部門的所有組別

    // 篩選指定部門的員工並收集組別資訊
    users.value.forEach((element: Employee) => {
      if (element.DeptNo === selectedDept.value) {
        deptUsers.value.push(element);
        // 如果員工有組別資訊，加入組別地圖
        if (element.TeamID !== null && element.TeamCName !== null) {
          teamsMap.set(element.TeamID, element.TeamCName);
        }
      }
    });

    // 將收集到的組別加入選項列表
    teamsMap.forEach((value: string, key: number) => {
      teams.value.push({ TeamID: key, TeamCName: value });
    });

    // 如果目前選中的組別在新部門中不存在，重置為「全部」
    if (teamsMap.get(selectedTeam.value) === undefined) {
      selectedTeam.value = 0;
    }
  } else {
    // 如果選擇「全部」部門
    deptUsers.value = users.value;      // 顯示所有員工
    teams.value = allTeams.value;       // 顯示所有組別
  }

  // 根據組別進一步篩選
  onChangeTeam();
};

/**
 * 處理組別選擇改變事件
 */
const onChangeTeam = (): void => {
  if (selectedTeam.value === 0) {
    // 選擇「全部」組別
    if (selectedDept.value === 0) {
      // 全部部門 + 全部組別 = 所有員工
      deptUsers.value = users.value;
    } else {
      // 特定部門 + 全部組別 = 該部門所有員工
      deptUsers.value = [];
      users.value.forEach((element: Employee) => {
        if (element.DeptNo === selectedDept.value) {
          deptUsers.value.push(element);
        }
      });
    }
  } else if (selectedTeam.value === 1) {
    // 選擇「未分組」
    deptUsers.value = [];
    users.value.forEach((element: Employee) => {
      if (selectedDept.value === 0) {
        // 全部部門的未分組員工
        if (element.TeamID === null) {
          deptUsers.value.push(element);
        }
      } else {
        // 特定部門的未分組員工
        if (element.DeptNo === selectedDept.value && element.TeamID === null) {
          deptUsers.value.push(element);
        }
      }
    });
  } else {
    // 選擇特定組別
    deptUsers.value = [];
    users.value.forEach((element: Employee) => {
      if (selectedDept.value === 0) {
        // 全部部門中指定組別的員工
        if (element.TeamID === selectedTeam.value) {
          deptUsers.value.push(element);
        }
      } else {
        // 特定部門中指定組別的員工
        if (element.DeptNo === selectedDept.value && element.TeamID === selectedTeam.value) {
          deptUsers.value.push(element);
        }
      }
    });
  }
};

// 生命週期鉤子

/**
 * 組件掛載後執行
 * 從 API 載入所有員工資料並初始化相關選項
 */
onMounted(() => {
  axios.get(getUsersUrl).then((response) => {
    // 取得員工資料
    users.value = response.data;

    const deptMap = new Map<number, string>();  // 部門資料對應表
    const teamMap = new Map<number, string>();  // 組別資料對應表

    // 處理每個員工的資料
    users.value.forEach((element: Employee) => {
      // 如果需要顯示員工編號，在姓名前加上編號
      if (props.showEmpNo) {
        element.CName = `${element.EmpNo} ${element.CName}`;
      }

      // 收集部門資訊
      deptMap.set(element.DeptNo, element.DeptSName);

      // 收集組別資訊（如果員工有組別）
      if (element.TeamID !== undefined && element.TeamID !== null && element.TeamCName !== null) {
        teamMap.set(element.TeamID, element.TeamCName);
      }
    });

    // 建立部門選項列表
    deptMap.forEach((value, key) => {
      depts.value.push({ DeptNo: key, DeptName: value });
    });

    // 部門選項按編號排序
    depts.value.sort(function (a: Dept, b: Dept): number {
      if (a.DeptNo < b.DeptNo) {
        return -1;
      }
      if (a.DeptNo > b.DeptNo) {
        return 1;
      }
      // DeptNo 相等時返回 0
      return 0;
    });

    // 建立組別選項列表
    teamMap.forEach((value, key) => {
      teams.value.push({ TeamID: key, TeamCName: value });
    });

    // 組別選項按 ID 排序
    teams.value.sort(function (a: Team, b: Team): number {
      if (a.TeamID < b.TeamID) {
        return -1;
      }
      if (a.TeamID > b.TeamID) {
        return 1;
      }
      // TeamID 相等時返回 0
      return 0;
    });

    // 保存完整的組別列表
    allTeams.value = teams.value;

    // 設定初始選中的員工編號
    empNo.value = props.modelValue;

    // 根據初始部門設定決定顯示的員工列表
    if (props.deptNo === 0) {
      // 如果部門為 0（全部），顯示所有員工
      deptUsers.value = users.value;
    } else {
      // 否則根據指定部門篩選員工
      onChangeDept();
    }
  });
});

// 監聽器

/**
 * 監聽 modelValue 的變化
 * 當父組件傳入的值改變時，同步更新內部的員工編號
 */
watch(() => props.modelValue, (val: string) => {
  empNo.value = val;
});

/**
 * 監聽 deptNo 的變化
 * 當父組件傳入的部門編號改變時，更新選中的部門並重新篩選員工
 */
watch(() => props.deptNo, (val: number) => {
  selectedDept.value = val;
  onChangeDept();
});
</script>
<style lang="scss" scoped>
.choose-user-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
}

.choose-button {
  flex-shrink: 0;
}
</style>
