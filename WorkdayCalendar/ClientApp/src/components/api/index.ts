export const userIsAdminUrl = `${import.meta.env.VITE_APP_BASE_URL}api/User/IsAdmin/`;
export const userIdUrl = `${import.meta.env.VITE_APP_BASE_URL}api/User/UserId/`;
export const getUsersUrl = `${import.meta.env.VITE_APP_BASE_URL}api/User/Users/<USER>
export const userNameUrl = `${import.meta.env.VITE_APP_BASE_URL}api/User/UserName/`;
export const deptNoUrl = `${import.meta.env.VITE_APP_BASE_URL}api/User/DeptNo/`;
export const deptSNameUrl = `${import.meta.env.VITE_APP_BASE_URL}api/User/DeptSName/`;
export const getDeptsUrl = `${import.meta.env.VITE_APP_BASE_URL}api/User/Departments/`;
export const shiftGetUrl = `${import.meta.env.VITE_APP_BASE_URL}api/Workday/GetShifts/`;
export const holidayUrl = `${import.meta.env.VITE_APP_BASE_URL}api/ShiftWeekday/Holidays/`;
export const addWorkdaysUrl = `${import.meta.env.VITE_APP_BASE_URL}api/Workday/AddWorkdays/`;
export const canAddWorkdaysUrl = `${import.meta.env.VITE_APP_BASE_URL}api/Workday/CanAdd/`;
export const updateWorkdayUrl = `${import.meta.env.VITE_APP_BASE_URL}api/Workday/Update/`;
export const getWorkdaysUrl = `${import.meta.env.VITE_APP_BASE_URL}api/Workday/GetWorkdays/`;
export const getDayTypeUrl = `${import.meta.env.VITE_APP_BASE_URL}api/Workday/GetWorkdayType/`;

export const workShiftGetUrl = `${import.meta.env.VITE_APP_BASE_URL}api/WorkShift/Get/`;
export const workShiftAddUrl = `${import.meta.env.VITE_APP_BASE_URL}api/WorkShift/Add/`;
export const workShiftUpdateUrl = `${import.meta.env.VITE_APP_BASE_URL}api/WorkShift/Update/`;
export const workShiftDeleteUrl = `${import.meta.env.VITE_APP_BASE_URL}api/WorkShift/Delete/`;

export const weekdayGetUrl = `${import.meta.env.VITE_APP_BASE_URL}api/ShiftWeekday/GetAll`;
export const weekdayAddUrl = `${import.meta.env.VITE_APP_BASE_URL}api/ShiftWeekday/Add/`;
export const weekdayUpdateUrl = `${import.meta.env.VITE_APP_BASE_URL}api/ShiftWeekday/Update/`;
export const weekdayDeleteUrl = `${import.meta.env.VITE_APP_BASE_URL}api/ShiftWeekday/Delete/`;

export const empWorkShiftGetUrl = `${import.meta.env.VITE_APP_BASE_URL}api/EmpWorkShift/Get/`;
export const empWorkShiftAddUrl = `${import.meta.env.VITE_APP_BASE_URL}api/EmpWorkShift/AddOrUpdate/`;
export const empWorkShiftUpdateUrl = `${import.meta.env.VITE_APP_BASE_URL}api/EmpWorkShift/Update/`;
export const empWorkShiftDeleteUrl = `${import.meta.env.VITE_APP_BASE_URL}api/EmpWorkShift/Delete/`;
export const empWorkShiftGetByEmpNoUrl = `${import.meta.env.VITE_APP_BASE_URL}api/EmpWorkShift/GetByEmpNo/`;
export const empWorkShiftGetByShiftIdUrl = `${import.meta.env.VITE_APP_BASE_URL}api/EmpWorkShift/GetByShiftId/`;

export const empWorkdayGetUrl = `${import.meta.env.VITE_APP_BASE_URL}api/EmpWorkday/GetEmpWorkdays/`;
export const empWorkdayYearGetUrl = `${import.meta.env.VITE_APP_BASE_URL}api/EmpWorkday/GetEmpWorkdaysYear/`;
export const empWorkdayAddUrl = `${import.meta.env.VITE_APP_BASE_URL}api/EmpWorkday/Add/`;
export const empWorkdayUpdateUrl = `${import.meta.env.VITE_APP_BASE_URL}api/EmpWorkday/Update/`;
export const empWorkdayDeleteUrl = `${import.meta.env.VITE_APP_BASE_URL}api/EmpWorkday/Delete/`;
export const empWorkdayCanAddUrl = `${import.meta.env.VITE_APP_BASE_URL}api/EmpWorkday/CanAdd/`;
export const empWorkdayAddYearlyUrl = `${import.meta.env.VITE_APP_BASE_URL}api/EmpWorkday/AddYearlyWorkdays/`;

export type Shift = {
  AfternoonRestEnd: string;
  AfternoonRestStart: string;
  ArrivalTime: string;
  Comment: string | null;
  DepartureTime: string;
  FlexibleArrivalAfter: string;
  FlexibleArrivalBefore: string;
  FlexibleDepartureAfter: string;
  FlexibleDepartureBefore: string;
  MiddayBreakEnd: string;
  MiddayBreakStart: string;
  MorningRestEnd: string;
  MorningRestStart: string;
  ShiftId: number;
  ShiftName: string;
  Workdays: string;
  RestDay: number;
  FixedDayOff: number;
};

export type ShiftName = {
  ShiftId: number;
  ShiftName: string;
};

export interface Workday {
  AfternoonRestEnd: Date;
  AfternoonRestStart: Date;
  ArrivalTime: Date;
  Comment: string;
  DayType: number;
  DepartureTime: Date;
  FlexibleArrivalAfter: Date;
  FlexibleArrivalBefore: Date;
  FlexibleDepartureAfter: Date;
  FlexibleDepartureBefore: Date;
  MiddayBreakEnd: Date;
  MiddayBreakStart: Date;
  MorningRestEnd: Date;
  MorningRestStart: Date;
  ShiftId: number;
  WeekDay: number;
  WorkDate: Date;
  WorkHours: number;
}

export interface EmpWorkday {
  EmpNo: string;
  ShiftId: number;
  WorkDate: Date;
  DayType: number;
  ArrivalTime: Date;
  DepartureTime: Date;
  FlexibleArrivalBefore: Date;
  FlexibleArrivalAfter: Date;
  FlexibleDepartureBefore: Date;
  FlexibleDepartureAfter: Date;
  MiddayBreakStart: Date;
  MiddayBreakEnd: Date;
  MorningRestStart: Date;
  MorningRestEnd: Date;
  AfternoonRestStart: Date;
  AfternoonRestEnd: Date;
  WorkHours: number;
  WeekDay: number;
  Comment: string;
}

export type EmpWorkShift = {
  EmpNo: string;
  ShiftId: number;
  WorkDate: Date;
};

export type EmpWorkShiftParam = {
  EmpNo: string;
  ShiftId: number;
  StartDate: Date;
  EndDate: Date;
};
