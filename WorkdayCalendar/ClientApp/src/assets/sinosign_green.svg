<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   inkscape:version="1.0 (4035a4fb49, 2020-05-01)"
   sodipodi:docname="sinosign_green.svg"
   xml:space="preserve"
   style="enable-background:new 0 0 512.001 512.001;"
   viewBox="0 0 512.001 512.001"
   y="0px"
   x="0px"
   id="Capa_1"
   version="1.1"><metadata
   id="metadata55"><rdf:RDF><cc:Work
       rdf:about=""><dc:format>image/svg+xml</dc:format><dc:type
         rdf:resource="http://purl.org/dc/dcmitype/StillImage" /><dc:title></dc:title></cc:Work></rdf:RDF></metadata><defs
   id="defs53" /><sodipodi:namedview
   inkscape:current-layer="Capa_1"
   inkscape:window-maximized="0"
   inkscape:window-y="0"
   inkscape:window-x="0"
   inkscape:cy="256.00049"
   inkscape:cx="256.00049"
   inkscape:zoom="1.6445281"
   showgrid="false"
   id="namedview51"
   inkscape:window-height="839"
   inkscape:window-width="1400"
   inkscape:pageshadow="2"
   inkscape:pageopacity="0"
   guidetolerance="10"
   gridtolerance="10"
   objecttolerance="10"
   borderopacity="1"
   bordercolor="#666666"
   pagecolor="#ffffff" />
<g
   id="g6">
	<g
   id="g4">
		<path
   id="path2"
   d="M497.001,274.172l-160.665,0.001c-34.85,0-63.202,28.352-63.202,63.201c0.001,34.848,28.353,63.199,63.201,63.199    c18.306,0,33.199,14.893,33.199,33.199c0,18.307-14.894,33.201-33.199,33.201H15.001c-8.284,0-15,6.716-15,15s6.716,15,15,15    h321.334c34.849,0,63.199-28.352,63.199-63.201c0-34.848-28.351-63.199-63.199-63.199c-18.307,0-33.2-14.893-33.201-33.199    c0-18.307,14.894-33.201,33.202-33.201l160.665-0.001c8.284,0,15-6.716,15-15S505.285,274.172,497.001,274.172z" />
	</g>
</g>
<g
   id="g12">
	<g
   id="g10">
		<path
   id="path8"
   d="M70.09,146.197L3.454,226.54c-3.155,3.805-4.233,8.919-2.884,13.674l41.012,144.438l57.332-138.411    c3.17-7.654,11.945-11.287,19.598-8.118c7.654,3.171,11.289,11.944,8.119,19.599l-57.332,138.41L200.43,323    c4.316-2.407,7.17-6.786,7.629-11.707l9.696-103.933L70.09,146.197z" />
	</g>
</g>
<g
   id="g18">
	<g
   id="g16">
		<path
   id="path14"
   d="M256.052,61.774L145.96,16.171c-3.675-1.523-7.805-1.523-11.48-0.001c-3.675,1.523-6.596,4.442-8.118,8.118L86.5,120.522    l137.808,57.079l39.861-96.229C267.34,73.719,263.705,64.944,256.052,61.774z" />
	</g>
</g>
<g
   id="g20">
</g>
<g
   id="g22">
</g>
<g
   id="g24">
</g>
<g
   id="g26">
</g>
<g
   id="g28">
</g>
<g
   id="g30">
</g>
<g
   id="g32">
</g>
<g
   id="g34">
</g>
<g
   id="g36">
</g>
<g
   id="g38">
</g>
<g
   id="g40">
</g>
<g
   id="g42">
</g>
<g
   id="g44">
</g>
<g
   id="g46">
</g>
<g
   id="g48">
</g>
<path
   id="path57"
   d="m 70.67161,393.98929 c 0.18294,-0.64429 13.114066,-31.95535 28.73583,-69.58012 27.19793,-65.50578 28.40538,-68.57641 28.45441,-72.36118 0.11431,-8.82585 -6.29393,-15.20193 -15.27861,-15.20193 -4.36114,0 -9.10325,2.22262 -11.67635,5.47269 -0.999197,1.26207 -13.359715,30.34159 -30.370096,71.44907 -15.776823,38.12644 -28.747823,69.38446 -28.824447,69.46227 -0.07662,0.0778 -9.360904,-32.3479 -20.631734,-72.05714 C 5.6947144,256.96556 0.59067066,238.17149 0.59813523,235.75255 c 0.005783,-1.87418 0.48167967,-4.19816 1.13775277,-5.55608 1.5408457,-3.18919 68.173333,-83.44868 69.092412,-83.2225 0.40386,0.0994 33.46896,13.72562 73.478,30.28052 l 72.7437,30.09982 0.0168,1.96917 c 0.0162,1.90207 -7.86426,87.66265 -9.11185,99.16137 -0.68765,6.33787 -1.94383,9.36941 -4.99108,12.04493 -1.11639,0.98021 -30.06938,17.41943 -64.33998,36.53161 -34.2706,19.11219 -63.654486,35.50315 -65.297529,36.42439 -2.683262,1.50446 -2.953491,1.55572 -2.654725,0.50351 z"
   style="fill:#008000;stroke-width:0.608077" /><path
   id="path59"
   d="M 155.66776,148.82635 C 118.04298,133.24867 87.194897,120.46132 87.116459,120.41002 86.633527,120.09416 127.50174,22.706649 128.84236,20.978662 c 0.90815,-1.170548 3.14245,-2.900265 4.96511,-3.843815 6.29013,-3.256255 3.7189,-4.103534 66.858,22.031244 32.108,13.290267 57.16051,23.998112 58.3754,24.950583 4.60404,3.609538 6.66197,8.644395 5.60672,13.717204 -0.46288,2.22513 -28.89368,71.879532 -39.75238,97.391812 l -0.81877,1.9237 z"
   style="fill:#008000;stroke-width:0.608077" /><path
   id="path61"
   d="m 8.7922699,495.05577 c -5.465207,-2.54493 -8.18419273,-6.96816 -8.18419273,-13.314 0,-5.07691 2.53769533,-9.56553 6.94510533,-12.28436 l 2.9926045,-1.84607 166.356883,-0.30404 c 160.5274,-0.29338 166.46343,-0.34383 169.39728,-1.43969 5.68339,-2.12289 8.80774,-4.04566 12.99988,-8.00031 13.6737,-12.89908 13.96998,-34.38291 0.65775,-47.69515 -6.08071,-6.08071 -14.54046,-9.75333 -22.46644,-9.75333 -30.98879,0 -57.26979,-21.72742 -62.98055,-52.06819 -1.37072,-7.28259 -0.96087,-19.16486 0.90971,-26.37377 5.42946,-20.92413 21.76575,-38.13404 42.31441,-44.57723 9.30649,-2.91812 9.36091,-2.91961 99.15002,-2.72863 l 84.57056,0.17988 2.99255,1.84607 c 6.51898,4.02147 8.87763,12.31889 5.43373,19.1151 -1.42998,2.82193 -3.56793,4.8928 -7.00175,6.78206 -1.26233,0.69452 -16.06389,0.88797 -87.25907,1.14044 l -85.73889,0.30403 -4.31858,1.57999 c -7.94626,2.9072 -14.9386,8.78479 -18.7231,15.73817 -2.0851,3.831 -4.02061,11.44678 -4.00719,15.7673 0.039,12.54344 7.44402,24.67967 18.30733,30.00408 5.07358,2.4867 8.5284,3.21448 19.38289,4.08317 9.53037,0.76271 14.93168,2.23384 23.41098,6.37636 12.66034,6.18514 22.4273,15.95283 28.60571,28.6079 5.34907,10.95635 7.35351,21.63755 6.27081,33.41565 -1.40111,15.24185 -8.0516,28.68328 -19.64639,39.70771 -6.39665,6.082 -13.14592,10.36742 -20.93698,13.29387 -11.04189,4.14749 1.32913,3.88286 -181.20452,3.87629 l -165.165315,-0.006 z"
   style="fill:#008000;stroke-width:0.608077" /></svg>
