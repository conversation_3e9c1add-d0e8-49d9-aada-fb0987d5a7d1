import { defineStore } from "pinia";
import axios from "axios";
import { userIsAdminUrl, userIdUrl } from "../components/api";

export const useAuthUserStore = defineStore("authUser", {
  state: () => {
    return {
      logonUser: "",
      isAdmin: false,
      Year: new Date().getFullYear(),
    };
  },
  // getters: {
  //   getUserId: (state) => state.logonUser 
  // },
  actions: {
    async checkIsAdmin(): Promise<void> {
      await axios.get(userIsAdminUrl).then((response) => {
        this.isAdmin = response.data as boolean;
      });
    },
    async getUserId(): Promise<void>{
      await axios.get(userIdUrl).then((response) => {
        this.logonUser = response.data as string;
      });
    },
    setYear(year: number): void {
      this.Year = year;
    },
  },
});
