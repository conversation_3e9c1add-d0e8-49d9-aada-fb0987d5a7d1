<template>
  <div>
    <div></div>

    &nbsp;
    <h1>
      <a @click="prevYear"><span class="pi pi-chevron-left p-link"></span></a>
      &nbsp;{{ year }} 年 &nbsp;
      <a @click="nextYear" class="pi pi-chevron-right p-link"></a>
    </h1>
    <div class="p-fluid">
      <div class="p-field p-grid" style="align-items: center;">
        <label for="EmpNo" class="p-col-fixed" style="display: flex; align-items: center;">員工：</label>
        <div class="p-col-fixed" style="display: flex; align-items: center;">
          <ChooseUser v-model="empNo" v-on:change="empChange" :showName="true" :selectDept="true" />
        </div>
      </div>
    </div>
    <div>
      <DataTable :value="empWorkdays" stripedRows responsiveLayout="scroll" class="p-datatable-customers"
        dataKey="WorkDate">
        <Column field="WorkDate" header="日期">
          <template #body="slotProps">
            {{ formatDate(slotProps.data.WorkDate) }}
          </template>
        </Column>
        <Column field="DayType" header="班別">
          <template #body="slotProps">
            {{ slotProps.data.ShiftName }}
          </template>
        </Column>
        <Column :bodyStyle="{ textAlign: 'center', overflow: 'visible' }" class="nowrap">
          <template #body="slotProps">
            <Button type="button" v-if="isAdmin" @click="deleteEmpWorkday(slotProps.data)" label="" alt="刪除"
              icon="pi pi-trash" class="p-button-danger p-button-raised p-button-rounded p-button-sm" />
          </template>
        </Column>
      </DataTable>
    </div>

    <!--  新增 員工工作日 Dialog -->
    <Dialog position="center" v-model:visible="dialogDisplay" v-bind:modal="true">
      <template #header>
        <h3>{{ header }}</h3>
      </template>

      <div class="p-fluid">
        <div class="p-field p-grid">
          <label for="EmpNo" class="p-col-fixed" style="width: 220px">員工：</label>
          <div class="p-col-fixed" style="width: 300px">
            <label v-if="empNo">{{ EmpName }}</label>
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="ShiftName" class="p-col-fixed" style="width: 220px">班別名稱：</label>
          <div class="p-col-fixed" style="width: 300px">
            {{ ShiftName }}
          </div>
        </div>
      </div>

      <Button label="新增" @click="addEmpWorkdays" class="p-button-raised p-button-rounded" icon="pi pi-check"
        autofocus />
      &nbsp;&nbsp;
      <Button label="取消" v-on:click="cancelAddWorkdays" class="p-button-raised p-button-danger p-button-rounded"
        icon="pi pi-times" />
    </Dialog>

    <!-- 修改員工工作日 Dialog -->
    <Dialog position="center" v-model:visible="showUpdateDialog" v-bind:modal="true">
      <template #header>
        <h3>{{ updateHeader }}</h3>
      </template>
      <div class="p-field p-grid">
        <label for="FlexibleArrivalBefore" class="p-col-fixed" style="width: 220px">最早彈性上班時間(時:分)：</label>
        <div class="p-col-fixed" style="width: 300px">
          <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="FlexibleArrivalBefore"
            :disabled="!isAdmin" />
        </div>
      </div>
      <div class="p-fluid">
        <div class="p-field p-grid"></div>
        <div class="p-field p-grid">
          <label for="ArrivalTime" class="p-col-fixed" style="width: 220px">正常上班時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="ArrivalTime" :disabled="!isAdmin" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="FlexibleArrivalAfter" class="p-col-fixed" style="width: 220px">最晚彈性上班時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="FlexibleArrivalAfter"
              :disabled="!isAdmin" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="MiddayBreakStart" class="p-col-fixed" style="width: 220px">用餐休息開始時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="MiddayBreakStart"
              :disabled="!isAdmin" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="MiddayBreakEnd" class="p-col-fixed" style="width: 220px">用餐休息結束時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="MiddayBreakEnd" :disabled="!isAdmin" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="MorningRestStart" class="p-col-fixed" style="width: 220px">前段休息開始時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="MorningRestStart"
              :disabled="!isAdmin" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="MorningRestEnd" class="p-col-fixed" style="width: 220px">前段休息結束時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="MorningRestEnd" :disabled="!isAdmin" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="AfternoonRestStart" class="p-col-fixed" style="width: 220px">後段休息開始時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="AfternoonRestStart"
              :disabled="!isAdmin" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="AfternoonRestEnd" class="p-col-fixed" style="width: 220px">後段休息結束時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="AfternoonRestEnd"
              :disabled="!isAdmin" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="FlexibleDepartureBefore" class="p-col-fixed" style="width: 220px">最早彈性下班時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="FlexibleDepartureBefore"
              :disabled="!isAdmin" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="DepartureTime" class="p-col-fixed" style="width: 220px">正常下班時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="DepartureTime" :disabled="!isAdmin" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="FlexibleDepartureAfter" class="p-col-fixed" style="width: 220px">最晚彈性下班時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="FlexibleDepartureAfter"
              :disabled="!isAdmin" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="DayType" class="p-col-fixed" style="width: 220px">本日類型：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Dropdown v-model="DayType" :options="dayTypes" optionValue="TypeId" optionLabel="TypeName"
              placeholder="請選擇 工作日類型" v-on:change="dateTypeChanged(DayType)" :disabled="!isAdmin" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="DayType" class="p-col-fixed" style="width: 220px">本日正常工時：</label>
          <div class="p-col-fixed" style="width: 300px">
            <InputNumber v-model="WorkHours" :min="0" :max="12" :allowEmpty="false" :disabled="!isAdmin" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="AfternoonRestStart" class="p-col-fixed" style="width: 220px">備註：</label>
          <div class="p-col-fixed" style="width: 300px">
            <InputText v-model="Comment" type="text" placeholder="備註" :disabled="!isAdmin" />
          </div>
        </div>
      </div>
      <Button label="修改" v-if="isAdmin" v-on:click="updateDate" class="p-button-raised p-button-rounded"
        icon="pi pi-check" autofocus />
      &nbsp;&nbsp;
      <Button label="取消" v-if="isAdmin" v-on:click="cancelUpdate"
        class="p-button-raised p-button-danger p-button-rounded" icon="pi pi-times" />
    </Dialog>

    <Dialog position="center" v-model:visible="showWarning" v-bind:modal="true">
      <h3>{{ warningMessage }}</h3>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
// ===== Vue 3 Composition API 導入 =====
import { ref, reactive, onMounted, computed, nextTick } from "vue";

// ===== 自訂組件導入 =====
import ChooseUser from "../components/ChooseUser.vue";
import ChooseShift from "../components/ChooseShift.vue";

// ===== PrimeVue 組件與服務導入 =====
import { useToast } from "primevue/usetoast";        // Toast 通知服務
import { useConfirm } from "primevue/useconfirm";    // 確認對話框服務

import Dialog from "primevue/dialog";                // 對話框組件
import Dropdown from "primevue/dropdown";            // 下拉選單組件
import Calendar from "primevue/calendar";            // 日期時間選擇器組件
import InputText from "primevue/inputtext";          // 文字輸入框組件
import InputNumber from "primevue/inputnumber";      // 數字輸入框組件

// ===== HTTP 請求與 API 導入 =====
import axios from "axios";                           // HTTP 請求庫
import {
  userIsAdminUrl,                                    // 驗證管理員身份 API
  getUsersUrl,                                       // 取得員工清單 API
  shiftGetUrl,                                       // 取得班別清單 API
  empWorkdayGetUrl,                                  // 取得員工工作日清單 API
  empWorkdayYearGetUrl,                              // 取得員工年度工作日清單 API
  empWorkdayAddUrl,                                  // 批次新增員工年度工作日 API
  empWorkdayUpdateUrl,                               // 更新員工工作日 API
  empWorkdayDeleteUrl,                               // 刪除員工工作日 API
  empWorkdayCanAddUrl,                               // 檢查是否可新增員工工作日 API
  getDayTypeUrl,                                     // 取得工作日類型 API
  holidayUrl,                                        // 取得假日資料 API
  Shift,                                             // 班別型別定義
  EmpWorkday                                         // 員工工作日型別定義
} from "../components/api";

// ===== 狀態管理導入 =====
import { useAuthUserStore } from '../store/index';   // 使用者認證狀態管理

// ===== 初始化服務與全域狀態 =====
const userStore = useAuthUserStore();                // 使用者狀態管理實例
const toast = useToast();                           // Toast 通知服務實例
const confirm = useConfirm();                       // 確認對話框服務實例

// ===== 介面型別定義 =====
interface IDayType {
  TypeId: number;
  TypeName: string;
}

interface IEmployee {
  EmpNo: string;
  CName: string;
}

interface IShiftName {
  ShiftId: number;
  ShiftName: string;
}

// ===== 響應式狀態變數定義 =====

// --- 權限與介面控制相關 ---
const isAdmin = ref(false);                         // 是否為管理員身份
const dialogDisplay = ref(false);                   // 控制新增對話框顯示狀態
const showUpdateDialog = ref(false);                // 控制修改對話框顯示狀態
const showWarning = ref(false);                     // 控制警告對話框顯示狀態
const warningMessage = ref("");                     // 警告訊息內容

// --- 表單狀態與標題 ---
const header = ref("");                             // 新增對話框標題
const updateHeader = ref("");                       // 修改對話框標題
const canAdd = ref(false);                          // 是否可新增員工工作日
/**
 * 檢查是否可以產生員工班別
 * 需要選擇員工、班別、開始日期和結束日期
 */
const canGenerate = computed(() => {
  return empNo.value !== "" &&
    selectedShiftId.value !== null &&
    startDate.value !== undefined &&
    endDate.value !== undefined &&
    startDate.value <= endDate.value;
});
// --- 資料管理相關 ---
const employees = ref<Array<IEmployee>>([]);        // 員工清單
const shifts = ref<Array<Shift>>([]);              // 班別清單
const distinctShifts = ref<Array<IShiftName>>([]);  // 去重複的班別清單
const empWorkdays = ref<Array<EmpWorkday>>([]);     // 員工工作日清單
const holidays = ref<any[]>([]);                // 國定假日資料陣列
const months = ref<Array<Date>>([]);                // 月份陣列（用於日曆顯示）
const currentEmpWorkday = ref<EmpWorkday | null>(null); // 目前選中的員工工作日物件
const selectedShiftId = ref<number | null>(null);   // 目前選中的班別編號

// --- 工作日類型定義 ---
const dayTypes = ref<Array<IDayType>>([]);          // 所有工作日類型
const dayTypesNormal = reactive([                    // 平日工作日類型
  { "TypeId": 1, "TypeName": "工作日" },
  { "TypeId": 4, "TypeName": "週間國定假日" },
  { "TypeId": 8, "TypeName": "補假日" },
  { "TypeId": 9, "TypeName": "彈性放假日" },
  { "TypeId": 10, "TypeName": "週間天災日" },
  { "TypeId": 20, "TypeName": "週間休息日" }
]);
const dayTypesSat = reactive([                       // 週六工作日類型
  { "TypeId": 2, "TypeName": "週六休息日" },
  { "TypeId": 5, "TypeName": "週六國定假日" },
  { "TypeId": 7, "TypeName": "補班日" },
  { "TypeId": 11, "TypeName": "週六天災日" }
]);
const dayTypesSun = reactive([                       // 週日工作日類型
  { "TypeId": 3, "TypeName": "例假日" },
  { "TypeId": 6, "TypeName": "週日國定假日" }
]);

// --- 基本資料欄位 ---
const year = ref(userStore.Year);                   // 目前年份
const empNo = ref("");                              // 員工編號
const EmpName = ref("");                            // 員工姓名
const ShiftId = ref(1);                             // 班別編號
const ShiftName = ref("");                          // 班別名稱
const startDate = ref<Date | undefined>(undefined); // 開始日期
const endDate = ref<Date | undefined>(undefined);   // 結束日期

// --- 工作日資料欄位 ---
const WorkDate = ref(new Date());                    // 工作日期
const WeekDay = ref(1);                             // 星期幾
const DayType = ref(1);                             // 日期類型
const WorkHours = ref(8);                           // 工作時數
const Comment = ref("");                            // 備註

// --- 工作時間設定 ---
const ArrivalTime = ref(new Date("2049-01-01T08:00"));              // 正常上班時間
const DepartureTime = ref(new Date("2049-01-01T17:00"));            // 正常下班時間
const FlexibleArrivalBefore = ref(new Date("2049-01-01T07:30"));    // 最早彈性上班時間
const FlexibleArrivalAfter = ref(new Date("2049-01-01T09:00"));     // 最晚彈性上班時間
const FlexibleDepartureBefore = ref(new Date("2049-01-01T16:30"));  // 最早彈性下班時間
const FlexibleDepartureAfter = ref(new Date("2049-01-01T18:00"));   // 最晚彈性下班時間

// --- 休息時間設定 ---
const MiddayBreakStart = ref(new Date("2049-01-01T12:00"));         // 用餐休息開始時間
const MiddayBreakEnd = ref(new Date("2049-01-01T13:00"));           // 用餐休息結束時間
const MorningRestStart = ref(new Date("2049-01-01T10:00"));         // 前段休息開始時間
const MorningRestEnd = ref(new Date("2049-01-01T10:10"));           // 前段休息結束時間
const AfternoonRestStart = ref(new Date("2049-01-01T15:00"));       // 後段休息開始時間
const AfternoonRestEnd = ref(new Date("2049-01-01T15:10"));         // 後段休息結束時間

// ===== 輔助工具函數 =====

/**
 * 顯示 Toast 通知訊息
 * @param message - 要顯示的訊息內容
 */
const toastMessage = (message: string): void => {
  toast.add({ severity: "info", summary: message, life: 3500 });
};

/**
 * 處理空值或 null 字串，進行修剪處理
 * @param theStr - 要處理的字串（可能為 null）
 * @returns 修剪後的字串，null 或空字串則回傳空字串
 */
const trimNull = (theStr: string | null) => {
  if (theStr === null || theStr === "") {
    return "";
  } else {
    return theStr.trim();
  }
};

/**
 * 處理班別選擇改變事件
 */
const onShiftChanged = async () => {
  // 班別選擇改變時重新載入工作日資料
  if (empNo.value && selectedShiftId.value) {
    await getEmpWorkdays(empNo.value, year.value, selectedShiftId.value);
  }
  ShiftId.value = selectedShiftId.value as number;
  ShiftName.value = shifts.value.find(shift => shift.ShiftId === selectedShiftId.value)?.ShiftName || "";

};

/**
 * 將日期轉換為字串格式
 * @param d - 要轉換的日期物件
 * @returns 格式化後的日期字串 (YYYY/M/D)
 */
const toDateString = (d: Date): string => {
  return `${d.getFullYear()}/${d.getMonth() + 1}/${d.getDate()}`;
};

// ===== 年份操作函數 =====

/**
 * 切換到上一年
 */
const prevYear = () => {
  year.value--;
  changeYear();
};

/**
 * 切換到下一年
 */
const nextYear = () => {
  year.value++;
  changeYear();
};

/**
 * 變更年份時的處理
 * 清空資料並重新載入該年份的相關資料
 */
const changeYear = () => {
  userStore.setYear(year.value);
  months.value = [];
  empWorkdays.value = [];
  distinctShifts.value = [];

  // 產生12個月的日期物件
  for (let i = 1; i < 13; i++) {
    months.value.push(new Date(year.value, i - 1, 1));
  }

  // 重新載入資料
  getShifts();
  if (empNo.value && selectedShiftId.value) {
    getEmpWorkdays(empNo.value, year.value, selectedShiftId.value);
  }
};
/**
 * 取得指定年份的國定假日資料
 * @param year - 年份
 */
const getHolidays = (year: number): void => {
  axios
    .get(holidayUrl + year)
    .then((res) => {
      holidays.value = res.data;  // 儲存國定假日資料
    })
    .catch((error) => {
      console.error(error);
    });
};
/**
 * 取得指定月份和日期的 Date 物件
 * @param theMonth - 月份索引（0-11）
 * @param theDay - 日期
 * @returns Date 物件
 */
const getTheDate = (theMonth: number, theDay: number) => {
  return new Date(year.value, theMonth, theDay);
};

// ===== 員工操作函數 =====

/**
 * 員工選擇變更處理
 * 當選擇不同員工時，重新載入該員工的工作日資料
 */
const empChange = async () => {
  // 找到選中的員工資料
  const selectedEmp = employees.value.find(emp => emp.EmpNo === empNo.value);
  if (selectedEmp) {
    EmpName.value = selectedEmp.CName;
  }

  // 載入該員工的工作日資料
  if (empNo.value && year.value) {
    await getEmpWorkdaysYear(empNo.value, year.value);
  }
};

// ===== 班別操作函數 =====

/**
 * 班別選擇變更處理
 * 當選擇不同班別時，更新班別名稱並檢查是否可新增
 */
const shiftChange = (): void => {
  distinctShifts.value = [];
  const m = new Map<number, string>();

  // 建立班別對應表並找到選中的班別
  for (let i = 0; i < shifts.value.length; i++) {
    m.set(shifts.value[i].ShiftId, shifts.value[i].ShiftName);
    if (shifts.value[i].ShiftId === ShiftId.value) {
      ShiftName.value = shifts.value[i].ShiftName;
    }
  }

  // 建立去重複的班別清單
  m.forEach((value, key) => {
    const temp: IShiftName = { ShiftId: key, ShiftName: value };
    distinctShifts.value.push(temp);
  });
};

// ===== 日期類型操作函數 =====

/**
 * 日期類型變更處理
 * 根據選擇的日期類型自動設定工作時數
 * @param typeId - 日期類型 ID
 */
const dateTypeChanged = (typeId: number): void => {
  switch (typeId) {
    case 1: // 工作日
    case 7: // 補班日
      WorkHours.value = 8;
      break;
    case 2:  // 週六休息日
    case 3:  // 例假日
    case 4:  // 週間國定假日
    case 5:  // 週六國定假日
    case 6:  // 週日國定假日
    case 8:  // 補假日
    case 9:  // 彈性放假日
    case 10: // 週間天災日
    case 11: // 週六天災日
    case 20: // 週間休息日
      WorkHours.value = 0;
      break;
  }
};

// ===== 日曆樣式函數 =====

/**
 * 取得日曆日期的 CSS 樣式類別
 * 根據工作日類型決定顯示顏色
 * @param year - 年份
 * @param month - 月份
 * @param day - 日期
 * @returns CSS 類別名稱
 */
const getCssClass = (year: number, month: number, day: number) => {
  let ret = "";
  const elem = empWorkdays.value.find((x) => {
    const y = new Date(x.WorkDate);
    return y.getFullYear() === year && y.getMonth() === month && y.getDate() === day;
  });

  if (elem !== null && elem !== undefined) {
    // DayType 1-工作日 2-週六休息日 3-例假日 4-週間國定假日 5-週六國定假日 6-週日國定假日 7-補班日 8-補假日 9-彈性放假日 10-週間天災日 11-週六天災日 20-週間休息日
    switch (elem.DayType) {
      case 1:
        ret = "working-day";
        break;
      case 7:
        ret = "working-day bold";
        break;
      case 2:
      case 3:
        ret = "work-offday";
        break;
      case 4:
      case 5:
      case 6:
      case 8:
      case 10:
      case 11:
        ret = "work-holiday";
        break;
      case 9:
        ret = "work-holiday bold";
        break;
      default:
        ret = "";
        break;
    }
  }
  return ret;
};

// ===== 對話框操作函數 =====

/**
 * 顯示新增員工工作日對話框
 */
const showAddDialog = () => {
  if (selectedShiftId.value === 0) {
    showWarning.value = true;
    warningMessage.value = "請先選擇班別，再新增員工工作日";
    return;
  }

  if (!empNo.value) {
    showWarning.value = true;
    warningMessage.value = "請先選擇員工";
    return;
  }

  dialogDisplay.value = true;
  header.value = addWorkdayTitle();
};

/**
 * 產生新增工作日的標題
 * @returns 標題字串
 */
const addWorkdayTitle = () => {
  return `新增 ${EmpName.value} ${year.value} 年度工作日`;
};

/**
 * 取消新增工作日對話框
 */
const cancelAddWorkdays = () => {
  dialogDisplay.value = false;
};

/**
 * 顯示修改員工工作日對話框
 * @param date - 選中的日期
 */
const showUpdate = (date: Date) => {
  if (!empNo.value) {
    return;
  }
  if (empWorkdays.value.length === 0 || canAdd.value) {
    toastMessage(`未建立 ${EmpName.value} ${ShiftName.value} ${toDateString(date)} 行事曆`);
    return;
  }

  const year = date.getFullYear();
  const month = date.getMonth();
  const day = date.getDate();
  updateHeader.value = `修改 ${EmpName.value} ${year} 年 ${month + 1} 月 ${day} 日`;

  if (!isAdmin.value) {
    updateHeader.value = `${EmpName.value} ${year} 年 ${month + 1} 月 ${day} 日`;
  }

  const elem = empWorkdays.value.find((x) => {
    const y = new Date(x.WorkDate);
    return y.getFullYear() === year && y.getMonth() === month && y.getDate() === day;
  });

  if (elem !== null && elem !== undefined) {
    currentEmpWorkday.value = elem;
    ArrivalTime.value = new Date(elem.ArrivalTime);
    DepartureTime.value = new Date(elem.DepartureTime);
    FlexibleArrivalBefore.value = new Date(elem.FlexibleArrivalBefore);
    FlexibleArrivalAfter.value = new Date(elem.FlexibleArrivalAfter);
    FlexibleDepartureBefore.value = new Date(elem.FlexibleDepartureBefore);
    FlexibleDepartureAfter.value = new Date(elem.FlexibleDepartureAfter);
    MiddayBreakStart.value = new Date(elem.MiddayBreakStart);
    MiddayBreakEnd.value = new Date(elem.MiddayBreakEnd);
    MorningRestStart.value = new Date(elem.MorningRestStart);
    MorningRestEnd.value = new Date(elem.MorningRestEnd);
    AfternoonRestStart.value = new Date(elem.AfternoonRestStart);
    AfternoonRestEnd.value = new Date(elem.AfternoonRestEnd);
    DayType.value = elem.DayType;
    WorkHours.value = elem.WorkHours;
    Comment.value = elem.Comment;
    WeekDay.value = elem.WeekDay;
  } else {
    toastMessage(`未建立 ${EmpName.value} ${ShiftName.value} ${toDateString(date)} 行事曆`);
    return;
  }

  showUpdateDialog.value = true;
};

/**
 * 取消修改工作日對話框
 */
const cancelUpdate = () => {
  showUpdateDialog.value = false;
};

// ===== 資料操作函數 =====

/**
 * 新增員工工作日
 */
const addEmpWorkdays = () => {
  if (!empNo.value) {
    showWarning.value = true;
    warningMessage.value = "請選擇員工";
    return;
  }

  if (!ShiftId.value) {
    showWarning.value = true;
    warningMessage.value = "請選擇班別";
    return;
  }

  canAdd.value = false;

  const newRequest = {
    EmpNo: empNo.value,
    Year: year.value,
    ShiftId: ShiftId.value,
    StartDate: startDate.value,
    EndDate: endDate.value,
  };

  axios
    .post(empWorkdayAddUrl, newRequest)
    .then((response) => {
      if (response.data === true) {
        toastMessage(`新增 ${EmpName.value} ${year.value} 年工作日成功`);
        if (selectedShiftId.value) {
          getEmpWorkdays(empNo.value, year.value, selectedShiftId.value);
        }
        canAdd.value = false;
      } else {
        toastMessage(`新增 ${EmpName.value} ${year.value} 年工作日失敗\n${response.data}`);
        canAdd.value = true;
      }
    })
    .catch((error) => {
      console.log(error);
      toastMessage(`新增 ${EmpName.value} ${year.value} 年工作日失敗\n${error.response.data}`);
      canAdd.value = true;
    });

  dialogDisplay.value = false;
};

/**
 * 更新員工工作日
 */
const updateDate = () => {
  if (!currentEmpWorkday.value) {
    return;
  }

  const cDay = currentEmpWorkday.value;
  cDay.EmpNo = empNo.value;
  cDay.ShiftId = ShiftId.value;
  cDay.ArrivalTime = ArrivalTime.value;
  cDay.DepartureTime = DepartureTime.value;
  cDay.FlexibleArrivalBefore = FlexibleArrivalBefore.value;
  cDay.FlexibleArrivalAfter = FlexibleArrivalAfter.value;
  cDay.FlexibleDepartureBefore = FlexibleDepartureBefore.value;
  cDay.FlexibleDepartureAfter = FlexibleDepartureAfter.value;
  cDay.MiddayBreakStart = MiddayBreakStart.value;
  cDay.MiddayBreakEnd = MiddayBreakEnd.value;
  cDay.MorningRestStart = MorningRestStart.value;
  cDay.MorningRestEnd = MorningRestEnd.value;
  cDay.AfternoonRestStart = AfternoonRestStart.value;
  cDay.AfternoonRestEnd = AfternoonRestEnd.value;
  cDay.DayType = DayType.value;
  cDay.WorkHours = WorkHours.value;
  cDay.WeekDay = WeekDay.value;
  cDay.Comment = trimNull(Comment.value);

  axios
    .put(empWorkdayUpdateUrl, cDay)
    .then((response) => {
      const date = new Date(cDay.WorkDate);
      if (response.data === true) {
        toastMessage(`更新 ${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 成功`);
        // 更新成功後重新載入工作日資料
        if (empNo.value && selectedShiftId.value) {
          getEmpWorkdays(empNo.value, year.value, selectedShiftId.value);
        }
      } else {
        toastMessage(`更新 ${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 失敗`);
      }
    })
    .catch((error) => {
      const date = new Date(cDay.WorkDate);
      toastMessage(`更新 ${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 失敗 ${error}`);
    });

  showUpdateDialog.value = false;
};

// ===== 資料載入函數 =====

/**
 * 取得員工清單
 */
const getEmployees = () => {
  axios
    .get(getUsersUrl)
    .then((response) => {
      employees.value = response.data;
    })
    .catch((error) => {
      console.error(error);
    });
};

/**
 * 取得班別清單
 */
const getShifts = () => {
  axios
    .get(shiftGetUrl)
    .then((response) => {
      shifts.value = response.data;
      shiftChange();
    })
    .catch((error) => {
      console.error(error);
    });
};

/**
 * 取得工作日類型清單
 */
const getDayTypes = async () => {
  await axios
    .get(getDayTypeUrl)
    .then((response) => {
      dayTypes.value = response.data;
    })
    .catch((error) => {
      console.error(error);
    });
};

/**
 * 刪除員工班別設定
 */
const deleteEmpWorkday = (empWorkday: EmpWorkday) => {
  confirm.require({
    message: "刪除後無法復原，確定嗎？",
    header: "確認刪除",
    icon: "pi pi-exclamation-triangle",
    accept: async () => {
      try {

        let workday = {
          EmpNo: empWorkday.EmpNo,
          ShiftId: empWorkday.ShiftId,
          WorkDate: empWorkday.WorkDate
        };
        const response = await axios.delete(empWorkdayDeleteUrl, { data: workday });
        if (response.data) {
          toastMessage("刪除成功");
          await getEmpWorkdaysYear(empNo.value, year.value); // 重新載入清單
        } else {
          toastMessage("刪除失敗");
        }
      } catch (error) {
        console.error("刪除失敗:", error);
        toastMessage("刪除失敗: " + error);
      }
    }
  });
};

/**
 * 取得員工年度工作日清單
 * @param EmpNo - 員工編號
 * @param year - 年份
 */
const getEmpWorkdaysYear = async (EmpNo: string, year: number) => {

  await axios
    .get(`${empWorkdayYearGetUrl}?empNo=${EmpNo}&year=${year}`)
    .then((response) => {
      empWorkdays.value = response.data;
    })
    .catch((error) => {
      console.error(error);
    });
};

/**
 * 取得員工工作日清單
 * @param EmpNo - 員工編號
 * @param year - 年份
 * @param shiftId - 班別編號
 */
const getEmpWorkdays = async (EmpNo: string, year: number, shiftId: number) => {

  await axios
    .get(`${empWorkdayGetUrl}?empNo=${EmpNo}&year=${year}&shiftId=${shiftId}`)
    .then((response) => {
      empWorkdays.value = response.data;
    })
    .catch((error) => {
      console.error(error);
    });
};

/**
 * 格式化日期顯示
 */
const formatDate = (date: Date | string): string => {
  if (!date) return "";
  const d = new Date(date);
  return d.toLocaleDateString('zh-TW');
};
// ===== 生命週期鉤子 =====

/**
 * 組件掛載完成後的初始化處理
 */
onMounted(async () => {
  // 從使用者狀態管理取得管理員權限
  isAdmin.value = userStore.isAdmin;

  // 初始化月份陣列
  for (let i = 1; i < 13; i++) {
    months.value.push(new Date(year.value, i - 1, 1));
  }

  // 載入基礎資料
  await getEmployees();
  await getShifts();
  await getDayTypes();
  await getHolidays(year.value);              // 載入該年度國定假日
  // await getEmpWorkdaysYear(empNo.value, year.value); // 因為初始化時無empNo.value，故不啟用
  // 確保 DOM 更新完成
  nextTick();
});
</script>

<style scoped>
.working-day {
  color: blue;
}

.work-holiday {
  color: red;
}

.work-offday {
  color: pink;
}

.work-make-up-day {
  color: green;
}

.bold {
  font-weight: bold;
}

.p-datepicker table td {
  padding: 0.05rem !important;
}

.p-datepicker-other-month>.p-disabled {
  display: none;
}

.p-toast-message-detail {
  white-space: pre-line;
}

h3 {
  color: blue;
}
</style>