<template>
    <div>
        <h1>員工班別設定</h1>
        <div class="p-d-flex p-flex-wrap">
            <div class="p-mr-2 p-mb-2"><ChooseUser v-model="userId" cssClass="userWidth" :deptNo="0" :selectDept="false" /></div>
            <div class="p-mr-2 p-mb-2" v-if="userIdNotNull"><ChooseShift /></div> 
</div>
        <!-- <div class="p-d-flex">
            <div class="p-mr-2 p-mb-2"><ChooseUser v-model="userId" cssClass="userWidth" :deptNo="0" :selectDept="false" /></div>
            <div class="p-mr-2 p-mb-2" v-if="userIdNotNull"><ChooseShift /></div> 
        </div> -->

        <div class="p-grid">
            <div class="p-col-4">
                <ChooseUser v-model="userId" cssClass="userWidth" :deptNo="0" :selectDept="false" />
            </div>
            <div class="p-col-8" v-if="userIdNotNull">
                <ChooseShift />
            </div>

            <div class="p-col-4">選擇單一日期</div>
            <div class="p-col-4">設定日期區間</div>
            <div class="p-col-4">點選行事曆</div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref, computed, Ref } from "vue";
import { defineComponent } from "vue";

import ChooseUser from "../components/ChooseUser.vue";
import ChooseShift from "../components/ChooseShift.vue";

const userId: Ref<string> = ref("");
const userIdNotNull: Ref<boolean> = computed(() => {
    return userId.value !== "";
});
</script>

<style lang="scss">
.userWidth {
    min-width: 160px;
    max-width: 220px;
}
</style>