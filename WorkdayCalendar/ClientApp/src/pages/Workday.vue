<!-- 工作日曆管理頁面模板 -->
<template>
  <div>
    <div></div>

    &nbsp;
    <!-- 年份選擇標題區域 -->
    <h1>
      <!-- 前一年按鈕 -->
      <a @click="prevYear"><span class="pi pi-chevron-left p-link"></span></a>
      &nbsp;{{ year }} 年 &nbsp;
      <!-- 後一年按鈕 -->
      <a @click="nextYear" class="pi pi-chevron-right p-link"></a>
    </h1>
    <!-- 班別選擇與產生行事曆區域 -->
    <div class="p-fluid">
      <div class="p-field p-grid">
        <!-- 班別名稱標籤 -->
        <label for="ShiftName" class="p-col-fixed">班別名稱：</label>
        <!-- 班別下拉選單 -->
        <div class="p-col-fixed">
          <Dropdown v-model.number="ShiftId" :options="distinctShifts" optionValue="ShiftId" v-on:change="shiftChange"
            optionLabel="ShiftName" placeholder="請選擇 班別" />
        </div>
        <div>
          <label class="p-col-fixed"> </label>
          <!-- 產生年度行事曆按鈕（僅在可新增時顯示） -->
          <Button type="button" label="產生年度行事曆" iconPos="left" icon="pi pi-plus"
            class="p-button-raised p-button-rounded" v-if="canAdd" v-on:click="showAddDialog" />
        </div>
      </div>
    </div>
    <!-- 月曆顯示區域 -->
    <div>
      <div class="p-grid">
        <!-- 遍歷12個月份，每個月份顯示一個日曆組件 -->
        <div class="p-xl-3 p-md-4 p-sm-6" v-for="(m, idx) in months" v-bind:key="idx">
          <!-- PrimeVue 日曆組件，顯示單月，支援點擊日期 -->
          <Calendar v-model="months[idx]" selectionMode="single" :numberOfMonths="1" :showOtherMonths="false"
            @date-select="showUpdate" v-bind:minDate="getTheDate(idx, 1)" v-bind:maxDate="getTheDate(idx, 31)"
            :showPrev="false" :inline="true" locale="zh-tw">
            <!-- 自定義日期顯示樣式，根據工作日類型套用不同的 CSS 類別 -->
            <template #date="slotProps">
              <span :class="getCssClass(slotProps.date.year, slotProps.date.month, slotProps.date.day)">{{
                slotProps.date.day
              }} </span>
            </template>
            <!-- 隱藏日曆標題 -->
            <template #header>&nbsp;</template>
          </Calendar>
        </div>
      </div>
    </div>
    <!-- 新增工作日對話框 -->
    <Dialog position="center" v-model:visible="dialogDisplay" v-bind:modal="true">
      <template #header>
        <h3>{{ header }}</h3>
      </template>

      <!-- 班別選擇區域 -->
      <div class="p-fluid">
        <div class="p-field p-grid">
          <label for="ShiftName" class="p-col-fixed" style="width: 220px">班別名稱：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Dropdown v-model="ShiftId" :options="distinctShifts" optionValue="ShiftId" v-on:change="shiftChange"
              optionLabel="ShiftName" placeholder="請選擇 班別" />
          </div>
        </div>
      </div>

      <!-- 國定假日顯示區域 -->
      <div class="p-field p-grid">
        <label class="p-col-fixed" style="width: 220px">國定假日</label>
        <div class="p-col-fixed" style="width: 450px">
          <!-- 國定假日列表，顯示日期和名稱，管理員可刪除 -->
          <DataTable :value="holidays" stripedRows responsiveLayout="scroll">
            <!-- 日期欄位，格式化顯示 -->
            <Column field="Date" header="日期"><template #body="slotProps">
                {{ toDateString(new Date(slotProps.data.Date)) }}
              </template>
            </Column>
            <!-- 假日名稱欄位 -->
            <Column field="Name" header="假日名稱" />
            <!-- 操作欄位（刪除按鈕，僅管理員可見） -->
            <Column>
              <template #body="xx">
                <Button type="button" v-if="isAdmin" v-on:click="removeHoliday(xx.data)" alt="刪除" icon="pi pi-trash"
                  iconPos="left" class="p-button-danger p-button-raised p-button-rounded" />
              </template>
            </Column>
          </DataTable>
        </div>
      </div>
      <!-- 對話框操作按鈕 -->
      <Button label="新增" @click="addWorkdays" class="p-button-raised p-button-rounded" icon="pi pi-check" autofocus />
      &nbsp;&nbsp;
      <Button label="取消" v-on:click="cancelAddWorkdays" class="p-button-raised p-button-danger p-button-rounded"
        icon="pi pi-times" />
    </Dialog>
    <!-- 修改工作日對話框 -->
    <Dialog position="center" v-model:visible="showUpdateDialog" v-bind:modal="true">
      <template #header>
        <h3>{{ updateHeader }}</h3>
      </template>
      
      <!-- 彈性上班時間設定 -->
      <div class="p-field p-grid">
        <label for="FlexibleArrivalBefore" class="p-col-fixed" style="width: 220px">最早彈性上班時間(時:分)：</label>
        <div class="p-col-fixed" style="width: 300px">
          <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="FlexibleArrivalBefore"
            :disabled="!isAdmin" />
        </div>
      </div>
      
      <div class="p-fluid">
        <div class="p-field p-grid"></div>
        <!-- 正常上班時間設定 -->
        <div class="p-field p-grid">
          <label for="ArrivalTime" class="p-col-fixed" style="width: 220px">正常上班時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="ArrivalTime" :disabled="!isAdmin" />
          </div>
        </div>
        <!-- 最晚彈性上班時間設定 -->
        <div class="p-field p-grid">
          <label for="FlexibleArrivalAfter" class="p-col-fixed" style="width: 220px">最晚彈性上班時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="FlexibleArrivalAfter"
              :disabled="!isAdmin" />
          </div>
        </div>
        <!-- 用餐休息時間設定 -->
        <div class="p-field p-grid">
          <label for="MiddayBreakStart" class="p-col-fixed" style="width: 220px">用餐休息開始時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="MiddayBreakStart"
              :disabled="!isAdmin" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="MiddayBreakEnd" class="p-col-fixed" style="width: 220px">用餐休息結束時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="MiddayBreakEnd" :disabled="!isAdmin" />
          </div>
        </div>
        <!-- 前段休息時間設定 -->
        <div class="p-field p-grid">
          <label for="MorningRestStart" class="p-col-fixed" style="width: 220px">前段休息開始時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="MorningRestStart"
              :disabled="!isAdmin" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="MorningRestEnd" class="p-col-fixed" style="width: 220px">前段休息結束時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="MorningRestEnd" :disabled="!isAdmin" />
          </div>
        </div>
        <!-- 後段休息時間設定 -->
        <div class="p-field p-grid">
          <label for="AfternoonRestStart" class="p-col-fixed" style="width: 220px">後段休息開始時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="AfternoonRestStart"
              :disabled="!isAdmin" />
          </div>
        </div>
        <div class="p-field p-grid">
          <label for="AfternoonRestEnd" class="p-col-fixed" style="width: 220px">後段休息結束時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="AfternoonRestEnd"
              :disabled="!isAdmin" />
          </div>
        </div>
        <!-- 彈性下班時間設定 -->
        <div class="p-field p-grid">
          <label for="FlexibleDepartureBefore" class="p-col-fixed" style="width: 220px">最早彈性下班時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="FlexibleDepartureBefore"
              :disabled="!isAdmin" />
          </div>
        </div>
        <!-- 正常下班時間設定 -->
        <div class="p-field p-grid">
          <label for="DepartureTime" class="p-col-fixed" style="width: 220px">正常下班時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="DepartureTime" :disabled="!isAdmin" />
          </div>
        </div>
        <!-- 最晚彈性下班時間設定 -->
        <div class="p-field p-grid">
          <label for="FlexibleDepartureAfter" class="p-col-fixed" style="width: 220px">最晚彈性下班時間(時:分)：</label>
          <div class="p-col-fixed" style="width: 300px">
            <Calendar hourFormat="24" :showTime="true" :timeOnly="true" v-model="FlexibleDepartureAfter"
              :disabled="!isAdmin" />
          </div>
        </div>
        <!-- 工作日類型設定（根據星期幾顯示不同選項） -->
        <div class="p-field p-grid">
          <label for="DayType" class="p-col-fixed" style="width: 220px">本日類型：</label>
          <div class="p-col-fixed" style="width: 300px">
            <!-- 週一到週五的工作日類型選項 -->
            <Dropdown v-model="DayType" v-if="WeekDay < 6 && WeekDay > 0" :options="dayTypesNormal" optionValue="TypeId"
              optionLabel="TypeName" placeholder="請選擇 工作日類型" v-on:change="dateTypeChanged(DayType)"
              :disabled="!isAdmin" />
            <!-- 週六的工作日類型選項 -->
            <Dropdown v-model="DayType" v-if="WeekDay === 6" :options="dayTypesSat" optionValue="TypeId"
              optionLabel="TypeName" placeholder="請選擇 工作日類型" v-on:change="dateTypeChanged(DayType)"
              :disabled="!isAdmin" />
            <!-- 週日的工作日類型選項 -->
            <Dropdown v-model="DayType" v-if="WeekDay === 0" :options="dayTypesSun" optionValue="TypeId"
              optionLabel="TypeName" placeholder="請選擇 工作日類型" v-on:change="dateTypeChanged(DayType)"
              :disabled="!isAdmin" />
          </div>
        </div>
        <!-- 正常工時設定 -->
        <div class="p-field p-grid">
          <label for="DayType" class="p-col-fixed" style="width: 220px">本日正常工時：</label>
          <div class="p-col-fixed" style="width: 300px">
            <InputNumber v-model="WorkHours" :min="0" :max="12" :allowEmpty="false" :disabled="!isAdmin" />
          </div>
        </div>
        <!-- 備註欄位 -->
        <div class="p-field p-grid">
          <label for="AfternoonRestStart" class="p-col-fixed" style="width: 220px">備註：</label>
          <div class="p-col-fixed" style="width: 300px">
            <InputText v-model="Comment" type="text" placeholder="備註" :disabled="!isAdmin" />
          </div>
        </div>
      </div>
      <!-- 修改對話框操作按鈕（僅管理員可見） -->
      <Button label="修改" v-if="isAdmin" v-on:click="updateDate" class="p-button-raised p-button-rounded"
        icon="pi pi-check" autofocus />
      &nbsp;&nbsp;
      <Button label="取消" v-if="isAdmin" v-on:click="cancelUpdate"
        class="p-button-raised p-button-danger p-button-rounded" icon="pi pi-times" />
    </Dialog>
    
    <!-- 警告訊息對話框 -->
    <Dialog position="center" v-model:visible="showWarning" v-bind:modal="true">
      <h3>{{ warningMessage }}</h3>
    </Dialog>
  </div>
</template>

/**
 * 工作日曆管理頁面
 * 主要功能：
 * 1. 顯示年度工作日曆（12個月份的日曆）
 * 2. 支援建立年度工作日資料
 * 3. 支援修改每日的工作時間設定
 * 4. 管理國定假日資料
 * 5. 根據工作日類型顯示不同的日期樣式
 */
<script setup lang="ts">
// ===== Vue 3 Composition API 導入 =====
import { ref, computed, watch, onMounted, nextTick } from "vue";

// ===== PrimeVue 組件導入 =====
import DataTable from "primevue/datatable";     // 資料表格組件
import Dialog from "primevue/dialog";           // 對話框組件
import Dropdown from "primevue/dropdown";       // 下拉選單組件
import Calendar from "primevue/calendar";       // 日曆組件
import InputText from 'primevue/inputtext';     // 文字輸入框組件
import Button from 'primevue/button';           // 按鈕組件
import InputNumber from 'primevue/inputnumber'; // 數字輸入框組件
import Column from 'primevue/column';           // 資料表格欄位組件

// ===== 第三方庫導入 =====
import axios from "axios";                      // HTTP 請求庫

// ===== 狀態管理導入 =====
import { useAuthUserStore } from '../store/index';  // 使用者認證狀態管理

// ===== API 與型別導入 =====
import {
  userIsAdminUrl,      // 檢查管理員權限 API
  shiftGetUrl,         // 取得班別資料 API
  holidayUrl,          // 國定假日 API
  addWorkdaysUrl,      // 新增工作日 API
  canAddWorkdaysUrl,   // 檢查是否可新增工作日 API
  updateWorkdayUrl,    // 更新工作日 API
  getWorkdaysUrl,      // 取得工作日資料 API
  getDayTypeUrl,       // 取得日期類型 API
  Shift,               // 班別型別定義
  ShiftName,           // 班別名稱型別定義
  Workday              // 工作日型別定義
} from "../components/api";

// ===== 介面型別定義 =====
/**
 * 日期類型介面定義
 * 用於定義工作日的類型（如工作日、休息日、國定假日等）
 */
interface IDayType {
  TypeId: number;       // 類型編號
  TypeName: string;     // 類型名稱
}

// ===== 初始化服務與全域狀態 =====
const userStore = useAuthUserStore();  // 初始化使用者狀態管理

// ===== 計算屬性 =====
const isAdmin = computed(() => userStore.isAdmin);  // 計算當前使用者是否為管理員
const Year = computed(() => userStore.Year);        // 計算當前選擇的年份

// ===== 響應式狀態變數定義 =====

// --- 對話框控制相關 ---
const dialogDisplay = ref<boolean>(false);      // 控制新增工作日對話框顯示
const showUpdateDialog = ref<boolean>(false);   // 控制修改工作日對話框顯示
const showWarning = ref<boolean>(false);        // 控制警告訊息對話框顯示
const warningMessage = ref<string>("");         // 警告訊息內容
const header = ref<string>("");                 // 新增對話框標題
const updateHeader = ref<string>("");           // 修改對話框標題

// --- 權限與功能控制 ---
const canAdd = ref<boolean>(false);             // 控制是否可以新增工作日

// --- 班別相關資料 ---
const shifts = ref<Shift[]>([]);                // 所有班別資料
const distinctShifts = ref<ShiftName[]>([]);    // 去重後的班別名稱列表（用於下拉選單）
const currentShift = ref<Shift | null>(null);   // 當前選擇的班別物件
const currentWorkday = ref<Workday | null>(null); // 當前選擇的工作日物件
const ShiftId = ref<number>(1);                 // 當前選擇的班別編號
const ShiftName = ref<string>("");              // 當前選擇的班別名稱

// --- 日期與時間相關 ---
const year = ref<number>(userStore.Year);                    // 當前顯示的年份
const StartDate = ref<Date>(new Date(userStore.Year, 0, 1)); // 年度開始日期（1月1日）
const EndDate = ref<Date>(new Date(userStore.Year, 11, 31)); // 年度結束日期（12月31日）
const months = ref<Date[]>([]);                              // 12個月份的日期物件陣列
const WorkDate = ref<Date>(new Date());                      // 工作日期
const WeekDay = ref<number>(1);                              // 星期幾（0=週日, 1=週一, ..., 6=週六）

// 初始化月份數組（建立12個月份的第一天）
for (let i = 1; i < 13; i++) {
  months.value.push(new Date(year.value, i - 1, 1));
}

// --- 工作日與假日資料 ---
const workdays = ref<Workday[]>([]);            // 年度工作日資料陣列
const holidays = ref<any[]>([]);                // 國定假日資料陣列
const dayTypes = ref<IDayType[]>([]);           // 所有日期類型資料

// --- 日期類型選項（根據星期幾提供不同的選項） ---
// 週一到週五可選擇的日期類型
const dayTypesNormal = ref<IDayType[]>([
  { "TypeId": 1, "TypeName": "工作日" },         // 正常工作日
  { "TypeId": 4, "TypeName": "週間國定假日" },    // 平日遇到國定假日
  { "TypeId": 8, "TypeName": "補假日" },          // 補假
  { "TypeId": 9, "TypeName": "彈性放假日" },      // 彈性假期
  { "TypeId": 10, "TypeName": "週間天災日" },     // 平日天災假
  { "TypeId": 20, "TypeName": "週間休息日" }      // 平日休息
]);

// 週六可選擇的日期類型
const dayTypesSat = ref<IDayType[]>([
  { "TypeId": 2, "TypeName": "週六休息日" },      // 週六正常休息
  { "TypeId": 5, "TypeName": "週六國定假日" },    // 週六遇到國定假日
  { "TypeId": 7, "TypeName": "補班日" },          // 週六補班
  { "TypeId": 11, "TypeName": "週六天災日" }      // 週六天災假
]);

// 週日可選擇的日期類型
const dayTypesSun = ref<IDayType[]>([
  { "TypeId": 3, "TypeName": "例假日" },          // 週日正常例假
  { "TypeId": 6, "TypeName": "週日國定假日" }     // 週日遇到國定假日
]);

// --- 工作時間相關 ---
const DayType = ref<number>(1);                 // 當前選擇的日期類型編號
const WorkHours = ref<number>(8);               // 當日工作時數
// 以下時間變數都使用當年的1月1日作為基準日期，只取時間部分
const ArrivalTime = ref<Date>(new Date(userStore.Year + "-01-01T08:00"));           // 正常上班時間
const DepartureTime = ref<Date>(new Date(userStore.Year + "-01-01T17:00"));         // 正常下班時間
const FlexibleArrivalBefore = ref<Date>(new Date(userStore.Year + "-01-01T07:30")); // 最早彈性上班時間
const FlexibleArrivalAfter = ref<Date>(new Date(userStore.Year + "-01-01T09:00"));  // 最晚彈性上班時間
const FlexibleDepartureBefore = ref<Date>(new Date(userStore.Year + "-01-01T16:30")); // 最早彈性下班時間
const FlexibleDepartureAfter = ref<Date>(new Date(userStore.Year + "-01-01T18:00")); // 最晚彈性下班時間
const MiddayBreakStart = ref<Date>(new Date(userStore.Year + "-01-01T12:00"));      // 午休開始時間
const MiddayBreakEnd = ref<Date>(new Date(userStore.Year + "-01-01T13:00"));        // 午休結束時間
const MorningRestStart = ref<Date>(new Date(userStore.Year + "-01-01T10:00"));      // 上午休息開始時間
const MorningRestEnd = ref<Date>(new Date(userStore.Year + "-01-01T10:10"));        // 上午休息結束時間
const AfternoonRestStart = ref<Date>(new Date(userStore.Year + "-01-01T15:00"));    // 下午休息開始時間
const AfternoonRestEnd = ref<Date>(new Date(userStore.Year + "-01-01T15:10"));      // 下午休息結束時間
const Comment = ref<string>("");                // 備註欄位
// ===== 監聽器設定 =====
/**
 * 監聽年份變化
 * 當年份改變時，重新初始化所有相關資料
 */
watch(year, (newVal: number, oldVal: number) => {
  if (newVal !== oldVal) {
    year.value = newVal;
    StartDate.value = new Date(year.value, 0, 1);    // 重設年度開始日期
    EndDate.value = new Date(year.value, 11, 31);    // 重設年度結束日期
    months.value = [];                               // 清空月份陣列
    workdays.value = [];                             // 清空工作日資料
    distinctShifts.value = [];                       // 清空班別選項
    
    // 重新建立12個月份
    for (let i = 1; i < 13; i++) {
      months.value.push(new Date(year.value, i - 1, 1));
    }
    
    // 重新載入相關資料
    getShifts();                                     // 取得班別資料
    getHolidays(year.value);                         // 取得該年度國定假日
    getWorkdays(year.value, ShiftId.value);          // 取得該年度工作日資料
    checkCanAdd();                                   // 檢查是否可新增工作日
  }
});

// ===== 工具函數 =====

/**
 * 移除假日
 * 從國定假日清單中移除指定的假日項目
 * @param holiday - 要移除的假日物件
 */
const removeHoliday = (holiday: any): void => {
  for (let i = 0; i < holidays.value.length; i++) {
    if (holidays.value[i].Name == holiday.Name) {
      holidays.value.splice(i, 1);
      break;
    }
  }
};

/**
 * 檢查是否可以新增工作日
 * 向後端API查詢指定年份和班別是否已建立工作日資料
 */
const checkCanAdd = (): void => {
  const newRow = {
    year: year.value,
    shiftId: ShiftId.value,
    startDate: StartDate.value,
    holidays: holidays.value,
  };
  
  axios
    .post(canAddWorkdaysUrl, newRow)
    .then((response) => {
      if (response.data == true) {
        canAdd.value = true;  // 可以新增工作日
      } else {
        canAdd.value = false; // 已存在工作日資料，無法新增
      }
    })
    .catch((error) => {
      console.log(error);
      toastMessage(`查詢 ${year.value} 年 ${ShiftName.value} 是否可新增工作日失敗 ${error}`);
      canAdd.value = false;
    });
};

/**
 * 顯示 Toast 訊息
 * 目前暫時使用 console.log，實際應用中應使用適當的 toast 組件
 * @param message - 要顯示的訊息
 */
const toastMessage = (message: string): void => {
  // 注意：在 Composition API 中需要使用 useToast 或其他方式來顯示 toast
  console.log(message); // 暫時使用 console.log，實際應用中需要適當的 toast 實現
};
/**
 * 新增工作日
 * 根據選擇的年份、班別和國定假日資料，建立整年度的工作日資料
 */
const addWorkdays = (): void => {
  canAdd.value = false;  // 設定為不可新增狀態，避免重複操作
  
  const newRow = {
    year: year.value,
    shiftId: ShiftId.value,
    startDate: StartDate.value,
    holidays: holidays.value,
  };
  
  axios
    .post(addWorkdaysUrl, newRow)
    .then((response) => {
      if (response.data == true) {
        toastMessage(`新增 ${year.value} 年 ${ShiftName.value} 工作日成功`);
        getWorkdays(year.value, ShiftId.value);  // 重新載入工作日資料
        canAdd.value = false;                    // 成功後設為不可新增
      } else {
        toastMessage(`新增 ${year.value} 年 ${ShiftName.value} 工作日失敗`);
        canAdd.value = true;                     // 失敗後恢復可新增狀態
      }
    })
    .catch((error) => {
      console.log(error);
      toastMessage(`新增 ${year.value} 年 ${ShiftName.value} 工作日失敗 ${error}`);
      canAdd.value = true;                       // 錯誤後恢復可新增狀態
    });
    
  getWorkdays(year.value, ShiftId.value);        // 重新載入工作日資料
  dialogDisplay.value = false;                   // 關閉對話框
};

/**
 * 將日期轉換為字串格式
 * 格式：YYYY/M/D（月份和日期不補零）
 * @param d - 日期物件
 * @returns 格式化的日期字串
 */
const toDateString = (d: Date): string => {
  return `${d.getFullYear()}/${d.getMonth() + 1}/${d.getDate()}`;
};

/**
 * 將日期轉換為日期時間字串格式
 * 格式：YYYY/M/D H:M:00（完整的日期時間格式）
 * @param d - 日期物件
 * @returns 格式化的日期時間字串
 */
const toDateTimeString = (d: Date): string => {
  return d.getFullYear() + "/" + (d.getMonth() + 1) + "/" + d.getDate() + " " + d.getHours() + ":" + d.getMinutes() + ":00";
};

/**
 * 將年月日轉換為特定格式的日期字串
 * 格式：YYYY-MM-DDTHH:mm:ss（ISO 格式，用於API傳送）
 * @param year - 年份
 * @param month - 月份（0-11）
 * @param day - 日期
 * @returns 格式化的日期字串
 */
const toDateStrX = (year: number, month: number, day: number): string => {
  let ret = year + "-";
  // 月份處理（月份+1因為傳入的是0-11，需要轉為1-12）
  if (month < 9) {
    ret += "0" + (month + 1);
  } else {
    ret += month + 1;
  }
  // 日期處理（補零至兩位數）
  if (day < 10) {
    ret += "-0" + day + "T00:00:00";
  } else {
    ret += "-" + day + "T00:00:00";
  }
  return ret;
};

/**
 * 日期類型變更時的處理
 * 根據不同的日期類型自動設定對應的工作時數
 * @param typeId - 日期類型ID
 */
const dateTypeChanged = (typeId: number): void => {
  switch (typeId) {
    case 1: // 工作日
    case 7: // 補班日
      WorkHours.value = 8;  // 設定為8小時工作日
      break;
    case 2:  // 週六休息日
    case 3:  // 例假日
    case 4:  // 週間國定假日
    case 5:  // 週六國定假日
    case 6:  // 週日國定假日
    case 8:  // 補假日
    case 9:  // 彈性放假日
    case 10: // 週間天災日
    case 11: // 週六天災日
    case 20: // 週間休息日
      WorkHours.value = 0;  // 設定為0小時（非工作日）
      break;
  }
};

/**
 * 班別變更時的處理
 * 當使用者選擇不同班別時，更新相關資料並重新載入工作日和假日資料
 */
const shiftChange = (): void => {
  distinctShifts.value = [];  // 清空班別選項
  const m = new Map<number, string>();
  
  // 遍歷所有班別，建立班別ID和名稱的對應關係
  for (let i = 0; i < shifts.value.length; i++) {
    m.set(shifts.value[i].ShiftId, shifts.value[i].ShiftName);
    
    // 如果找到當前選擇的班別，更新相關變數
    if (shifts.value[i].ShiftId === ShiftId.value) {
      ShiftName.value = shifts.value[i].ShiftName;
      currentShift.value = shifts.value[i];
    }
  }
  
  // 將Map轉換為下拉選單所需的格式
  m.forEach((value, key) => {
    const temp: ShiftName = { ShiftId: key, ShiftName: value };
    distinctShifts.value.push(temp);
  });
  
  // 重新檢查權限和載入資料
  checkCanAdd();                              // 檢查是否可新增工作日
  getHolidays(year.value);                    // 重新載入國定假日
  getWorkdays(year.value, ShiftId.value);     // 重新載入工作日資料
};

/**
 * 去除字串的空值
 * 處理可能為null或空字串的字串，確保返回有效字串
 * @param theStr - 要處理的字串
 * @returns 處理後的字串
 */
const trimNull = (theStr: string | null): string => {
  if (theStr === null || theStr === "") {
    return "";
  } else {
    return theStr.trim();  // 移除前後空白字元
  }
};

/**
 * 顯示更新對話框
 * 當使用者點擊日曆上的日期時，顯示該日期的工作時間設定對話框
 * @param date - 選擇的日期
 */
const showUpdate = (date: Date): void => {
  // 檢查是否已建立行事曆
  if (shifts.value.length === 0 || canAdd.value) {
    toastMessage(`尚未建立行事曆`);
    return;
  }
  
  // 取得選擇的日期資訊
  const selectedYear = date.getFullYear();
  const selectedMonth = date.getMonth();
  const selectedDay = date.getDate();
  
  // 設定對話框標題
  updateHeader.value = `修改 ${ShiftName.value} ${selectedYear} 年 ${selectedMonth + 1} 月 ${selectedDay} 日`;
  if (!isAdmin.value) {
    // 非管理員只能查看，不能修改
    updateHeader.value = `${ShiftName.value} ${selectedYear} 年 ${selectedMonth + 1} 月 ${selectedDay} 日`;
  }
  
  // 在工作日資料中尋找對應的日期
  const elem = workdays.value.find((x) => {
    const y = new Date(x.WorkDate);
    return y.getFullYear() === selectedYear && y.getMonth() === selectedMonth && y.getDate() === selectedDay;
  });

  if (elem !== null && elem !== undefined) {
    // 找到對應的工作日資料，將資料載入到表單中
    currentWorkday.value = elem;
    ArrivalTime.value = new Date(elem.ArrivalTime);
    DepartureTime.value = new Date(elem.DepartureTime);
    FlexibleArrivalBefore.value = new Date(elem.FlexibleArrivalBefore);
    FlexibleArrivalAfter.value = new Date(elem.FlexibleArrivalAfter);
    FlexibleDepartureBefore.value = new Date(elem.FlexibleDepartureBefore);
    FlexibleDepartureAfter.value = new Date(elem.FlexibleDepartureAfter);
    MiddayBreakStart.value = new Date(elem.MiddayBreakStart);
    MiddayBreakEnd.value = new Date(elem.MiddayBreakEnd);
    MorningRestStart.value = new Date(elem.MorningRestStart);
    MorningRestEnd.value = new Date(elem.MorningRestEnd);
    AfternoonRestStart.value = new Date(elem.AfternoonRestStart);
    AfternoonRestEnd.value = new Date(elem.AfternoonRestEnd);
    DayType.value = elem.DayType;
    WorkHours.value = elem.WorkHours;
    Comment.value = elem.Comment;
    WeekDay.value = elem.WeekDay;
  } else {
    // 找不到對應的工作日資料
    toastMessage(`尚未建立行事曆`);
    return;
  }
  
  showUpdateDialog.value = true;  // 顯示修改對話框
};
/**
 * 更新日期資料
 * 將使用者修改的工作時間設定更新到後端資料庫
 */
const updateDate = (): void => {
  const cDay = currentWorkday.value;
  if (cDay != null) {
    // 將表單中的資料更新到工作日物件
    cDay.ShiftId = ShiftId.value;
    cDay.ArrivalTime = ArrivalTime.value;
    cDay.DepartureTime = DepartureTime.value;
    cDay.FlexibleArrivalBefore = FlexibleArrivalBefore.value;
    cDay.FlexibleArrivalAfter = FlexibleArrivalAfter.value;
    cDay.FlexibleDepartureBefore = FlexibleDepartureBefore.value;
    cDay.FlexibleDepartureAfter = FlexibleDepartureAfter.value;
    cDay.MiddayBreakStart = MiddayBreakStart.value;
    cDay.MiddayBreakEnd = MiddayBreakEnd.value;
    cDay.MorningRestStart = MorningRestStart.value;
    cDay.MorningRestEnd = MorningRestEnd.value;
    cDay.AfternoonRestStart = AfternoonRestStart.value;
    cDay.AfternoonRestEnd = AfternoonRestEnd.value;
    cDay.DayType = DayType.value;
    cDay.WorkHours = WorkHours.value;
    cDay.WeekDay = WeekDay.value;
    cDay.Comment = trimNull(Comment.value);

    // 呼叫後端API更新工作日資料
    axios
      .put(updateWorkdayUrl, cDay)
      .then((response) => {
        const date = new Date(cDay.WorkDate);
        if (response.data == true) {
          toastMessage(`更新 ${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 成功`);
        } else {
          toastMessage(`更新 ${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 失敗`);
        }
      })
      .catch((error) => {
        const date = new Date(cDay.WorkDate);
        toastMessage(`更新 ${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 失敗 ${error}`);
      });
    showUpdateDialog.value = false;  // 關閉修改對話框
  }
};

/**
 * 取消更新
 * 關閉修改對話框，放棄所有修改
 */
const cancelUpdate = (): void => {
  showUpdateDialog.value = false;
};

/**
 * 取消新增工作日
 * 關閉新增工作日對話框
 */
const cancelAddWorkdays = (): void => {
  dialogDisplay.value = false;
};
/**
 * 顯示新增對話框
 * 開啟新增年度工作日的對話框
 */
const showAddDialog = (): void => {
  // 檢查是否已有班別資料
  if (shifts.value.length == 0) {
    showWarning.value = true;
    warningMessage.value = "請先建立年度行事曆，再新增年度工作日";
    return;
  }
  
  dialogDisplay.value = true;           // 顯示新增對話框
  header.value = addWorkdayTitle();     // 設定對話框標題
  ShiftName.value = "";                 // 清空班別名稱
};

/**
 * 新增工作日標題
 * 產生新增工作日對話框的標題文字
 * @returns 標題字串
 */
const addWorkdayTitle = (): string => {
  return `新增 ${ShiftName.value} ${year.value} 年度工作日`;
};

/**
 * 取得指定月份和日期的 Date 物件
 * 用於設定日曆組件的最小和最大日期範圍
 * @param theMonth - 月份索引（0-11）
 * @param theDay - 日期
 * @returns Date 物件
 */
const getTheDate = (theMonth: number, theDay: number): Date => {
  return new Date(year.value, theMonth, theDay);
};

/**
 * 取得 CSS 類別名稱
 * 根據工作日類型返回對應的CSS類別，用於在日曆上顯示不同顏色
 * @param year - 年份
 * @param month - 月份（0-11）
 * @param day - 日期
 * @returns CSS 類別名稱
 */
const getCssClass = (year: number, month: number, day: number): string => {
  let ret = "";
  
  // 在工作日資料中尋找對應的日期
  const elem = workdays.value.find((x) => {
    const y = new Date(x.WorkDate);
    return y.getFullYear() === year && y.getMonth() === month && y.getDate() === day;
  });
  
  if (elem !== null && elem !== undefined) {
    // 根據不同的日期類型設定對應的CSS類別
    // DayType 對照表：
    // 1-工作日 2-週六休息日 3-例假日 4-週間國定假日 5-週六國定假日 
    // 6-週日國定假日 7-補班日 8-補假日 9-彈性放假日 10-週間天災日 11-週六天災日 20-週間休息日
    switch (elem.DayType) {
      case 1:
        ret = "working-day";           // 藍色 - 正常工作日
        break;
      case 7:
        ret = "working-day bold";      // 藍色粗體 - 補班日
        break;
      case 2:
      case 3:
        ret = "work-offday";           // 粉色 - 休息日/例假日
        break;
      case 4:
      case 5:
      case 6:
      case 8:
      case 10:
      case 11:
        ret = "work-holiday";          // 紅色 - 國定假日/補假日/天災日
        break;
      case 9:
        ret = "work-holiday bold";     // 紅色粗體 - 彈性放假日
        break;
      default:
        ret = "";                      // 無特殊樣式
        break;
    }
  }
  return ret;
};
/**
 * 判斷是否為工作日
 * 檢查指定日期是否為需要上班的工作日
 * @param year - 年份
 * @param month - 月份（0-11）
 * @param day - 日期
 * @returns 是否為工作日
 */
const isWorkDay = (year: number, month: number, day: number): boolean => {
  let ret = false;
  
  // 在工作日資料中尋找對應的日期
  const elem = workdays.value.find(
    (x) =>
      new Date(x.WorkDate).getFullYear() === year &&
      new Date(x.WorkDate).getMonth() === month &&
      new Date(x.WorkDate).getDate() === day
  );
  
  if (undefined !== elem && elem !== null) {
    // 只有工作日(1)和補班日(7)才算是真正的工作日
    // 當日類型 DayType 對照：
    // 1-工作日 2-週六休息日 3-例假日 4-週間國定假日 5-週六國定假日 
    // 6-週日國定假日 7-補班日 8-補假日 9-彈性放假日 10-週間天災日 11-週六天災日 20-週間休息日
    ret = elem.DayType == 1 || elem.DayType == 7;
  }
  return ret;
};

/**
 * 取得月份名稱
 * 將日期字串轉換為本地化的月份名稱
 * @param dtStr - 日期字串
 * @returns 月份名稱（繁體中文）
 */
const getMonthName = (dtStr: string): string => {
  const date = new Date(dtStr);
  if (date.toString() !== "Invalid Date") 
    return date.toLocaleString("zh-tw", { month: "long" });
  else 
    return "不知道";
};
// ===== 年份操作函數 =====

/**
 * 切換到前一年
 * 將當前顯示年份減1並重新載入資料
 */
const prevYear = (): void => {
  year.value--;
  changeYear();
};

/**
 * 切換到後一年
 * 將當前顯示年份加1並重新載入資料
 */
const nextYear = (): void => {
  year.value++;
  changeYear();
};

/**
 * 年份變更處理
 * 當年份改變時，更新狀態管理中的年份並重新初始化所有相關資料
 */
const changeYear = (): void => {
  userStore.setYear(year.value);        // 更新狀態管理中的年份
  months.value = [];                    // 清空月份陣列
  workdays.value = [];                  // 清空工作日資料
  distinctShifts.value = [];            // 清空班別選項
  
  // 重新建立12個月份的第一天
  for (let i = 1; i < 13; i++) {
    months.value.push(new Date(year.value, i - 1, 1));
  }
  
  // 重新載入所有相關資料
  getShifts();                          // 載入班別資料
  getHolidays(year.value);              // 載入該年度國定假日
  getWorkdays(year.value, ShiftId.value); // 載入該年度工作日資料
};
// ===== API 呼叫函數 =====

/**
 * 計算指定月份的天數
 * 用於日曆相關計算（目前未使用，但保留供未來擴展）
 * @param year - 年份
 * @param month - 月份（1-12，注意這裡是1-based）
 * @returns 該月份的天數
 */
const getDaysInMonth = (year: number, month: number): number => {
  // 這裡 January 是 1-based（與一般的0-based不同）
  // Day 0 表示前一個月的最後一天，所以這樣可以取得指定月份的天數
  return new Date(year, month, 0).getDate();
};

/**
 * 取得所有班別資料
 * 從後端API載入班別資料並更新班別選項
 */
const getShifts = (): void => {
  axios
    .get(shiftGetUrl)
    .then((res) => {
      shifts.value = res.data;    // 儲存班別資料
      shiftChange();              // 更新班別選項和相關資料
    })
    .catch((error) => {
      console.error(error);
    });
};

/**
 * 取得所有日期類型資料
 * 從後端API載入日期類型選項（工作日、休息日、國定假日等）
 */
const getDayTypes = (): void => {
  axios
    .get(getDayTypeUrl)
    .then((res) => {
      dayTypes.value = res.data;  // 儲存日期類型資料
    })
    .catch((error) => {
      console.error(error);
    });
};

/**
 * 取得指定年份和班別的工作日資料
 * @param year - 年份
 * @param shiftId - 班別編號
 */
const getWorkdays = (year: number, shiftId: number): void => {
  axios
    .get(getWorkdaysUrl + "?year=" + year + "&shiftId=" + shiftId)
    .then((res) => {
      workdays.value = res.data;  // 儲存工作日資料
    })
    .catch((error) => {
      console.error(error);
    });
};

/**
 * 取得指定年份的國定假日資料
 * @param year - 年份
 */
const getHolidays = (year: number): void => {
  axios
    .get(holidayUrl + year)
    .then((res) => {
      holidays.value = res.data;  // 儲存國定假日資料
    })
    .catch((error) => {
      console.error(error);
    });
};

// ===== 元件生命週期 =====
/**
 * 元件掛載完成後的初始化
 * 按序載入所有必要的資料，確保頁面正確顯示
 */
onMounted(async () => {
  checkCanAdd();                                // 檢查是否可新增工作日
  await getShifts();                            // 載入班別資料
  await getHolidays(year.value);                // 載入國定假日資料
  await getWorkdays(year.value, ShiftId.value); // 載入工作日資料
  await getDayTypes();                          // 載入日期類型資料
  await nextTick();                             // 等待DOM更新完成
});

</script>

<!-- 自定義樣式：定義不同工作日類型的顯示顏色 -->
<style>
/* 工作日樣式 - 藍色文字 */
.working-day {
  color: blue;
}

/* 國定假日樣式 - 紅色文字 */
.work-holiday {
  color: red;
}

/* 休息日樣式 - 粉色文字 */
.work-offday {
  color: pink;
}

/* 補班日樣式 - 綠色文字（目前未使用） */
.work-make-up-day {
  color: green;
}

/* 粗體樣式 - 用於特殊日期（如補班日、彈性放假日） */
.bold {
  font-weight: bold;
}

/* 日曆格式調整 */
.p-datepicker table td {
  padding: 0.05rem !important;  /* 縮小日期格的間距 */
}

/* 隱藏其他月份的日期 */
.p-datepicker-other-month>.p-disabled {
  display: none;
}
</style>
