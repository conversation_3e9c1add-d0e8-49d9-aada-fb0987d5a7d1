<template>
  <div>

    <EmpWorkShift />
  </div>
</template>

<script setup lang="ts">
// ===== Vue 3 Composition API 導入 =====
import { onMounted } from "vue";

// ===== 自訂組件導入 =====
import EmpWorkShift from "../components/EmpWorkShift.vue";

// ===== 生命週期鉤子 =====

/**
 * 組件掛載完成後的初始化處理
 * 可以在這裡進行頁面特定的初始化工作
 */
onMounted(() => {
  // 設定頁面標題
  document.title = "員工個別化班別管理 - 中興社工作日曆";
});
</script>

<style scoped>
h1 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

p {
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.6;
}
</style>