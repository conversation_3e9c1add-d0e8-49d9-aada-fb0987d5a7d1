import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import { createPinia } from "pinia";
import PrimeVue from "primevue/config";
import ToastService from "primevue/toastservice";
import InputNumber from "primevue/inputnumber";
import InputText from "primevue/inputtext";
import Button from "primevue/button";
import Dialog from "primevue/dialog";
import Toast from "primevue/toast";
import DataTable from "primevue/datatable";
import Dropdown from 'primevue/dropdown';
import Calendar from "primevue/calendar";
import Ripple from 'primevue/ripple';
import ConfirmationService from "primevue/confirmationservice";
import "@fullcalendar/core/vdom";
import "@fullcalendar/core";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";

import FullCalendar from "primevue/fullcalendar";
import Column from "primevue/column";
import ColumnGroup from "primevue/columngroup";
import "primevue/resources/themes/saga-blue/theme.css";
//import 'primevue/resources/themes/bootstrap4-light-blue/theme.css';
import "primevue/resources/primevue.min.css";
import "primeicons/primeicons.css";
import "primeflex/primeflex.css";

const app = createApp(App);
const pinia = createPinia();
app.use(pinia);
app.use(PrimeVue, {
  //使用PrimeVue
  ripple: true,
  locale: {
    accept: "是",
    reject: "否",
    choose: "選擇",
    upload: "上傳",
    cancel: "取消",
    dayNames: [
      "星期日",
      "星期一",
      "星期二",
      "星期三",
      "星期四",
      "星期五",
      "星期六",
    ],
    dayNamesShort: ["週日", "週一", "週二", "週三", "週四", "週五", "週六"],
    dayNamesMin: ["日", "一", "二", "三", "四", "五", "六"],
    monthNames: [
      "一月",
      "二月",
      "三月",
      "四月",
      "五月",
      "六月",
      "七月",
      "八月",
      "九月",
      "十月",
      "十一月",
      "十二月",
    ],
    monthNamesShort: [
      "一月",
      "二月",
      "三月",
      "四月",
      "五月",
      "六月",
      "七月",
      "八月",
      "九月",
      "十月",
      "十一月",
      "十二月",
    ],
    today: "今天",
    clear: "清楚",
    weekHeader: "週",
    firstDayOfWeek: 0,
    dateFormat: "yy/mm/dd",
    weak: "弱",
    medium: "中",
    strong: "強",
    passwordPrompt: "輸入密碼",
  },
});

app.use(ConfirmationService);
app.use(ToastService); //用Toast必備
app.use(router);
app.directive('ripple', Ripple);
app.component("InputNumber", InputNumber);
app.component("InputText", InputText);
app.component("Button", Button);
app.component("Toast", Toast);
app.component("Dialog ", Dialog);
app.component("DataTable", DataTable);
app.component("Dropdown", Dropdown);
app.component("Calendar", Calendar);
app.component("FullCalendar", FullCalendar);
app.component("dayGridPlugin ", dayGridPlugin);
app.component("timeGridPlugin ", timeGridPlugin);
app.component("interactionPlugin ", interactionPlugin);
app.component("Column", Column);
app.component("ColumnGroup", ColumnGroup);
app.mount("#app");
