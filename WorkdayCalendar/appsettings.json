{"ConnectionStrings": {"Workday-orig": "server=sqlmis.sinotech.org.tw;uid=WorkdayAdmin;password=*************;database=Workday;TrustServerCertificate=true;pooling=true;Application Name=WorkCalendar;Connection Timeout=600;", "Workday-dev": "server=attendancedev.sinotech.org.tw;uid=sa;password=********;database=Workday;TrustServerCertificate=true;pooling=true;Application Name=WorkCalendar;Connection Timeout=600;", "Workday": "server=localhost;uid=sa;password=********;database=Workday;TrustServerCertificate=true;pooling=true;Application Name=WorkCalendar;Connection Timeout=600;", "Workday-home": "server=************;uid=sa;password=********;database=Workday;TrustServerCertificate=true;pooling=true;Application Name=WorkCalendar;Connection Timeout=600;", "MIS": "server=sqlmis.sinotech.org.tw;uid=CardFormUser;password=formuser@card;database=MIS;TrustServerCertificate=true;pooling=false;Application Name=WorkCalendar;Connection Timeout=600;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "SpecialEmployees": {}, "DatabaseSettings": {"CommandTimeoutSeconds": 36000, "CommandTimeoutComment": "資料庫命令執行逾時時間（秒），36000秒 = 10小時"}}