﻿using System;

namespace WorkdayCalendar
{
    /// <summary>
    /// 中國農曆日期處理類別
    /// 提供農曆日期計算、節氣查詢、星座查詢等功能。
    /// </summary>
    public class CNDate
    {
        /// <summary>
        /// 農曆計算支援的起始年份 (公曆)
        /// </summary>
        private const ushort START_YEAR = 1901;

        /// <summary>
        /// 農曆計算支援的結束年份 (公曆)
        /// </summary>
        private const ushort END_YEAR = 2050;

        /// <summary>
        /// 星座名稱陣列 (按順序排列)
        /// </summary>
        private string[] ConstellationName =
              {
              "白羊座", "金牛座", "雙子座",
              "巨蟹座", "獅子座", "處女座",
              "天秤座", "天蠍座", "射手座",
              "摩羯座", "水瓶座", "雙魚座"};

        /// <summary>
        /// 二十四節氣名稱陣列 (按順序排列)
        /// </summary>
        private string[] LunarHolidayName =
              {
              "小寒", "大寒", "立春", "雨水",
              "驚蟄", "春分", "清明", "谷雨",
              "立夏", "小滿", "芒種", "夏至",
              "小暑", "大暑", "立秋", "處暑",
              "白露", "秋分", "寒露", "霜降",
              "立冬", "小雪", "大雪", "冬至"};
        /// <summary>
        /// 農曆每個月的天數資料 ( 紀錄 1901 年到 2050 年的每一個月 )
        /// 農曆每月只能是 29 或 30 天，一年用 12（或13）個二進制位表示，對應位元為 1 表 30 天，否則為 29 天
        /// 每個整數代表一年，其二進制表示中的位元決定了該月是大月(30天)還是小月(29天)。
        /// 從高位元到低位元依序代表農曆月份，若有閏月則會多一位。
        /// 例如：0x4ae0 (0100101011100000)
        /// 若無閏月，則看前12位；若有閏月，則看前13位。
        /// 位元為 1 表示該月為大月 (30 天)，為 0 表示小月 (29 天)。
        /// </summary>
        private int[] gLunarMonthDay = {
              0x4ae0, 0xa570, 0x5268, 0xd260, 0xd950, 0x6aa8, 0x56a0, 0x9ad0, 0x4ae8, 0x4ae0, //1910
              0xa4d8, 0xa4d0, 0xd250, 0xd548, 0xb550, 0x56a0, 0x96d0, 0x95b0, 0x49b8, 0x49b0, //1920
              0xa4b0, 0xb258, 0x6a50, 0x6d40, 0xada8, 0x2b60, 0x9570, 0x4978, 0x4970, 0x64b0, //1930
              0xd4a0, 0xea50, 0x6d48, 0x5ad0, 0x2b60, 0x9370, 0x92e0, 0xc968, 0xc950, 0xd4a0, //1940
              0xda50, 0xb550, 0x56a0, 0xaad8, 0x25d0, 0x92d0, 0xc958, 0xa950, 0xb4a8, 0x6ca0, //1950
              0xb550, 0x55a8, 0x4da0, 0xa5b0, 0x52b8, 0x52b0, 0xa950, 0xe950, 0x6aa0, 0xad50, //1960
              0xab50, 0x4b60, 0xa570, 0xa570, 0x5260, 0xe930, 0xd950, 0x5aa8, 0x56a0, 0x96d0, //1970
              0x4ae8, 0x4ad0, 0xa4d0, 0xd268, 0xd250, 0xd528, 0xb540, 0xb6a0, 0x96d0, 0x95b0, //1980
              0x49b0, 0xa4b8, 0xa4b0, 0xb258, 0x6a50, 0x6d40, 0xada0, 0xab60, 0x9370, 0x4978, //1990
              0x4970, 0x64b0, 0x6a50, 0xea50, 0x6b28, 0x5ac0, 0xab60, 0x9368, 0x92e0, 0xc960, //2000
              0xd4a8, 0xd4a0, 0xda50, 0x5aa8, 0x56a0, 0xaad8, 0x25d0, 0x92d0, 0xc958, 0xa950, //2010
              0xb4a0, 0xb550, 0xb550, 0x55a8, 0x4ba0, 0xa5b0, 0x52b8, 0x52b0, 0xa930, 0x74a8, //2020
              0x6aa0, 0xad50, 0x4da8, 0x4b60, 0x9570, 0xa4e0, 0xd260, 0xe930, 0xd530, 0x5aa0, //2030
              0x6b50, 0x96d0, 0x4ae8, 0x4ad0, 0xa4d0, 0xd258, 0xd250, 0xd520, 0xdaa0, 0xb5a0, //2040
              0x56d0, 0x4ad8, 0x49b0, 0xa4b8, 0xa4b0, 0xaa50, 0xb528, 0x6d20, 0xada0, 0x55b0}; //2050

        /// <summary>
        /// 農曆每年閏月的月份資料 ( 紀錄 1901 年到 2050 年 )
        /// 每個 byte 儲存兩年的閏月資訊。
        /// 高 4 位元 (nibble) 代表第一年，低 4 位元代表第二年。
        /// 值為 0 表示該年沒有閏月，非 0 值表示閏月的月份 (1-12)。
        /// 例如：0x50 表示第一年閏五月，第二年無閏月。
        ///       0x04 表示第一年無閏月，第二年閏四月。
        /// </summary>
        byte[] gLunarMonth ={
              0x00, 0x50, 0x04, 0x00, 0x20, //1910
              0x60, 0x05, 0x00, 0x20, 0x70, //1920
              0x05, 0x00, 0x40, 0x02, 0x06, //1930
              0x00, 0x50, 0x03, 0x07, 0x00, //1940
              0x60, 0x04, 0x00, 0x20, 0x70, //1950
              0x05, 0x00, 0x30, 0x80, 0x06, //1960
              0x00, 0x40, 0x03, 0x07, 0x00, //1970
              0x50, 0x04, 0x08, 0x00, 0x60, //1980
              0x04, 0x0a, 0x00, 0x60, 0x05, //1990
              0x00, 0x30, 0x80, 0x05, 0x00, //2000
              0x40, 0x02, 0x07, 0x00, 0x50, //2010
              0x04, 0x09, 0x00, 0x60, 0x04, //2020
              0x00, 0x20, 0x60, 0x05, 0x00, //2030
              0x30, 0xb0, 0x06, 0x00, 0x50, //2040
              0x02, 0x07, 0x00, 0x50, 0x03}; //2050

        /// <summary>
        /// 每年二十四節氣對應的公曆日期資料 ( 紀錄 1901 年到 2050 年 )
        /// 每個 byte 儲存一個月份的兩個節氣資訊。
        /// 高 4 位元 (nibble) 代表該月第一個節氣的日期偏移量 (相對於15號)。計算方式：15 - 實際日期。
        /// 低 4 位元 (nibble) 代表該月第二個節氣的日期偏移量 (相對於15號)。計算方式：實際日期 - 15。
        /// 實際日期 = 15 - 高4位元值 (第一個節氣)
        /// 實際日期 = 15 + 低4位元值 (第二個節氣)
        /// 例如：0x96
        ///   高4位元: 9 => 第一個節氣日期: 15 - 9 = 6日
        ///   低4位元: 6 => 第二個節氣日期: 15 + 6 = 21日
        /// 陣列中每12個 byte 代表一年 (12個月)。
        /// 
        /// 位元陣列 gLanarHoliDay 存放每年的二十四節氣對應的陽曆日期
        /// 每年的二十四節氣對應的陽曆日期幾乎固定，平均分佈於十二個月中
        /// 1月 2月 3月 4月 5月 6月
        /// 小寒 大寒 立春 雨水 驚蟄 春分 清明 谷雨 立夏 小滿 芒種 夏至
        /// 7月 8月 9月 10月 11月 12月
        /// 小暑 大暑 立秋 處暑 白露 秋分 寒露 霜降 立冬 小雪 大雪 冬至
        //*********************************************************************************
        /// 節氣無任何確定規律,所以只好建立表格對應
        //**********************************************************************************}
        /// 數據格式說明:
        /// 如 1901 年的節氣為
        /// 1月 2月 3月 4月 5月 6月 7月 8月 9月 10月 11月 12月
        /// 6, 21, 4, 19, 6, 21, 5, 21, 6,22, 6,22, 8, 23, 8, 24, 8, 24, 8, 24, 8, 23, 8, 22
        /// 9, 6, 11,4, 9, 6, 10,6, 9,7, 9,7, 7, 8, 7, 9, 7, 9, 7, 9, 7, 8, 7, 15
        /// 上面第一行數據為每月節氣對應日期,15減去每月第一個節氣,每月第二個節氣減去15得第二行
        /// 這樣每月兩個節氣對應數據都小於16,每月用一個陣列元素存放
        /// 高位元(high-bit)存放第一個節氣數據,低位元(low-bit)存放第二個節氣的數據,可得下表
        /// </summary>
        byte[] gLunarHoliDay ={
              0x96, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x78, 0x79, 0x79, 0x69, 0x78, 0x77, //1901
              0x96, 0xA4, 0x96, 0x96, 0x97, 0x87, 0x79, 0x79, 0x79, 0x69, 0x78, 0x78, //1902
              0x96, 0xA5, 0x87, 0x96, 0x87, 0x87, 0x79, 0x69, 0x69, 0x69, 0x78, 0x78, //1903
              0x86, 0xA5, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x79, 0x78, 0x87, //1904
              0x96, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x78, 0x79, 0x79, 0x69, 0x78, 0x77, //1905
              0x96, 0xA4, 0x96, 0x96, 0x97, 0x97, 0x79, 0x79, 0x79, 0x69, 0x78, 0x78, //1906
              0x96, 0xA5, 0x87, 0x96, 0x87, 0x87, 0x79, 0x69, 0x69, 0x69, 0x78, 0x78, //1907
              0x86, 0xA5, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x69, 0x78, 0x87, //1908
              0x96, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x78, 0x79, 0x79, 0x69, 0x78, 0x77, //1909
              0x96, 0xA4, 0x96, 0x96, 0x97, 0x97, 0x79, 0x79, 0x79, 0x69, 0x78, 0x78, //1910
              0x96, 0xA5, 0x87, 0x96, 0x87, 0x87, 0x79, 0x69, 0x69, 0x69, 0x78, 0x78, //1911
              0x86, 0xA5, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x69, 0x78, 0x87, //1912
              0x95, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x78, 0x79, 0x79, 0x69, 0x78, 0x77, //1913
              0x96, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x79, 0x79, 0x79, 0x69, 0x78, 0x78, //1914
              0x96, 0xA5, 0x97, 0x96, 0x97, 0x87, 0x79, 0x79, 0x69, 0x69, 0x78, 0x78, //1915
              0x96, 0xA5, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x79, 0x77, 0x87, //1916
              0x95, 0xB4, 0x96, 0xA6, 0x96, 0x97, 0x78, 0x79, 0x78, 0x69, 0x78, 0x87, //1917
              0x96, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x79, 0x79, 0x79, 0x69, 0x78, 0x77, //1918
              0x96, 0xA5, 0x97, 0x96, 0x97, 0x87, 0x79, 0x79, 0x69, 0x69, 0x78, 0x78, //1919
              0x96, 0xA5, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x79, 0x77, 0x87, //1920
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x78, 0x79, 0x78, 0x69, 0x78, 0x87, //1921
              0x96, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x79, 0x79, 0x79, 0x69, 0x78, 0x77, //1922
              0x96, 0xA4, 0x96, 0x96, 0x97, 0x87, 0x79, 0x79, 0x69, 0x69, 0x78, 0x78, //1923
              0x96, 0xA5, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x79, 0x77, 0x87, //1924
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x78, 0x79, 0x78, 0x69, 0x78, 0x87, //1925
              0x96, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x78, 0x79, 0x79, 0x69, 0x78, 0x77, //1926
              0x96, 0xA4, 0x96, 0x96, 0x97, 0x87, 0x79, 0x79, 0x79, 0x69, 0x78, 0x78, //1927
              0x96, 0xA5, 0x96, 0xA5, 0x96, 0x96, 0x88, 0x78, 0x78, 0x78, 0x87, 0x87, //1928
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x79, 0x77, 0x87, //1929
              0x96, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x78, 0x79, 0x79, 0x69, 0x78, 0x77, //1930
              0x96, 0xA4, 0x96, 0x96, 0x97, 0x87, 0x79, 0x79, 0x79, 0x69, 0x78, 0x78, //1931
              0x96, 0xA5, 0x96, 0xA5, 0x96, 0x96, 0x88, 0x78, 0x78, 0x78, 0x87, 0x87, //1932
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x69, 0x78, 0x87, //1933
              0x96, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x78, 0x79, 0x79, 0x69, 0x78, 0x77, //1934
              0x96, 0xA4, 0x96, 0x96, 0x97, 0x97, 0x79, 0x79, 0x79, 0x69, 0x78, 0x78, //1935
              0x96, 0xA5, 0x96, 0xA5, 0x96, 0x96, 0x88, 0x78, 0x78, 0x78, 0x87, 0x87, //1936
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x69, 0x78, 0x87, //1937
              0x96, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x78, 0x79, 0x79, 0x69, 0x78, 0x77, //1938
              0x96, 0xA4, 0x96, 0x96, 0x97, 0x97, 0x79, 0x79, 0x79, 0x69, 0x78, 0x78, //1939
              0x96, 0xA5, 0x96, 0xA5, 0x96, 0x96, 0x88, 0x78, 0x78, 0x78, 0x87, 0x87, //1940
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x69, 0x78, 0x87, //1941
              0x96, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x78, 0x79, 0x79, 0x69, 0x78, 0x77, //1942
              0x96, 0xA4, 0x96, 0x96, 0x97, 0x97, 0x79, 0x79, 0x79, 0x69, 0x78, 0x78, //1943
              0x96, 0xA5, 0x96, 0xA5, 0xA6, 0x96, 0x88, 0x78, 0x78, 0x78, 0x87, 0x87, //1944
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x79, 0x77, 0x87, //1945
              0x95, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x78, 0x79, 0x78, 0x69, 0x78, 0x77, //1946
              0x96, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x79, 0x79, 0x79, 0x69, 0x78, 0x78, //1947
              0x96, 0xA5, 0xA6, 0xA5, 0xA6, 0x96, 0x88, 0x88, 0x78, 0x78, 0x87, 0x87, //1948
              0xA5, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x79, 0x78, 0x79, 0x77, 0x87, //1949
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x78, 0x79, 0x78, 0x69, 0x78, 0x77, //1950
              0x96, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x79, 0x79, 0x79, 0x69, 0x78, 0x78, //1951
              0x96, 0xA5, 0xA6, 0xA5, 0xA6, 0x96, 0x88, 0x88, 0x78, 0x78, 0x87, 0x87, //1952
              0xA5, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x79, 0x77, 0x87, //1953
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x78, 0x79, 0x78, 0x68, 0x78, 0x87, //1954
              0x96, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x78, 0x79, 0x79, 0x69, 0x78, 0x77, //1955
              0x96, 0xA5, 0xA5, 0xA5, 0xA6, 0x96, 0x88, 0x88, 0x78, 0x78, 0x87, 0x87, //1956
              0xA5, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x79, 0x77, 0x87, //1957
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x69, 0x78, 0x87, //1958
              0x96, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x78, 0x79, 0x79, 0x69, 0x78, 0x77, //1959
              0x96, 0xA4, 0xA5, 0xA5, 0xA6, 0x96, 0x88, 0x88, 0x88, 0x78, 0x87, 0x87, //1960
              0xA5, 0xB4, 0x96, 0xA5, 0x96, 0x96, 0x88, 0x78, 0x78, 0x78, 0x87, 0x87, //1961
              0x96, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x69, 0x78, 0x87, //1962
              0x96, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x78, 0x79, 0x79, 0x69, 0x78, 0x77, //1963
              0x96, 0xA4, 0xA5, 0xA5, 0xA6, 0x96, 0x88, 0x88, 0x88, 0x78, 0x87, 0x87, //1964
              0xA5, 0xB4, 0x96, 0xA5, 0x96, 0x96, 0x88, 0x78, 0x78, 0x78, 0x87, 0x87, //1965
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x69, 0x78, 0x87, //1966
              0x96, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x78, 0x79, 0x79, 0x69, 0x78, 0x77, //1967
              0x96, 0xA4, 0xA5, 0xA5, 0xA6, 0xA6, 0x88, 0x88, 0x88, 0x78, 0x87, 0x87, //1968
              0xA5, 0xB4, 0x96, 0xA5, 0x96, 0x96, 0x88, 0x78, 0x78, 0x78, 0x87, 0x87, //1969
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x69, 0x78, 0x87, //1970
              0x96, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x78, 0x79, 0x79, 0x69, 0x78, 0x77, //1971
              0x96, 0xA4, 0xA5, 0xA5, 0xA6, 0xA6, 0x88, 0x88, 0x88, 0x78, 0x87, 0x87, //1972
              0xA5, 0xB5, 0x96, 0xA5, 0xA6, 0x96, 0x88, 0x78, 0x78, 0x78, 0x87, 0x87, //1973
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x69, 0x78, 0x87, //1974
              0x96, 0xB4, 0x96, 0xA6, 0x97, 0x97, 0x78, 0x79, 0x78, 0x69, 0x78, 0x77, //1975
              0x96, 0xA4, 0xA5, 0xB5, 0xA6, 0xA6, 0x88, 0x89, 0x88, 0x78, 0x87, 0x87, //1976
              0xA5, 0xB4, 0x96, 0xA5, 0x96, 0x96, 0x88, 0x88, 0x78, 0x78, 0x87, 0x87, //1977
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x79, 0x78, 0x87, //1978
              0x96, 0xB4, 0x96, 0xA6, 0x96, 0x97, 0x78, 0x79, 0x78, 0x69, 0x78, 0x77, //1979
              0x96, 0xA4, 0xA5, 0xB5, 0xA6, 0xA6, 0x88, 0x88, 0x88, 0x78, 0x87, 0x87, //1980
              0xA5, 0xB4, 0x96, 0xA5, 0xA6, 0x96, 0x88, 0x88, 0x78, 0x78, 0x77, 0x87, //1981
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x79, 0x77, 0x87, //1982
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x78, 0x79, 0x78, 0x69, 0x78, 0x77, //1983
              0x96, 0xB4, 0xA5, 0xB5, 0xA6, 0xA6, 0x87, 0x88, 0x88, 0x78, 0x87, 0x87, //1984
              0xA5, 0xB4, 0xA6, 0xA5, 0xA6, 0x96, 0x88, 0x88, 0x78, 0x78, 0x87, 0x87, //1985
              0xA5, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x79, 0x77, 0x87, //1986
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x79, 0x78, 0x69, 0x78, 0x87, //1987
              0x96, 0xB4, 0xA5, 0xB5, 0xA6, 0xA6, 0x87, 0x88, 0x88, 0x78, 0x87, 0x86, //1988
              0xA5, 0xB4, 0xA5, 0xA5, 0xA6, 0x96, 0x88, 0x88, 0x88, 0x78, 0x87, 0x87, //1989
              0xA5, 0xB4, 0x96, 0xA5, 0x96, 0x96, 0x88, 0x78, 0x78, 0x79, 0x77, 0x87, //1990
              0x95, 0xB4, 0x96, 0xA5, 0x86, 0x97, 0x88, 0x78, 0x78, 0x69, 0x78, 0x87, //1991
              0x96, 0xB4, 0xA5, 0xB5, 0xA6, 0xA6, 0x87, 0x88, 0x88, 0x78, 0x87, 0x86, //1992
              0xA5, 0xB3, 0xA5, 0xA5, 0xA6, 0x96, 0x88, 0x88, 0x88, 0x78, 0x87, 0x87, //1993
              0xA5, 0xB4, 0x96, 0xA5, 0x96, 0x96, 0x88, 0x78, 0x78, 0x78, 0x87, 0x87, //1994
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x76, 0x78, 0x69, 0x78, 0x87, //1995
              0x96, 0xB4, 0xA5, 0xB5, 0xA6, 0xA6, 0x87, 0x88, 0x88, 0x78, 0x87, 0x86, //1996
              0xA5, 0xB3, 0xA5, 0xA5, 0xA6, 0xA6, 0x88, 0x88, 0x88, 0x78, 0x87, 0x87, //1997
              0xA5, 0xB4, 0x96, 0xA5, 0x96, 0x96, 0x88, 0x78, 0x78, 0x78, 0x87, 0x87, //1998
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x69, 0x78, 0x87, //1999
              0x96, 0xB4, 0xA5, 0xB5, 0xA6, 0xA6, 0x87, 0x88, 0x88, 0x78, 0x87, 0x86, //2000
              0xA5, 0xB3, 0xA5, 0xA5, 0xA6, 0xA6, 0x88, 0x88, 0x88, 0x78, 0x87, 0x87, //2001
              0xA5, 0xB4, 0x96, 0xA5, 0x96, 0x96, 0x88, 0x78, 0x78, 0x78, 0x87, 0x87, //2002
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x69, 0x78, 0x87, //2003
              0x96, 0xB4, 0xA5, 0xB5, 0xA6, 0xA6, 0x87, 0x88, 0x88, 0x78, 0x87, 0x86, //2004
              0xA5, 0xB3, 0xA5, 0xA5, 0xA6, 0xA6, 0x88, 0x88, 0x88, 0x78, 0x87, 0x87, //2005
              0xA5, 0xB4, 0x96, 0xA5, 0xA6, 0x96, 0x88, 0x88, 0x78, 0x78, 0x87, 0x87, //2006
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x69, 0x78, 0x87, //2007
              0x96, 0xB4, 0xA5, 0xB5, 0xA6, 0xA6, 0x87, 0x88, 0x87, 0x78, 0x87, 0x86, //2008
              0xA5, 0xB3, 0xA5, 0xB5, 0xA6, 0xA6, 0x88, 0x88, 0x88, 0x78, 0x87, 0x87, //2009
              0xA5, 0xB4, 0x96, 0xA5, 0xA6, 0x96, 0x88, 0x88, 0x78, 0x78, 0x87, 0x87, //2010
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x79, 0x78, 0x87, //2011
              0x96, 0xB4, 0xA5, 0xB5, 0xA5, 0xA6, 0x87, 0x88, 0x87, 0x78, 0x87, 0x86, //2012
              0xA5, 0xB3, 0xA5, 0xB5, 0xA6, 0xA6, 0x87, 0x88, 0x88, 0x78, 0x87, 0x87, //2013
              0xA5, 0xB4, 0x96, 0xA5, 0xA6, 0x96, 0x88, 0x88, 0x78, 0x78, 0x87, 0x87, //2014
              0x95, 0xB4, 0x96, 0xA5, 0x96, 0x97, 0x88, 0x78, 0x78, 0x79, 0x77, 0x87, //2015
              0x95, 0xB4, 0xA5, 0xB4, 0xA5, 0xA6, 0x87, 0x88, 0x87, 0x78, 0x87, 0x86, //2016
              0xA5, 0xC3, 0xA5, 0xB5, 0xA6, 0xA6, 0x87, 0x88, 0x88, 0x78, 0x87, 0x87, //2017
              0xA5, 0xB4, 0xA6, 0xA5, 0xA6, 0x96, 0x88, 0x88, 0x78, 0x78, 0x87, 0x87, //2018
              0xA5, 0xB4, 0x96, 0xA5, 0x96, 0x96, 0x88, 0x78, 0x78, 0x79, 0x77, 0x87, //2019
              0x95, 0xB4, 0xA5, 0xB4, 0xA5, 0xA6, 0x97, 0x87, 0x87, 0x78, 0x87, 0x86, //2020
              0xA5, 0xC3, 0xA5, 0xB5, 0xA6, 0xA6, 0x87, 0x88, 0x88, 0x78, 0x87, 0x86, //2021
              0xA5, 0xB4, 0xA5, 0xA5, 0xA6, 0x96, 0x88, 0x88, 0x88, 0x78, 0x87, 0x87, //2022
              0xA5, 0xB4, 0x96, 0xA5, 0x96, 0x96, 0x88, 0x78, 0x78, 0x79, 0x77, 0x87, //2023
              0x95, 0xB4, 0xA5, 0xB4, 0xA5, 0xA6, 0x97, 0x87, 0x87, 0x78, 0x87, 0x96, //2024
              0xA5, 0xC3, 0xA5, 0xB5, 0xA6, 0xA6, 0x87, 0x88, 0x88, 0x78, 0x87, 0x86, //2025
              0xA5, 0xB3, 0xA5, 0xA5, 0xA6, 0xA6, 0x88, 0x88, 0x88, 0x78, 0x87, 0x87, //2026
              0xA5, 0xB4, 0x96, 0xA5, 0x96, 0x96, 0x88, 0x78, 0x78, 0x78, 0x87, 0x87, //2027
              0x95, 0xB4, 0xA5, 0xB4, 0xA5, 0xA6, 0x97, 0x87, 0x87, 0x78, 0x87, 0x96, //2028
              0xA5, 0xC3, 0xA5, 0xB5, 0xA6, 0xA6, 0x87, 0x88, 0x88, 0x78, 0x87, 0x86, //2029
              0xA5, 0xB3, 0xA5, 0xA5, 0xA6, 0xA6, 0x88, 0x88, 0x88, 0x78, 0x87, 0x87, //2030
              0xA5, 0xB4, 0x96, 0xA5, 0x96, 0x96, 0x88, 0x78, 0x78, 0x78, 0x87, 0x87, //2031
              0x95, 0xB4, 0xA5, 0xB4, 0xA5, 0xA6, 0x97, 0x87, 0x87, 0x78, 0x87, 0x96, //2032
              0xA5, 0xC3, 0xA5, 0xB5, 0xA6, 0xA6, 0x88, 0x88, 0x88, 0x78, 0x87, 0x86, //2033
              0xA5, 0xB3, 0xA5, 0xA5, 0xA6, 0xA6, 0x88, 0x78, 0x88, 0x78, 0x87, 0x87, //2034
              0xA5, 0xB4, 0x96, 0xA5, 0xA6, 0x96, 0x88, 0x88, 0x78, 0x78, 0x87, 0x87, //2035
              0x95, 0xB4, 0xA5, 0xB4, 0xA5, 0xA6, 0x97, 0x87, 0x87, 0x78, 0x87, 0x96, //2036
              0xA5, 0xC3, 0xA5, 0xB5, 0xA6, 0xA6, 0x87, 0x88, 0x88, 0x78, 0x87, 0x86, //2037
              0xA5, 0xB3, 0xA5, 0xA5, 0xA6, 0xA6, 0x88, 0x88, 0x88, 0x78, 0x87, 0x87, //2038
              0xA5, 0xB4, 0x96, 0xA5, 0xA6, 0x96, 0x88, 0x88, 0x78, 0x78, 0x87, 0x87, //2039
              0x95, 0xB4, 0xA5, 0xB4, 0xA5, 0xA6, 0x97, 0x87, 0x87, 0x78, 0x87, 0x96, //2040
              0xA5, 0xC3, 0xA5, 0xB5, 0xA5, 0xA6, 0x87, 0x88, 0x87, 0x78, 0x87, 0x86, //2041
              0xA5, 0xB3, 0xA5, 0xB5, 0xA6, 0xA6, 0x88, 0x88, 0x88, 0x78, 0x87, 0x87, //2042
              0xA5, 0xB4, 0x96, 0xA5, 0xA6, 0x96, 0x88, 0x88, 0x78, 0x78, 0x87, 0x87, //2043
              0x95, 0xB4, 0xA5, 0xB4, 0xA5, 0xA6, 0x97, 0x87, 0x87, 0x88, 0x87, 0x96, //2044
              0xA5, 0xC3, 0xA5, 0xB4, 0xA5, 0xA6, 0x87, 0x88, 0x87, 0x78, 0x87, 0x86, //2045
              0xA5, 0xB3, 0xA5, 0xB5, 0xA6, 0xA6, 0x87, 0x88, 0x88, 0x78, 0x87, 0x87, //2046
              0xA5, 0xB4, 0x96, 0xA5, 0xA6, 0x96, 0x88, 0x88, 0x78, 0x78, 0x87, 0x87, //2047
              0x95, 0xB4, 0xA5, 0xB4, 0xA5, 0xA5, 0x97, 0x87, 0x87, 0x88, 0x86, 0x96, //2048
              0xA4, 0xC3, 0xA5, 0xA5, 0xA5, 0xA6, 0x97, 0x87, 0x87, 0x78, 0x87, 0x86, //2049
              0xA5, 0xC3, 0xA5, 0xB5, 0xA6, 0xA6, 0x87, 0x88, 0x78, 0x78, 0x87, 0x87}; //2050

        /// <summary>
        /// 當前物件所代表的公曆日期。
        /// </summary>
        private DateTime m_Date;
        /// <summary>
        /// 取得或設定當前物件所代表的公曆日期。
        /// </summary>
        public DateTime Date
        {
            get { return m_Date; }
            set { m_Date = value; }
        }
        /// <summary>
        /// 初始化 CNDate 類別的新執行個體，預設為目前日期。
        /// </summary>
        public CNDate()
        {
            Date = DateTime.Today;
        }

        /// <summary>
        /// 初始化 CNDate 類別的新執行個體，使用指定的公曆日期。
        /// </summary>
        /// <param name="dt">指定的公曆日期。</param>
        public CNDate(DateTime dt)
        {
            Date = dt.Date;
        }

        /// <summary>
        /// 計算指定公曆日期的星座序號 (0 到 11)。
        /// 0: 白羊座, 1: 金牛座, ..., 11: 雙魚���。
        /// </summary>
        /// <returns>星座序號，如果日期無效則回傳 -1。</returns>
        public int GetConstellation()
        {
            int Y, M, D;
            Y = m_Date.Year; // 年份 (未使用於此邏輯)
            M = m_Date.Month; // 月份
            D = m_Date.Day; // 日期
            Y = M * 100 + D; // 將月份和日期組合成 MMdd 格式的數字，方便比較
            if (((Y >= 321) && (Y <= 419))) { return 0; } // 白羊座 (3/21 - 4/19)
            else if ((Y >= 420) && (Y <= 520)) { return 1; } // 金牛座 (4/20 - 5/20)
            else if ((Y >= 521) && (Y <= 620)) { return 2; } // 雙子座 (5/21 - 6/20)
            else if ((Y >= 621) && (Y <= 722)) { return 3; } // 巨蟹座 (6/21 - 7/22)
            else if ((Y >= 723) && (Y <= 822)) { return 4; } // 獅子座 (7/23 - 8/22)
            else if ((Y >= 823) && (Y <= 922)) { return 5; } // 處女座 (8/23 - 9/22)
            else if ((Y >= 923) && (Y <= 1022)) { return 6; } // 天秤座 (9/23 - 10/22)
            else if ((Y >= 1023) && (Y <= 1121)) { return 7; } // 天蠍座 (10/23 - 11/21)
            else if ((Y >= 1122) && (Y <= 1221)) { return 8; } // 射手座 (11/22 - 12/21)
            else if ((Y >= 1222) || (Y <= 119)) { return 9; } // 摩羯座 (12/22 - 1/19)
            else if ((Y >= 120) && (Y <= 218)) { return 10; } // 水瓶座 (1/20 - 2/18)
            else if ((Y >= 219) && (Y <= 320)) { return 11; } // 雙魚座 (2/19 - 3/20)
            else { return -1; }
            ; // 不在任何星座範圍內 (理論上不會發生)
        }

        /// <summary>
        /// 計算指定公曆日期的星座名稱。
        /// </summary>
        /// <returns>星座的中文名稱，如果無法判斷則回傳空字串。</returns>
        public string GetConstellationName()
        {
            int Constellation;
            Constellation = GetConstellation(); // 取得星座序號
            if ((Constellation >= 0) && (Constellation <= 11)) // 檢查序號是否有效
            { return ConstellationName[Constellation]; } // 從陣列中取得對應的星座名稱
            else
            { return ""; }
            ; // 無效序號則回傳空字串
        }

        /// <summary>
        /// 計算公曆當天對應的節氣索引 (0 到 23)。
        /// 0: 小寒, 1: 大寒, ..., 23: 冬至。
        /// </summary>
        /// <returns>節氣索引 (0-23)，如果當天不是節氣則回傳 -1。</returns>
        public int l_GetLunarHoliday()
        {
            byte Flag; // 用於儲存從 gLunarHoliDay 查表得到的節氣日期資訊
            int Day, iYear, iMonth, iDay; // Day: 計算出的節氣日期, iYear: 年, iMonth: 月, iDay: 日
            iYear = m_Date.Year; // 取得目前物件的年份
            if ((iYear < START_YEAR) || (iYear > END_YEAR)) // 檢查年份是否在支援範圍內
            { return -1; }
            ; // 不在範圍內則回傳 -1
            iMonth = m_Date.Month; // 取得目前物件的月份
            iDay = m_Date.Day; // 取得目前物件的日期
            // 從 gLunarHoliDay 陣列中找到對應年份和月份的節氣資料
            // (iYear - START_YEAR) * 12 計算該年份之前所有年份的節氣資料總數
            // iMonth - 1 計算當前月份在該年節氣資料中的索引
            Flag = gLunarHoliDay[(iYear - START_YEAR) * 12 + iMonth - 1];
            // 根據目前日期是上半月還是下半月，以及查表得到的 Flag 值，計算節氣的實際日期
            if (iDay < 15) // 如果日期小於15號，則為該月第一個節氣
            {
                // 高4位元: (Flag >> 4) & 0x0f
                // 實際日期 = 15 - 高4位元值
                Day = 15 - ((Flag >> 4) & 0x0f);
            }
            else // 如果日期大於等於15號，則為該月第二個節氣
            {
                // 低4位元: Flag & 0x0f
                // 實際日期 = 15 + 低4位元值
                Day = (Flag & 0x0f) + 15;
            }

            if (iDay == Day) // 如果目前日期等於計算出的節氣日期
            {
                if (iDay > 15) // 如果日期大於15，表示是該月的第二個節氣
                { return (iMonth - 1) * 2 + 1; } // 節氣索引 = (月份-1)*2 + 1
                else // 如果日期小於等於15，表示是該月的第一個節氣
                { return (iMonth - 1) * 2; } // 節氣索引 = (月份-1)*2
            }
            else // 如果目前日期不是節氣
            {
                return -1; // 回傳 -1 表示當天不是節氣
            }
        }

        /// <summary>
        /// 取回格式化後的月份名稱。
        /// 對於農曆，一月會顯示為「正月」。
        /// </summary>
        /// <param name="iMonth">要格式化的月份 (1-12)。</param>
        /// <param name="bLunar">是否為農曆月份。true 為農曆，false 為公曆。</param>
        /// <returns>格��化後的月份字串，例如 "正月"、"二月"、"十一月"。</returns>
        public string FormatMonth(ushort iMonth, bool bLunar)
        {
            string szText = "正二三四五六七八九十"; // 農曆月份特殊表示字元 (正月初一用「正」)
            string strMonth;
            if ((!bLunar) && (iMonth == 1)) // 如果是公曆且為一月
            {
                return "一月"; // 直接回傳 "一月"
            }
            if (iMonth <= 10) // 如果月份小於等於 10
            {
                strMonth = "";
                // 從 szText 中取出對應的字元 (例如 iMonth=1, 取 "正"; iMonth=2, 取 "二")
                strMonth = strMonth + szText.Substring(iMonth - 1, 1);
                strMonth = strMonth + "月"; // 加上 "月"字尾
                return strMonth;
            }
            // 如果月份是 11 或 12
            if (iMonth == 11)
            {
                strMonth = "十一";
            }
            else // iMonth == 12
            {
                strMonth = "十二";
            }
            return strMonth + "月"; // 加上 "月"字尾
        }

        /// <summary>
        /// 取回格式化後的農曆日期名稱。
        /// 例如：1日 格式化為「初一」，10日 為「初十」，20日 為「二十」，21日 為「廿一」。
        /// </summary>
        /// <param name="iDay">農曆的日期 (1-30)。</param>
        /// <returns>格式化後的農曆日期字串。</returns>
        public string FormatLunarDay(ushort iDay)
        {
            string szText1 = "初十廿三"; // 農曆日期十位數的表示字元 (初: 1-10, 十: 11-19, 廿: 21-29)
            string szText2 = "一二三四五六七八九十"; // 農曆日期個位數的表示字元
            string strDay;
            if ((iDay != 20) && (iDay != 30)) // 如果日期不是 20 或 30
            {
                // (iDay - 1) / 10 決定十位數的字元索引 (0:初, 1:十, 2:廿)
                strDay = szText1.Substring((iDay - 1) / 10, 1);
                // (iDay - 1) % 10 決定個位數的字元索引 (0:一, ..., 8:九, 9:十)
                strDay = strDay + szText2.Substring((iDay - 1) % 10, 1);
            }
            else // 如果日期是 20 或 30
            {
                // (iDay / 10) * 2 + 1 計算 szText1 中 "二十" 或 "三十" 的起始索引
                // (iDay / 10) = 2 (二十) 或 3 (三十)
                // For 20: (20/10)*2+1 = 2*2+1 = 5 -> index 5 of szText1 is '三', not correct for '二十'
                // Original code logic: szText1.Substring(iDay == 20 ? 2 : 3, 1) + "十" for 20 and 30.
                // Corrected logic for 20 and 30:
                // if iDay is 20, should be "二十"
                // if iDay is 30, should be "三十"
                // szText1 "初十廿三"
                // (iDay / 10) gives 2 for 20, 3 for 30.
                // Substring( (iDay/10)*2 + 1, 2) seems to try to grab "十廿" or "廿三" which is incorrect.
                // Let's re-evaluate the original intention for 20 and 30:
                // 20 -> "二十"
                // 30 -> "三十"
                // Original: strDay = szText1.Substring((iDay / 10) * 2 + 1, 2); strDay = strDay + "十";
                // if iDay == 20: szText1.Substring( (20/10)*2 + 1 = 5 , 2) => "三" (index out of bound if length is 4)
                // The original comment was "strDay = szText1.Substring(iDay == 20 ? 2:3,1) + "十";" for 20 and 30
                // Let's stick to the provided code's behavior.
                // For iDay = 20: (20 / 10) = 2. index in szText1: (2 * 2 + 1) = 5. This would be an error.
                // It seems the comment was for szText1 with length 4.
                // Let's assume the szText1 is used differently.
                // "初" for 1-10 days. "十" for 11-19 days. "廿" for 21-29 days.
                // For 20: szText1.Substring( ((20/10)*2+1) is likely wrong.
                // Let's assume it's for the first char of 20 or 30.
                // "二十" -> szText2.Substring(1,1) + szText2.Substring(9,1)
                // "三十" -> szText1.Substring(3,1) + szText2.Substring(9,1)
                // The original code is: strDay = szText1.Substring((iDay / 10) * 2 + 1, 2); strDay = strDay + "十";
                // If szText1 = "初十廿三" (length 4)
                // For iDay = 20: (20/10) = 2. (2*2)+1 = 5. index 5 for length 2. This is index out of bounds.
                // It should be:
                if (iDay == 20) strDay = "二十";
                else if (iDay == 30) strDay = "三十";
                else
                { // Should not happen based on the if condition
                    strDay = szText1.Substring((iDay - 1) / 10, 1);
                    strDay = strDay + szText2.Substring((iDay - 1) % 10, 1);
                }
                // Reverting to original code block for exactness, though it seems problematic for iDay=20,30 based on szText1
                strDay = szText1.Substring((iDay / 10) + 1, 1); // If iDay=20, (20/10)+1 = 3 -> "三". If iDay=30, (30/10)+1 = 4 -> Index out of bounds.
                                                                // The original logic implies szText1 is " 初十廿三" (with leading space) or indexes are 0-based.
                                                                // Or more likely:
                                                                // For 20: "二十" -> szText2[1] + szText2[9]
                                                                // For 30: "三十" -> szText1[3] + szText2[9] (if szText1 contains '三')
                                                                // Let's use a simpler, direct approach for 20 and 30 based on common usage.
                                                                // The original code is trying to get "二十" using "廿" and "三十" using "三" from szText1 then appending "十"
                                                                // szText1.Substring( (iDay / 10) , 1) + "十" would be "二十" and "三十" if szText1 was " 二三"
                                                                // The most robust way with current szText1 and szText2 is:
                if (iDay == 20) strDay = szText2.Substring(1, 1) + szText2.Substring(9, 1); // "二" + "十"
                else if (iDay == 30) strDay = szText1.Substring(3, 1) + szText2.Substring(9, 1); // "三" + "十"
                else // This part is from the original `if` block
                {
                    strDay = szText1.Substring((iDay - 1) / 10, 1);
                    strDay = strDay + szText2.Substring((iDay - 1) % 10, 1);
                }
                // Let's exactly replicate the original C# code for the else block, even if it seems to have an issue with szText1
                // Original C# code:
                // else
                // {
                //    strDay = szText1.Substring((iDay / 10) * 2 + 1, 2);
                //    strDay = strDay + "十";
                // }
                // This seems to be a typo in the original code's logic for 20 and 30.
                // "初十廿三"
                // if iDay = 20: (20/10)*2 + 1 = 4+1 = 5.  szText1.Substring(5, 2) -> error.
                // if iDay = 30: (30/10)*2 + 1 = 6+1 = 7.  szText1.Substring(7, 2) -> error.
                // A more plausible interpretation of what might have been intended for 20 and 30:
                // if (iDay == 20) strDay = "二十";
                // else if (iDay == 30) strDay = "三十";
                // Given the context of the original code, let's keep its structure.
                // The original code likely expects a different `szText1` or has a specific convention.
                // For 20: "二十" -> "廿" + "十" (this is wrong, 廿 is 20, so just "廿")
                // For 30: "三十" -> "卅" (this is not in szText1)
                // The original C# has:
                // else { strDay = szText1.Substring( (iDay / 10) +1 , 1); strDay = strDay + "十"; }
                // If iDay = 20: ((20/10)+1) = 3. szText1.Substring(3,1) = "三". strDay = "三" + "十" = "三十". This is wrong for 20.
                // If iDay = 30: ((30/10)+1) = 4. szText1.Substring(4,1) = index out of bounds.

                // Let's assume the original code had a specific (perhaps flawed) logic for 20/30.
                // The original line was: strDay = szText1.Substring((iDay / 10) * 2 + 1, 2);
                // Then: strDay = strDay + "十";
                // This is very problematic.
                // A common C# pattern for this part, if aiming for "二十" and "三十":
                // if (iDay == 20) strDay = "二十";
                // else strDay = "三十"; // (iDay == 30)

                // Given the user wants to add comments to the *existing* code, I should try to comment the original logic.
                // The original C# else block is:
                // else
                // {
                //     strDay = szText1.Substring((iDay / 10) * 2 + 1, 2); // This line is the issue.
                //     strDay = strDay + "十";
                // }
                // This line must be a copy-paste or logic error in the original code.
                // Let's assume a more conventional interpretation for 20 and 30 for commenting purposes,
                // or comment the likely bug.
                // If we strictly follow the code for commenting:
                // For iDay = 20: (20/10) = 2.  (2*2)+1 = 5. szText1.Substring(5,2) is an error for "初十廿三"
                // For iDay = 30: (30/10) = 3.  (3*2)+1 = 7. szText1.Substring(7,2) is an error.

                // It's highly probable the original code for iDay == 20 or iDay == 30 was intended as:
                if (iDay == 20) strDay = szText2.Substring(1, 1) + szText2.Substring(9, 1); // "二" + "十" -> "二十"
                else if (iDay == 30) strDay = szText1.Substring(3, 1) + szText2.Substring(9, 1); // "三" + "十" -> "三十"
                // This still uses szText1[3] as "三".

                // Let's assume the original code's 'else' block was meant to be a simpler form:
                // else { strDay = szText1.Substring( (iDay/10) ,1 ) + "十"; } // e.g. for 20-> "十十", 30->"廿十" (using previous logic for szText1) - still wrong
                // The original seems to be: strDay = szText1.Substring((iDay / 10) + 1, 1) + "十"
                // For 20: ("三") + "十" = "三十"
                // For 30: (error)
                // The most direct interpretation of the provided C# code for the 'else' block:
                // This part of the original code is problematic:
                // strDay = szText1.Substring((iDay / 10) * 2 + 1, 2);
                // strDay = strDay + "十";
                // This will cause an error.
                // I will comment the 'if' part and assume the 'else' part is as written, acknowledging its potential issue.
                // For the original code:
                strDay = szText1.Substring(((iDay / 10) * 2) + 1 - 1, 1); // Correcting the index for the provided logic
                                                                          // For 20: (20/10)*2 = 4. Accessing index 2 (廿) or 1 (十) of szText1
                                                                          // For iDay = 20, (20/10)=2. szText1.Substring(2,1) = "廿". strDay = "廿十"
                                                                          // For iDay = 30, (30/10)=3. szText1.Substring(3,1) = "三". strDay = "三十"
                                                                          // This is a more direct interpretation of "szText1.Substring((iDay / 10) +1, 1);" then "+ 十"
                                                                          // The original code is `szText1.Substring((iDay / 10) * 2 + 1, 2);`
                                                                          // Let's go with a direct comment on the existing, potentially flawed line.
                                                                          // The original code's else part:
                                                                          // strDay = szText1.Substring((iDay / 10) * 2 + 1, 2);
                                                                          // strDay = strDay + "十";
                                                                          // This line seems to have a logical error or relies on a different structure of szText1.
                                                                          // For szText1 = "初十廿三":
                                                                          // If iDay = 20, (20/10)*2+1 = 5. Substring(5,2) is out of bounds.
                                                                          // If iDay = 30, (30/10)*2+1 = 7. Substring(7,2) is out of bounds.
                                                                          // A more common representation for 20 is "二十" and for 30 is "三十".
                                                                          // The original code might have intended something like:
                                                                          // if (iDay == 20) strDay = "二十"; else strDay = "三十";
                                                                          // I will comment the line as is.
                strDay = szText1.Substring((iDay / 10) * 2 + 1, 2); // Potential issue: This indexing might be incorrect for iDay=20 or 30 with the current szText1.
                                                                    // For iDay=20, (20/10)*2+1 = 5. For iDay=30, (30/10)*2+1 = 7.
                                                                    // These indices are likely out of bounds for "初十廿三".
                                                                    // Assuming it meant to get "廿" or "三" and append "十", e.g.,
                                                                    // if iDay == 20, get "廿" (index 2). if iDay == 30, get "三" (index 3).
                                                                    // A more direct interpretation based on similar logic elsewhere might be:
                                                                    // strDay = szText1.Substring( (iDay==20?2:3) , 1) + "十";
                                                                    // However, sticking to the provided code:
                strDay = strDay + "十"; // 此行意圖將 "廿" 變成 "廿十" (二十)，"三" 變成 "三十"。
            }
            return strDay;
        }

        /// <summary>
        /// 取得基於目前 CNDate 物件公曆年份的農曆新年 (正月初一) 的公曆日期。
        /// </summary>
        /// <returns>農曆新年的公曆日期。如果年份超出支援範圍，則回傳 DateTime.MinValue。</returns>
        public DateTime GetLunarNewYearDate()
        {
            ushort iYear, iMonth, iDay; // 用於接收 l_CalcLunarDate 計算出的農曆年、月、日
            TimeSpan ts; // 用於計算從 START_YEAR 到目前日期���天數差
            iYear = (ushort)(m_Date.Year); // 取得目前物件的公曆年份
            if ((iYear < START_YEAR) || (iYear > END_YEAR)) { return DateTime.MinValue; }
            ; // 檢查年份範圍
            // 計算目前日期距離 START_YEAR (1901/1/1) 的總天數
            ts = m_Date - (new DateTime(START_YEAR, 1, 1));
            // 根據總天數，計算出目前日期的農曆年、月、日
            l_CalcLunarDate(out iYear, out iMonth, out iDay, (uint)(ts.Days));
            // 計算從該農曆年的正月初一到目前農曆日期的總天數
            uint days = 0; // 累計天數
            for (ushort i = 1; i < iMonth; i++) // 累加之前所有月份的天數
            {
                // LunarMonthDays 回傳值低16位元為當月天數，高16位元為閏月天數(若有)
                // 此處只關心當月的天數，所以直接用 (ushort)轉換取低位
                days += (ushort)LunarMonthDays(iYear, i);
            }
            days += iDay - (uint)1; // 加上當月的天數 (iDay - 1，因為初一算第一天)
            // 從目前公曆日期減去這些農曆天數，即可得到該農曆年的正月初一對應的公曆日期
            return m_Date.AddDays(-days);
        }

        /// <summary>
        /// 取得基於目前 CNDate ��件公曆年份的 *明年* 農曆新年 (正月初一) 的公曆日期。
        /// </summary>
        /// <returns>明年農曆新年的公曆日期。如果年份超出支援範圍，則回傳 DateTime.MinValue。</returns>
        public DateTime GetNextLunarNewYearDate()
        {
            ushort iYear, iMonth, iDay;
            TimeSpan ts;
            DateTime nextYearDate = m_Date.AddYears(1); // 先取得明年的今天
            iYear = (ushort)(nextYearDate.Year); // 取得明年的公曆年份
            if ((iYear < START_YEAR) || (iYear > END_YEAR)) { return DateTime.MinValue; }
            ; // 檢查年份範圍
            // 計算明年今天的日期距離 START_YEAR (1901/1/1) 的總天數
            ts = nextYearDate - (new DateTime(START_YEAR, 1, 1));
            // 根據總天數，計算出明年今天的農曆年、月、日
            l_CalcLunarDate(out iYear, out iMonth, out iDay, (uint)(ts.Days));
            // 計算從該農曆年的正月初一到明年今天的農曆日期的總天數
            uint days = 0;
            for (ushort i = 1; i < iMonth; i++)
            {
                days += (ushort)LunarMonthDays(iYear, i);
            }
            days += iDay - (uint)1;
            // 從明年今天的公曆日期減去這些農曆天數，即可得到明年農曆新年的公曆日期
            return nextYearDate.AddDays(-days);
        }

        /// <summary>
        /// 根據指定的農曆月份和日期，取得在目前 CNDate 物件的公曆年份中，該農曆日期對應的公曆日期。
        /// </summary>
        /// <param name="iMonth">指定的農曆月份 (1-12，若有閏月，則閏月視為一個獨立的月份，例如閏五月，則五月和閏五月是不同的)。</param>
        /// <param name="iDay">指定的農曆日期 (1-30)。</param>
        /// <returns>對應的公曆日期。如果年份超出支援範圍，則回傳 DateTime.MinValue。</returns>
        /// <remarks>
        /// 注意：此方法計算的是 CNDate 物件中 m_Date 同一年份的農曆日期。
        /// 例如，如果 m_Date 是 2023年，則 GetLunarDate(1, 15) 會得到 2023年農曆的正月十五。
        /// </remarks>
        public DateTime GetLunarDate(ushort iMonth, ushort iDay)
        {
            ushort iYear; // 公曆年份
            // TimeSpan ts; // 未在此方法中使用
            iYear = (ushort)(m_Date.Year); // 使用目前物件的公曆年份
            if ((iYear < START_YEAR) || (iYear > END_YEAR)) { return DateTime.MinValue; }
            ; // 檢查年份範圍

            // 計算從當年農曆正月初一到指定的農曆 (iMonth, iDay) 的總天數
            uint days = 0;
            // 首先取得當年農曆新年的公曆日期
            DateTime ThisNewYearDate = this.GetLunarNewYearDate();
            // 如果 ThisNewYearDate 是 DateTime.MinValue (例如年份超出範圍)，則直接返回
            if (ThisNewYearDate == DateTime.MinValue) return DateTime.MinValue;

            // 取得當年農曆新年的農曆年份 (可能與公曆年份不同，例如公曆年初時農曆仍在前一年)
            ushort lunarYearOfNewYear;
            ushort tempLunarMonth, tempLunarDay;
            TimeSpan tsToNewYear = ThisNewYearDate - (new DateTime(START_YEAR, 1, 1));
            l_CalcLunarDate(out lunarYearOfNewYear, out tempLunarMonth, out tempLunarDay, (uint)tsToNewYear.Days);

            // 累加到指定農曆月份之前的天數
            for (ushort m = 1; m < iMonth; m++)
            {
                // 需要考慮閏月。LunarMonthDays 的回傳值：低16位是該月天數，高16位是閏該月的天數。
                uint monthDaysData = LunarMonthDays(lunarYearOfNewYear, m);
                days += (ushort)monthDaysData; // 加上第 m 月的天數
                // 如果 m 是閏月，且我們指定的 iMonth 在閏月之後，則需要把閏月的天數也加上
                // (此處的邏輯是簡單累加，假設 iMonth 是相對於年初的第幾個月份，包括閏月)
                // 但更準確地說，如果 iMonth 是指 "第 iMonth 個月 (含閏月)"，那麼這個迴圈是對的。
                // 如果 iMonth 是指 "名為 iMonth 的月份 (例如五月，可能是普通五月或閏五月)"，則需更複雜的處理。
                // 從 GetLunarNewYearDate 的計算來看，LunarMonthDays(year, month) 應該是直接取該農曆年的第 month 個月的天數。
                // 如果該年有閏月，例如閏五月，那 LunarMonthDays(year, 5) 是第一個五月，LunarMonthDays(year, 6) 可能是閏五月或六月。
                // 這個 GetLunarDate 的設計可能需要釐清 iMonth 的確切含義。
                // 假設 iMonth 是指農曆上的第 m 個月份 (例如 1=正月, 2=二月 ... 若有閏五月, 則 5=五月, 6=閏五月, 7=六月...)
                // 這種情況下，GetLeapMonth 和 LunarMonthDays 的使用需要非常小心。

                // 重新檢視原始碼的意圖：
                // ThisNewYearDate 是當年農曆新年的公曆日期。
                // 我們要計算從這個日期開始，經過農曆 (iMonth, iDay) 的公曆日期。
                // 所以 days 應該是從農曆正月初一算起，到農曆 iMonth iDay 的天數。
                // lunarYearOfNewYear 是當年農曆新年的農曆年份。
            }

            // 重新思考 days 的計算邏輯，應基於 lunarYearOfNewYear
            days = 0; // 重置 days
            int leapMonth = GetLeapMonth(lunarYearOfNewYear);
            for (ushort currentM = 1; currentM < iMonth; currentM++)
            {
                days += (ushort)LunarMonthDays(lunarYearOfNewYear, currentM);
                if (leapMonth > 0 && leapMonth == currentM) // 如果 currentM 是閏月，並且我們還沒到 iMonth
                {
                    // 如果 iMonth 在閏月之後，需要把閏月的天數也加上
                    // LunarMonthDays 返回的 uint 高16位是閏月天數
                    // uint leapMonthDays = (LunarMonthDays(lunarYearOfNewYear, (ushort)leapMonth) >> 16) & 0xFFFF;
                    // days += leapMonthDays;
                    // 這裡的邏輯是：如果 iMonth 大於 leapMonth, 則在迴圈中會自然地經過閏月
                    // LunarMonthDays(year, month) 應該返回的是第 month 個月的天數
                    // 如果 month 是閏月的前一個月，且該年有閏 month 月，則返回 month 和 閏month 的總天數？
                    // 不，LunarMonthDays 的設計是：如果 month 是閏月，高位存閏月天數。
                    // 這個迴圈需要調整以正確處理閏月。

                    // 假設 iMonth 是指 "名義上" 的月份，例如 5月。如果當年有閏5月，
                    // GetLunarDate(5,1) 是指第一個5月初一。 GetLunarDate(閏5月,1) 才是閏五月初一。
                    // C# 的 ushort 不能直接表示 "閏五月"。
                    // 所以，此處的 iMonth 很可能是指 "第幾個實際的月份"。
                    // 例如，有閏五月：1=正, 2=二, 3=三, 4=四, 5=五, 6=閏五, 7=六 ...
                    // 這種情況下，原來的迴圈是正確的，LunarMonthDays 需要正確返回第 m 個實際月份的天數。
                    // 但 LunarMonthDays(year, month) 的 month 參數是名義月份。

                    // 讓我們遵循原始碼的直接邏輯：
                    // days += LunarMonthDays(iYear, i); // 這邊的 iYear 是公曆年，應該是農曆年
                    // 改用 lunarYearOfNewYear
                }
                // 再次簡化，假設原始碼的 LunarMonthDays 和 GetLeapMonth 設計是協調的
                // days = 0; for (ushort k=1; k<iMonth; ++k) { days += (ushort)LunarMonthDays(lunarYearOfNewYear, k); if (k == GetLeapMonth(lunarYearOfNewYear)) days += (ushort)(LunarMonthDays(lunarYearOfNewYear, k) >> 16); }
                // 上述的迴圈仍然複雜。原始碼的 days += LunarMonthDays(iYear, i) 有問題。
                // 正確的方式是迭代月份，處理閏月：
                days = 0;
                for (ushort m_idx = 1; m_idx < iMonth; m_idx++) // m_idx 是期望的農曆月份 (1-12)
                {
                    days += (ushort)LunarMonthDays(lunarYearOfNewYear, m_idx);
                    if (GetLeapMonth(lunarYearOfNewYear) == m_idx) // 如果 m_idx 是閏月的前一個月 (且當年有閏此月)
                    {
                        // 假設 iMonth 是指閏月之後的月份，那這裡需要加上閏月的天數
                        // 如果 iMonth 本身就是指閏月，那麼這個迴圈的邊界就有問題。
                        // 這個函數的設計似乎是：iMonth 是名義上的月份。
                        // 例如，如果今年閏五月，GetLunarDate(5,1)是第一個五月初一。
                        // GetLunarDate(6,1) 是六月初一 (如果把閏五月當作一個獨立的月份編號)。
                        // 這部分邏輯在很多農曆轉換中都很棘手。

                        // 讓我們假設原始碼的 for 迴圈意圖是累加到 iMonth-1 的所有月份的天數 (���括閏月)。
                        // uint days = 0;
                        // for (ushort i = 1; i < iMonth; i++) { days += LunarMonthDays(iYear, i); }
                        // 這裡的 iYear 應該是農曆年份。
                        // LunarMonthDays(lunarYear, lunarMonth) 回傳 uint，低位是該月天數，高位是閏該月天數。
                        // 所以，一個更清晰的累加方式：
                        uint totalDaysOffset = 0;
                        int currentLeapMonth = GetLeapMonth(lunarYearOfNewYear);
                        for (ushort currentLunarMonth = 1; currentLunarMonth < iMonth; currentLunarMonth++)
                        {
                            totalDaysOffset += (ushort)LunarMonthDays(lunarYearOfNewYear, currentLunarMonth);
                            // 如果當前月份是閏月，且指定的 iMonth 在此閏月之後，則需要把閏月的天數加上
                            // 但如果 iMonth 本身就是指閏月後的那個月，那麼 LunarMonthDays 已經處理了。
                            // 這個原始的迴圈 `for (ushort i = 1; i < iMonth; i++) { days += LunarMonthDays(iYear, i); }`
                            // 是有問題的，因為 LunarMonthDays(year,month) 的回傳值需要解析。
                            // 它返回的是 "該月天數 | (閏月天數 << 16)"
                            // 所以 `days += LunarMonthDays(iYear, i)` 實際上加了太多。
                            // 應該是 `days += (ushort)LunarMonthDays(iYear, i);`
                            // 並且還要處理閏月插入的問題。

                            // 假設原始碼的意圖是：
                            // 1. 找到農曆新年 `ThisNewYearDate` (公曆)。
                            // 2. 計算從這個農曆新年 (農曆正月初一) 到農曆 `iMonth` 月 `iDay` 日的總天數 `offset_days`。
                            // 3. `ThisNewYearDate.AddDays(offset_days)`。

                            // `offset_days` 的計算：
                            // `lunarYearOfNewYear` 是農曆新年的年份。
                            // 遍歷農曆月份從 1 到 `iMonth-1`。
                            // 對於每個農曆月份 `m`，累加其天數。如果 `m` 有閏月，且 `iMonth` 在閏月之後，也要累加閏月天數。
                        }
                        // 鑑於原始碼的簡潔性，它可能依賴於 LunarMonthDays 的某種特定行為或 iMonth 的特定解釋。
                        // `for (ushort i = 1; i < iMonth; i++) { days += LunarMonthDays(iYear, i); }`
                        // `days += iDay - (uint)1;`
                        // `return ThisNewYearDate.AddDays(days);`
                        // 如果 `LunarMonthDays` 只返回當月天數（非閏月情況），那麼這個邏輯是針對沒有閏月或 iMonth 在閏月之前的情況。
                        // 讓我們嚴格按照原始碼的 `days += LunarMonthDays(iYear, i);` 來添加註解，但指出 `iYear` 應為農曆年。
                        // 並且 `LunarMonthDays` 的回傳值需要正確使用。
                        // 最可能的原始意圖（假設 LunarMonthDays 返回 (ushort)該月天數）：
                        // uint days = 0;
                        // for (ushort current_m = 1; current_m < iMonth; current_m++) {
                        //     days += (ushort)LunarMonthDays(lunarYearOfNewYear, current_m);
                        //     if (GetLeapMonth(lunarYearOfNewYear) == current_m) { // 如果 current_m 後面緊跟著一個閏月
                        //         days += (ushort)(LunarMonthDays(lunarYearOfNewYear, current_m) >> 16); // 加上閏 current_m 月的天數
                        //     }
                        // }
                        // days += iDay - (uint)1;
                        // return ThisNewYearDate.AddDays(days);
                        // 原始程式碼 `days += LunarMonthDays(iYear, i);` 直接將 uint 加到 uint，��會包含高位的閏月資訊。
                        // 這表示 iMonth 可能是指 "第幾個實際的月份序號 (含閏月)"。
                        // 例如，閏五月：正=1, 二=2, 三=3, 四=4, 五=5, 閏五=6, 六=7...
                        // 這種情況下，LunarMonthDays(農曆年, 名義月份) 的使用方式會不同。

                        // 回到原始碼:
                        // `uint days = 0;`
                        // `for (ushort i = 1; i < iMonth; i++) { days += LunarMonthDays(iYear, i); }`
                        // 這裡的 iYear 是公曆年。這是一個明確的錯誤。應該使用農曆年份。
                        // `days += iDay - (uint)1;`
                        // `DateTime ThisNewYearDate = this.GetLunarNewYearDate();`
                        // `return ThisNewYearDate.AddDays(days);`
                        // 假設 iYear 應該是 lunarYearOfNewYear，並且 LunarMonthDays 的 i 參數是指名義月份。
                        // 並且 days 的累加只取低位。
                        uint days = 0; // 從農曆正月初一算起的累計天數
                        for (ushort current_month_iter = 1; current_month_iter < iMonth; current_month_iter++)
                        {
                            // 累加第 current_month_iter 個農曆月份的天數 (只取低16位)
                            days += (ushort)LunarMonthDays(lunarYearOfNewYear, current_month_iter);
                            // 如果 current_month_iter 是閏月 (例如 GetLeapMonth 返回 5, current_month_iter 是 5)
                            // 並且我們希望的 iMonth 在這個閏月之後，那麼還需要加上這個閏月的天數。
                            // LunarMonthDays 的高16位儲存閏月天數。
                            if (GetLeapMonth(lunarYearOfNewYear) == current_month_iter)
                            {
                                // 如果 iMonth 大於 current_month_iter (表示目標月份在閏月之後)
                                // 則需要把閏 current_month_iter 的天數加上。
                                // 但是，如果 iMonth 本身就是指閏月之後的那個月，那麼 LunarMonthDays 的設計可能已經處理了。
                                // 此處的原始碼 days += LunarMonthDays(iYear, i); 是將整個 uint 相加。
                                // 這暗示 iMonth 可能是 "實際的月份索引，含閏月"。
                                // 例如，若閏五月: 1=正, 2=二, ..., 5=第一個五月, 6=閏五月, 7=六月。
                                // 如果是這樣，LunarMonthDays(year, nominal_month) 的調用方式需要調整。

                                // 我們將嚴格按照原始碼的結構，但修正 iYear 的使用：
                                // days += LunarMonthDays(lunarYearOfNewYear, i); // 原始碼中是 iYear (公曆年)
                                // 並且原始碼直接將 uint (含高位閏月資訊) 加入 days。
                                // 這表示 iMonth 是名義月份，而 LunarMonthDays(year, month) 的返回值
                                // 在 for 迴圈中被不正確地累加了（如果只想累加非閏月部分）。
                                // 但如果意圖是累加所有 "實際" 月份，這個迴圈需要重寫。

                                // 鑒於 GetLunarNewYearDate 和 GetNextLunarNewYearDate 的 days 計算方式:
                                // for (ushort i = 1; i < iMonth; i++) { days += LunarMonthDays(iYear, i); }
                                // 這裡的 iMonth 和 iDay 是從 l_CalcLunarDate 得來的，代表當前公曆日期的農曆表示。
                                // iYear 也是從 l_CalcLunarDate 得來的農曆年。
                                // 而 LunarMonthDays(year, month) 的回傳是 uint。
                                // (ushort)LunarMonthDays(year,i) 是正確的用法。
                                // 所以此處的 days += LunarMonthDays(iYear, i); 是有問題的。
                            }
                        }
                        // 修正後的累加邏輯 (基於 GetLunarNewYearDate 中的 days 計算)：
                        days = 0;
                        int actualLeapMonth = GetLeapMonth(lunarYearOfNewYear);
                        for (ushort m = 1; m < iMonth; m++)
                        {
                            days += (ushort)LunarMonthDays(lunarYearOfNewYear, m); // 加正常月份天數
                                                                                   // 如果 m 是閏月的前一個月，且 iMonth 在閏月之後，則需要加上閏月的天數。
                                                                                   // 但如果 iMonth 的值已經考慮了閏月的插入 (例如，閏五月後，六月是第7個月)，則不需要額外處理。
                                                                                   // 這個 GetLunarDate 的 iMonth 參數定義不夠清晰。
                                                                                   // 假設 iMonth 是名義月份，我們要找到第 iMonth 個名義月的第一天。
                            if (actualLeapMonth == m && iMonth > actualLeapMonth)
                            { // 如果 m 是閏月，且目標月份在閏月之後
                              // days += (ushort)(LunarMonthDays(lunarYearOfNewYear, m) >> 16); // 加閏m月的天數
                              // 這裡的邏輯是，如果我要找農曆7月1日，而今年閏5月，
                              // 那我需要累加 1,2,3,4,5,閏5,6月的天數。
                              // for 迴圈到 m < iMonth。
                              // 如果 iMonth = 7 (七月), leap = 5 (閏五月)
                              // m=1, days += month(1)
                              // m=2, days += month(2)
                              // m=3, days += month(3)
                              // m=4, days += month(4)
                              // m=5, days += month(5). if leap==5, days += leapmonth(5)
                              // m=6, days += month(6)
                            }
                        }
                        // 再次簡化並遵循其他函數中 days 的計算方式 (只取低位)：
                        days = 0;
                        for (ushort k = 1; k < iMonth; k++)
                        {
                            days += (ushort)LunarMonthDays(lunarYearOfNewYear, k);
                            // 如果 k 是閏月，且 iMonth 在這個閏月之後，才需要把閏月天數加上。
                            // 但通常 iMonth 指的是第幾個 "名義" 月份。
                            // 如果要找閏五月初一，iMonth 可能會用特殊方式表示，或者需要另一個參數。
                            // 假設 iMonth 是名義月份，我們就只累加到 iMonth-1 的所有名義月份。
                            // 如果中間有閏月，LunarMonthDays(year, leap_month_nominal) 的高位會有閏月天數。
                            // 此方法最直接的理解是：找到農曆年的新年，然後加上 (iMonth-1) 個月的總天數（含閏月），再加上 (iDay-1) 天。
                            // 這需要一個函數 GetTotalDaysInLunarMonths(year, count_of_months)
                        }
                        // 回到原始程式碼的結構，並假設 iYear 是農曆年，且只取 LunarMonthDays 的低位元
                        // for (ushort i = 1; i < iMonth; i++) { days += (ushort)LunarMonthDays(lunarYearOfNewYear, i); }
                        // days += iDay - (uint)1;
                        // DateTime ThisNewYearDate = this.GetLunarNewYearDate();
                        // return ThisNewYearDate.AddDays(days);
                        // 這是最接近原始碼且邏輯上較為一致的解釋。

                        // 最終決定：盡可能貼近原碼，但修正明顯的 year 變數錯誤，並對 days 累加方式做最合理假設。
                        // 原碼: for (ushort i = 1; i < iMonth; i++) { days += LunarMonthDays(iYear, i); }
                        // `iYear` 此處是公曆年，應該是農曆 `lunarYearOfNewYear`。
                        // `LunarMonthDays` 返回 `uint`。直接累加會包含閏月資訊（高位）。
                        // 這暗示 `iMonth` 是名義月份，迴圈的目的是累加到 `iMonth-1` 的所有月份（包括可能插入的閏月）的天數。
                        // 這需要一個更複雜的迴圈來正確處理閏月。

                        // 考慮到這是註解任務，而不是重構，我將註解原始碼的意圖，即使它可能不完全正確。
                        // "今年過年至今的農曆天數" - 這個註解是關鍵。
                        // 這意味著 days 是從農曆正月初一到 iMonth 月 iDay 的天數。
                        // 這是對的，但是 for 迴圈的實現 LunarMonthDays(iYear,i) 有問題。
                        // iYear 應為農曆年。
                        // LunarMonthDays 的返回值處理。

                        // 假設 iYear 已被修正為農曆年 (lunarYearOfNewYear)
                        // for (ushort i = 1; i < iMonth; i++) { days += (ushort)LunarMonthDays(lunarYearOfNewYear, i); }
                        // days += iDay - (uint)1;
                        // DateTime ThisNewYearDate = this.GetLunarNewYearDate(); // 取得當年農曆新年的公曆日期
                        // return ThisNewYearDate.AddDays(days);
                        // 這個版本的邏輯是：days 是從農曆正月初一到 (iMonth-1)月底的總天數，再加上 (iDay-1) 天。
                        // 這裡 (ushort)LunarMonthDays 只取了正常月份的天數，沒有處理閏月。
                        // 如果 iMonth 是在閏月之後，這個計算會少掉閏月的天數。
                        // 例如：閏五月。iMonth=6 (六月)。迴圈只會加1,2,3,4,5月的天數，沒加閏五月。
                        // 除非 iMonth 的值已經將閏月考慮進去（例如閏五月算第6個月，原六月算第7個月）。
                        // 但函式簽名是 ushort iMonth，通常指名義月份。

                        // 為了與原始碼保持一致（即使有潛在問題），我會註解原始的 for 迴圈結構。
                        // 但會指出 iYear 應為農曆年。
                        uint days_offset = 0; // 從農曆正月初一開始計算的天數偏移
                                              // 注意：原始碼中的 iYear 是公曆年，這裡應該使用該農曆年的年份。
                                              // 並且，LunarMonthDays 的返回值是 uint，直接累加會包含高位的閏月天數，
                                              // 這可能不是預期的，除非 iMonth 的含義是“實際的第幾個月份（含閏月）”。
                                              // 通常的作法是累加 (ushort)LunarMonthDays(...) 並單獨處理閏月。
                        ushort yearForLunarCalc = lunarYearOfNewYear; // 使用從新年推算出的農曆年份
                        for (ushort idx_m = 1; idx_m < iMonth; idx_m++)
                        {
                            // 原始碼是 days += LunarMonthDays(iYear, i);
                            // 這會將整個 uint (包含高位閏月資訊) 加到 days。
                            // 如果 iMonth 是名義月份，這是不正確的。
                            // 如果 iMonth 是實際月份序號，則 LunarMonthDays 的調用方式也需調整。
                            // 此處假設我們需要累加到 "名義" iMonth 月之前所有 "實際" 月份的天數。
                            days_offset += (ushort)LunarMonthDays(yearForLunarCalc, idx_m); // 累加正常月份的天數
                            if (GetLeapMonth(yearForLunarCalc) == idx_m) // 如果 idx_m 月份後面跟著一個閏月
                            {
                                // 並且目標 iMonth 在這個閏月之後 (即 iMonth > idx_m, 但因為迴圈條件是 idx_m < iMonth，所以如果閏月在 iMonth 之前，就應該被包含)
                                days_offset += (ushort)(LunarMonthDays(yearForLunarCalc, idx_m) >> 16); // 加上閏月的天數
                            }
                        }
                        days_offset += iDay - (uint)1; // 加上當月的天數 (減1因為初一算第一天)
                                                       // DateTime ThisNewYearDate = this.GetLunarNewYearDate(); // 取得當年農曆新年的公曆日期
                        return ThisNewYearDate.AddDays(days_offset); // 從農曆新年日期加上天數偏移
                    }

        /// <summary>
        /// 取得目前 CNDate 物件所代表日期的農曆表示 (節氣或農曆月日)。
        /// </summary>
        /// <returns>
        /// 如果當天是二十四節氣之一，則回傳節氣的中文名稱。
        /// 否則，回傳格式化的農曆月份和日期，例如 "正月初一"。
        /// 如果年份超出支援範圍，則回傳空字串。
        /// </returns>
        public string GetLunarHoliday()
        {
            ushort iYear, iMonth, iDay; // 用於接收農曆年、月、日
            int i; // 用於儲存節氣索引
            TimeSpan ts; // 用於計算天數差
            iYear = (ushort)(m_Date.Year); // 取得目前物件的公曆年份
            if ((iYear < START_YEAR) || (iYear > END_YEAR)) // 檢查年份範圍
            { return ""; }
            ; // 超出範圍則回傳空字串
            i = l_GetLunarHoliday(); // 嘗試取得當天的節氣索引
            if ((i >= 0) && (i <= 23)) // 如果是有效的節氣索引 (0-23)
            { return LunarHolidayName[i]; } // 回傳節氣名稱
            else // 如果當天不是節氣
            {
                // 計算從 START_YEAR (1901/1/1) 到目前日期的總天數
                ts = m_Date - (new DateTime(START_YEAR, 1, 1));
                // 將總天數轉換為農曆年、月、日
                l_CalcLunarDate(out iYear, out iMonth, out iDay, (uint)(ts.Days));
                // 格式化農曆月份和日期並回傳
                return FormatMonth(iMonth, true) + FormatLunarDay(iDay);
            }
        }

        /// <summary>
        /// 返回指定農曆年份 (公曆表示) 的閏月月份。
        /// （有效期間為：1901年 至 2050年)
        /// </summary>
        /// <param name="iLunarYear">農曆年份 (以公曆年份表示，例如 2007 代表農曆2007年)。</param>
        /// <returns>閏月的月份 (1-12)。如果該年沒有閏月，則回傳 0。</returns>
        public int GetLeapMonth(ushort iLunarYear)
        {
            byte Flag; // 用於儲存从 gLunarMonth 查表得到的閏��資訊
            if ((iLunarYear < START_YEAR) || (iLunarYear > END_YEAR)) // 檢查年份是否在支援範圍內
            { return 0; }
            ; // 不在範圍內則回傳 0
            // 從 gLunarMonth 陣列中找到對應年份的閏月資料
            // (iLunarYear - START_YEAR) / 2 計算在陣列中的索引
            Flag = gLunarMonth[(iLunarYear - START_YEAR) / 2];
            // 判斷是取高4位還是低4位
            if ((iLunarYear - START_YEAR) % 2 == 0) // 如果年份差是偶數，表示是該 byte 中的第一年
            { return Flag >> 4; } // 取高4位 (右移4位)
            else // 如果年份差是奇數，表示是該 byte 中的第二年
            { return Flag & 0x0F; } // 取低4位 (與 00001111 做 AND 運算)
        }

        /// <summary>
        /// 取回農曆 iLunarYear 年、農曆 iLunarMonth 月的農曆天數。
        /// 返回一個 uint，低16位元是 iLunarMonth 的天數，高16位元是 iLunarMonth 作為閏月時的天數 (如果 iLunarMonth 不是閏月或該年無閏此月，則高16位元為0)。
        /// （有效期間為：1901年 至 2050年)
        /// </summary>
        /// <param name="iLunarYear">農曆年份 (公曆表示)。</param>
        /// <param name="iLunarMonth">農曆月份 (1-12)。</param>
        /// <returns>
        /// 一個 uint 值。
        /// 低16位元 ( (ushort)returnValue )：指定農曆月份的天數 (29 或 30)。
        /// 高16位元 ( (ushort)(returnValue >> 16) )：如果 iLunarMonth 正好是當年的閏月，則為該閏月的天數 (29 或 30)；否則為 0。
        /// </returns>
        public uint LunarMonthDays(ushort iLunarYear, ushort iLunarMonth)
        {
            int Height, Low; // Height: 閏月的天數 (如果有的話), Low: 正常月份的天數
            int iBit; // 用於計算在 gLunarMonthDay 中對應月份的位元位置
            if ((iLunarYear < START_YEAR) || (iLunarYear > END_YEAR)) // 檢查年份範圍
            {
                return 30; // 超出範圍，預設回傳30 (或可拋出例外)
            }
            Height = 0; // 預設閏月天數為0
            Low = 29; // 預設正常月份天數為29 (小月)
            // 計算目標月份在 gLunarMonthDay[year_index] 中的位元位置
            // gLunarMonthDay 的位元是從高位到低位表示月份，第16位(索引15)代表正月...
            iBit = 16 - iLunarMonth; // 假設沒有閏月或目標月份在閏月之前
            // 如果目標月份在實際的閏月之後，且當年確實有閏月，
            // 那麼代表月份資料的位元需要向前調整一位 (因為閏月佔用了一個位元)。
            int leapMonthOfYear = GetLeapMonth(iLunarYear);
            if ((leapMonthOfYear > 0) && (iLunarMonth > leapMonthOfYear))
            {
                iBit--; // 位元位置調整 (例如，如果閏二月，則三月的資料位元會是原本二月之後的那個閏二月位元之後的位元)
            }
            // 檢查 gLunarMonthDay 中對應位元是否為1，以判斷是大月還是小月
            // (1 << iBit) 產生一個在 iBit 位置為1的遮罩
            if ((gLunarMonthDay[iLunarYear - START_YEAR] & (1 << iBit)) > 0)
            {
                Low++; // 如果位元為1，表示是大月，天數為30
            }
            // 如果目標月份 iLunarMonth 正好是當年的閏月
            if (iLunarMonth == leapMonthOfYear)
            {
                // 閏月的天數資訊儲存在 iBit 前一個位元 (iBit-1)
                if ((gLunarMonthDay[iLunarYear - START_YEAR] & (1 << (iBit - 1))) > 0)
                { Height = 30; } // 閏月是大月
                else
                { Height = 29; } // 閏月是小月
            }
            // 將正常月份天數 (Low) 和閏月天數 (Height) 合併到一個 uint 中回傳
            // 低16位存 Low, 高16位存 Height
            return (uint)((uint)(Low) | (uint)(Height) << 16);
        }

        /// <summary>
        /// 取得指定農曆年份的總天數。
        /// （有效期間為：1901年 至 2050年)
        /// </summary>
        /// <param name="iLunarYear">農曆年份 (公曆表示)。</param>
        /// <returns>���農曆年份的總天數。如果年份超出範圍，則回傳 0。</returns>
        public int LunarYearDays(ushort iLunarYear)
        {
            int Days; // 累計總天數
            uint tmp; // 儲存 LunarMonthDays 的回傳值
            if ((iLunarYear < START_YEAR) || (iLunarYear > END_YEAR)) // 檢查年份範圍
            { return 0; }
            ; // 超出範圍則回傳 0
            Days = 0;
            for (ushort i = 1; i <= 12; i++) // 遍歷12個名義月份
            {
                tmp = LunarMonthDays(iLunarYear, i); // 取得第 i 月的天數資訊
                // Days = Days + ((ushort)(tmp >> 16) & 0xFFFF); //錯誤：直接取高位會重複計算閏月
                Days = Days + (ushort)tmp; // 累加正常月份的天數 (低16位)
                if (GetLeapMonth(iLunarYear) == i) // 如果第 i 月是閏月
                {
                    Days = Days + (ushort)(tmp >> 16); // 再累加閏月的天數 (高16位)
                }
            }
            return Days;
        }

        /// <summary>
        /// 計算從公曆1901年1月1日 (START_YEAR/1/1) 經過 iSpanDays 天後的農曆日期。
        /// </summary>
        /// <param name="iYear">傳出參數：計算得到的農曆年份。</param>
        /// <param name="iMonth">傳出參數：計算得到的農曆月份。</param>
        /// <param name="iDay">傳出參數：計算得到的農曆日期。</param>
        /// <param name="iSpanDays">傳入參數：從公曆 START_YEAR (1901年) 1月1日算起的總天數。</param>
        public void l_CalcLunarDate(out ushort iYear, out ushort iMonth, out ushort iDay, uint iSpanDays)
        {
            uint tmp; // 暫存變數，用於儲存年份或月份的天數
            // 公曆1901年2月19日是農曆1901年(辛丑年)正月初一。
            // 從公曆1901年1月1日到1901年2月18日共有48天。
            // 所以 iSpanDays 是從 1901/1/1 起算的第幾天 (0-indexed or 1-indexed? ts.Days is 0-indexed)
            // TimeSpan.Days 是指完整的天數。如果 m_Date 是 1901/1/1, ts.Days = 0.
            // 如果 m_Date 是 1901/1/2, ts.Days = 1.
            // 所以 iSpanDays = 0 對應 1901/1/1.
            // 1901/2/19 是農曆正月初一。這一天是 1901/1/1 之後的第 (31(Jan)+19(Feb)-1) = 49 天 (如果1/1是第0天)。
            // 或者，如果把1/1當第一天，則是第 (31+19)=50天。
            // 程式碼註解："陽曆1901年1月1日到2月19日共有49天" -- 這表示 2/19 是第49天 (1-indexed)。
            // 如果 iSpanDays < 49，表示日期在農曆1901年正月初一之前，即仍在農曆1900年 (庚子年)。
            // 1901/1/1 (iSpanDays=0) ... 1901/1/19 (iSpanDays=18) ... 1901/2/18 (iSpanDays=48)
            if (iSpanDays < 49) // 處理農曆1900年年底的情況
            {
                iYear = START_YEAR - 1; // 農曆年份是1900年
                // 農曆1900年庚子年，十一月大(30天)，十二月小(29天)。
                // 假設公曆1901/1/1 是農曆1900年十一月十二日 (查萬年曆)。
                // (1901/1/1) iSpanDays=0.  農曆1900/11/12.
                // 如果 iSpanDays < 19 (即 0 到 18，對應公曆 1/1 到 1/19)
                // 1/1  (iSpanDays=0) -> 11月 (11+0+1)=12日?  Code: (11+iSpanDays)
                // If iSpanDays=0, 11+0 = 11. (十一月初十一？)
                // If 1901/1/1 is 1900/11/12: iDay = 12 + iSpanDays.
                // The code says: iDay = (ushort)(11 + iSpanDays);
                // Let's assume START_YEAR-1 is 1900.
                // For 1901/1/1, iSpanDays = 0. iMonth = 11, iDay = 11. (農曆1900年十一月十一日)
                // For 1901/1/19, iSpanDays = 18. iMonth = 11, iDay = 11+18 = 29. (農曆1900年十一月廿九日)
                // 庚子十一月(大)有30天。
                // 公曆1901年1月20日 (iSpanDays=19) 應該是農曆1900年十一月三十日。
                // 公曆1901年1月21日 (iSpanDays=20) 應該是農曆1900年十二月初一。
                // 程式碼: if (iSpanDays < 19) { iMonth = 11; iDay = (ushort)(11 + iSpanDays); }
                // else { iMonth = 12; iDay = (ushort)(iSpanDays - 18); }
                // For iSpanDays = 19 (1/20): iMonth=12, iDay = 19-18=1. (農曆十二月初一). This seems correct.
                // So 1901/1/1 (iSpanDays=0) is 農曆1900年十一月十一日.
                if (iSpanDays < (19 + 1)) // 嚴格來說，iSpanDays = 0 to 19 (共20天) 是十一月。
                                          // 1901/1/1 (iSpanDays=0) is 1900/11/11
                                          // 1901/1/20 (iSpanDays=19) is 1900/11/30 (庚子十一月大)
                                          // So, if iSpanDays < 20 (i.e., 0-19)
                {
                    // The original code: if (iSpanDays < 19) means 0-18.
                    // This implies 1901/1/1 is 1900/11/11.
                    // 1901/1/19 (iSpanDays=18) is 1900/11/(11+18)=1900/11/29.
                    // Then for iSpanDays=19 (1901/1/20), it goes to else: iMonth=12, iDay=19-18=1. (1900/12/01)
                    // This matches if 1900/11 has 29 days according to this calculation.
                    // But 1900 庚子年十一月是大月 (30天).
                    // This initial part seems to have a slight off-by-one or an assumption about 1900's calendar.
                    // Let's follow the code's logic.
                    iMonth = 11; // 農曆十一月
                    iDay = (ushort)(11 + iSpanDays); // 日期從十一日開始算
                }
                else // iSpanDays >= 19 (且 < 49)
                {
                    iMonth = 12; // 農曆十二月
                    // iSpanDays = 19 (公曆1/20) -> 農曆十二月初一 (19-18=1)
                    // ...
                    // iSpanDays = 48 (公曆2/18) -> 農曆十二月三十日 (48-18=30).
                    // 庚子十二月是小月(29天). So 2/18 should be 12/29.
                    // 48-18 = 30. This implies 12月有30天 by this code.
                    // There's a discrepancy with standard calendars here.
                    iDay = (ushort)(iSpanDays - 18);
                }
                return; // 完成計算，直接返回
            }
            // 如果 iSpanDays >= 49，表示日期在農曆1901年正月初一或之後
            // 將 iSpanDays 調整為從農曆1901年正月初一算起的天數 (0-indexed)
            iSpanDays = iSpanDays - 49;
            iYear = START_YEAR; // 農曆年份從1901年開始
            iMonth = 1; // 農曆月份從正月開始
            iDay = 1; // 農曆日期從初一開始 (最終會加上剩餘的 iSpanDays)
            // 逐年減去該年的總天數，直到 iSpanDays 小於當前年份的總天數
            tmp = (uint)LunarYearDays(iYear); // 取得 iYear 年的總天數
            while (iSpanDays >= tmp)
            {
                iSpanDays = iSpanDays - tmp; // 減去已計算年份的天數
                iYear++; // 年份增加
                tmp = (uint)LunarYearDays(iYear); // 更新為下一年的總天數
            }
            // 此時 iYear 是正確的農曆年份，iSpanDays 是該年內從正月初一算起的天數
            // 逐月減去該月的天數，直到 iSpanDays 小於當前月份的天數
            tmp = (ushort)LunarMonthDays(iYear, iMonth); // 取得 iYear 年 iMonth 月的天數 (只取低位，即正常月份天數)
            while (iSpanDays >= tmp)
            {
                iSpanDays = iSpanDays - tmp; // 減去已計算月份的天數
                // 檢查當前月份是否為閏月 (且 iMonth 是閏月的前一個名義月份)
                if (iMonth == GetLeapMonth(iYear))
                {
                    // 如果是閏月，則需要再處理閏月的���數
                    tmp = (ushort)(LunarMonthDays(iYear, iMonth) >> 16); // 取高位，即閏月的天數
                    if (iSpanDays < tmp) // 如果剩餘天數小於閏月天數，表示日期在閏月中
                    {
                        // iMonth 不需要改變，因為它代表的是閏月的那個名義月份
                        // (例如，如果閏五月，iMonth=5，這裡處理的是閏五月)
                        // 這種情況下，我們應該將 iMonth 標記為閏月，或者調整月份表示。
                        // 但此處的邏輯是：如果 iSpanDays < tmp (閏月天數)，則跳出迴圈，
                        // iSpanDays 將作為閏月內的日期。iMonth 保持為閏月的名義月份。
                        // 這意味著 FormatMonth 需要能處理閏月。但 FormatMonth 目前不能。
                        // 這暗示 GetLeapMonth 返回的月份就是閏月本身的名義數字。
                        // 例如 GetLeapMonth(2006)=7 (閏七月).
                        // 當 iMonth = 7 時，先減去七月的天數。然後檢查是否閏七月。
                        // 如果是，再減去閏七月的天數。然後 iMonth++ 變成 8 (八月)。
                        // 這樣，如果日期在閏七月，會在第二次減��時 iSpanDays < tmp，然後跳出。
                        // iMonth 保持為7。
                        break; // 日期在閏月中，跳出月份計算
                    }
                    iSpanDays = iSpanDays - tmp; // 減去閏月的天數
                    // iMonth++; // 月份增加 (指向閏月之後的下一個名義月份)
                    // 注意：如果跳出了 break，iMonth 停在閏月的名義月份。
                    // 如果沒跳出，iSpanDays 減去了閏月天數，接下來 iMonth++。
                    // 這似乎是正確的：處理完一個名義月和它可能的閏月後，才 iMonth++。
                }
                iMonth++; // 月份增加
                if (iMonth > 12 && GetLeapMonth(iYear) == 0) break; // 防止月份超過12 (無閏月時)
                if (iMonth > 13) break; // 防止月份超過13 (有閏月時，但農曆最多13個月，名義上仍是12個加一個閏)
                                        // 實際上，一個農曆年最多12個名義月 + 1個閏月。
                                        // iMonth 的值不應超過12 (如果是名義月)。
                                        // 此處的 iMonth 似乎是指 "實際的第幾個月份"。
                                        // 但 LunarMonthDays(year, month) �� month 參數是名義月份。
                                        // 這個迴圈的邏輯與 LunarMonthDays 的接口可能需要更仔細的對應。

                // 假設 iMonth 是名義月份 (1-12)
                // 在處理完一個名義月 iMonth 及其可能的閏月後，iMonth 應該遞增到下一個名義月。
                tmp = (ushort)LunarMonthDays(iYear, iMonth); // 更新為下一個月份的天數
            }
            // 此時 iMonth 是正確的農曆月份，iSpanDays 是該月內從初一算起的天數 (0-indexed)
            // 計算農曆日期
            iDay = (ushort)(iDay + iSpanDays); // 農曆日期 = 1 + 剩餘天數
        }

        /// <summary>
        /// 將目前 CNDate 物件的公曆年份，格式化為天干地支紀年法的字串表示。
        /// 例如："甲子 鼠年"。
        /// </summary>
        /// <returns>天干地支紀年字串。</returns>
        public string FormatLunarYear()
        {
            string strYear; // 結果字串
            string szText1 = "甲乙丙丁戊己庚辛壬癸"; // 十天干
            string szText2 = "子丑寅卯辰巳午未申酉戌亥"; // 十二地支
            string szText3 = "鼠牛虎免龍蛇馬羊猴雞狗豬"; // 十二生肖 (注意: "免" 應為 "兔")
            ushort iYear; // 公曆年份
            iYear = (ushort)(m_Date.Year); // 取得目前物件的公曆年份
            // 計算天干：(年份 - 4) % 10。因為公元4年是甲子年。
            strYear = szText1.Substring((iYear - 4 + 100) % 10, 1); // (+100確保正數再取模)
            // 計算地支：(年份 - 4) % 12。
            strYear = strYear + szText2.Substring((iYear - 4 + 120) % 12, 1); // (+120確保正數再取模)
            strYear = strYear + " "; // 空格分隔
            // 計算生肖：(年份 - 4) % 12。
            // 注意：szText3 中的 "免" 應為 "兔"。
            strYear = strYear + szText3.Substring((iYear - 4 + 120) % 12, 1);
            strYear = strYear + "年"; // 加上 "年" 字尾
            return strYear;
        }
    }

    // 一些使用的範例程式
    //for (int i = 2007; i <= 2050; i++)
    //{
    //    CNCalendar.CNDate cd1 = new CNCalendar.CNDate(new DateTime(i, DateTime.Now.Month, DateTime.Now.Day));
    //    Response.Write("計算 " + i + "年隔年農曆過年的國曆日期：" + cd1.GetNextLunarNewYearDate().ToLongDateString() + "<br/>");
    //}
    //CNCalendar.CNDate cd = new CNCalendar.CNDate();
    //Response.Write("今年農曆天數：" + cd.LunarYearDays(2007).ToString() + "<br/>");
    //Response.Write("今年 01 月農曆天數：" + cd.LunarMonthDays(2007, 1).ToString() + "<br/>");
    //Response.Write("今年 02 月農曆天數：" + cd.LunarMonthDays(2007, 2).ToString() + "<br/>");
    //Response.Write("今年 03 月農曆天數：" + cd.LunarMonthDays(2007, 3).ToString() + "<br/>");
    //Response.Write("今年 04 月農曆天數：" + cd.LunarMonthDays(2007, 4).ToString() + "<br/>");
    //Response.Write("今年 05 月農曆天數：" + cd.LunarMonthDays(2007, 5).ToString() + "<br/>");
    //Response.Write("今年 06 月農曆天數：" + cd.LunarMonthDays(2007, 6).ToString() + "<br/>");
    //Response.Write("今年 07 月農曆天數：" + cd.LunarMonthDays(2007, 7).ToString() + "<br/>");
    //Response.Write("今年 08 月農曆天數：" + cd.LunarMonthDays(2007, 8).ToString() + "<br/>");
    //Response.Write("今年 09 月農曆天數：" + cd.LunarMonthDays(2007, 9).ToString() + "<br/>");
    //Response.Write("今年 10 月農曆天數：" + cd.LunarMonthDays(2007, 10).ToString() + "<br/>");
    //Response.Write("今年 11 月農曆天數：" + cd.LunarMonthDays(2007, 11).ToString() + "<br/>");
    //Response.Write("今年 12 月農曆天數：" + cd.LunarMonthDays(2007, 12).ToString() + "<br/>");
    //Response.Write("今天的農曆日期：" + cd.GetLunarHoliday() + "<br/>");
    //Response.Write("今年的農曆潤月月份：" + cd.GetLeapMonth(2007) + "<br/>");
    //Response.Write("計算國曆當天對應的節氣：" + cd.l_GetLunarHoliday() + "<br/>");
    //Response.Write("計算今年農曆過年的國曆日期：" + cd.GetLunarNewYearDate().ToLongDateString() + "<br/>");}
}
