﻿using Microsoft.Data.SqlClient;
using System;
using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;

namespace WorkdayCalendar
{
    /// <summary>
    /// 工具類別，提供各種常用的公用方法
    /// </summary>
    public class Utility
    {
        /// <summary>
        /// 瀏覽器類型列舉
        /// </summary>
        public enum BrowserType
        {
            INTERNET_EXPLORER,  // Internet Explorer
            MOZILA_FIREFOX,     // Mozilla Firefox
            SAFARI,             // Safari
            NETSCAPE,           // Netscape
            GOOGLE_CHROME,      // Google Chrome
            FLOCK,              // Flock
            UNKNOWN             // 未知瀏覽器
        }

        /// <summary>
        /// 記錄系統事件到資料庫
        /// </summary>
        /// <param name="eventType">事件類型編號</param>
        /// <param name="empNo">員工編號</param>
        /// <param name="description">事件描述</param>
        /// <param name="ip">客戶端IP位址</param>
        /// <param name="connectionString">資料庫連線字串</param>
        public static void EventLog(int eventType, string empNo, string description, string ip, string connectionString)
        {
            // SQL插入語句，將事件資訊記錄到EventLog資料表
            string sqlstr = "INSERT INTO dbo.EventLog (EmpNo, EventTypeId ,[Description] ,IP) VALUES (@EmpNo, @EventTypeId ,@Description ,@IP);";

            // 建立SQL參數陣列以防止SQL注入攻擊
            SqlParameter[] paras = new SqlParameter[4];

            // 設定員工編號參數
            paras[0] = new SqlParameter("@EmpNo", System.Data.SqlDbType.VarChar, 5);
            paras[0].Value = empNo;

            // 設定事件類型編號參數
            paras[1] = new SqlParameter("@EventTypeId", System.Data.SqlDbType.TinyInt);
            paras[1].Value = eventType;

            // 設定IP位址參數
            paras[2] = new SqlParameter("@IP", System.Data.SqlDbType.NVarChar, 50);
            paras[2].Value = ip;

            // 設定事件描述參數
            paras[3] = new SqlParameter("@Description", System.Data.SqlDbType.NVarChar, 1024);
            paras[3].Value = description;

            // 執行SQL非查詢語句
            SqlUtil.ExecuteSqlNonQuery(connectionString, sqlstr, paras);
        }

        /// <summary>
        /// 將農曆日期轉換為西元日期
        /// </summary>
        /// <param name="year">西元年份</param>
        /// <param name="month">農曆月份</param>
        /// <param name="day">農曆日期</param>
        /// <returns>對應的西元日期</returns>
        public static DateTime LunarToSolar(int year, int month, int day)
        {
            // 使用台灣農曆系統進行日期轉換
            TaiwanLunisolarCalendar tlc = new TaiwanLunisolarCalendar();
            int iMonth = month;
            // 將西元年轉換為民國年（西元年減1911）
            int chineseYear = year - 1911;

            // 判斷該年是否為閏年
            if (tlc.IsLeapYear(chineseYear))
            {
                // 取得該年的閏月月份，例如閏4月會得到5
                int leapMonth = tlc.GetLeapMonth(chineseYear);
                // 如果指定月份大於等於閏月，則月份需要加1
                if (month >= leapMonth)
                {
                    iMonth++;
                }
            }
            // 將農曆日期轉換為西元日期
            DateTime solarDate = tlc.ToDateTime(chineseYear, iMonth, day, 0, 0, 0, 0);
            return solarDate;
        }

        /// <summary>
        /// 計算工作時數（DateTime版本）
        /// </summary>
        /// <param name="arrivalTime">到班時間</param>
        /// <param name="departureTime">離班時間</param>
        /// <param name="middayBreakStart">午休開始時間</param>
        /// <param name="middayBreakEnd">午休結束時間</param>
        /// <returns>實際工作時數</returns>
        public static double CalcWorkHours(DateTime arrivalTime, DateTime departureTime, DateTime middayBreakStart, DateTime middayBreakEnd)
        {
            // 如果離班時間晚於午休結束時間，需要扣除午休時間
            if (departureTime > middayBreakEnd)
            {
                return ((departureTime - arrivalTime) - (middayBreakEnd - middayBreakStart)).TotalHours;
            }
            else
            {
                // 如果離班時間早於或等於午休結束時間，不需要扣除午休時間
                return (departureTime - arrivalTime).TotalHours;
            }
        }

        /// <summary>
        /// 計算工作時數（TimeSpan版本）
        /// </summary>
        /// <param name="arrivalTime">到班時間</param>
        /// <param name="departureTime">離班時間</param>
        /// <param name="middayBreakStart">午休開始時間</param>
        /// <param name="middayBreakEnd">午休結束時間</param>
        /// <returns>實際工作時數</returns>
        public static double CalcWorkHours(TimeSpan arrivalTime, TimeSpan departureTime, TimeSpan middayBreakStart, TimeSpan middayBreakEnd)
        {
            // 如果離班時間晚於午休結束時間，需要扣除午休時間
            if (departureTime > middayBreakEnd)
            {
                return ((departureTime - arrivalTime) - (middayBreakEnd - middayBreakStart)).TotalHours;
            }
            else
            {
                // 如果離班時間早於或等於午休結束時間，不需要扣除午休時間
                return (departureTime - arrivalTime).TotalHours;
            }
        }

        /// <summary>
        /// 根據用戶代理字串判斷瀏覽器類型
        /// </summary>
        /// <param name="userAgent">HTTP請求中的User-Agent字串</param>
        /// <returns>瀏覽器類型列舉值</returns>
        public static BrowserType GetBrowserType(string userAgent)
        {
            // 檢查用戶代理字串是否為空
            if (userAgent != null)
            {
                // 依序檢查各種瀏覽器的識別字串
                if (userAgent.IndexOf("MSIE") != -1)
                    return BrowserType.INTERNET_EXPLORER;
                if (userAgent.IndexOf("Netscape") != -1)
                    return BrowserType.NETSCAPE;
                if (userAgent.IndexOf("Chrome") != -1)
                    return BrowserType.GOOGLE_CHROME;
                if (userAgent.IndexOf("Flock") != -1)
                    return BrowserType.FLOCK;
                if (userAgent.IndexOf("Safari") != -1)
                    return BrowserType.SAFARI;
                if (userAgent.IndexOf("Firefox") != -1)
                    return BrowserType.MOZILA_FIREFOX;
            }
            // 如果無法識別或用戶代理字串為空，返回未知類型
            return BrowserType.UNKNOWN;
        }

        /// <summary>
        /// 取得登入使用者的帳號名稱
        /// </summary>
        /// <param name="identity">身分驗證物件</param>
        /// <returns>使用者帳號名稱</returns>
        public static string GetLogonUser(System.Security.Principal.IIdentity identity)
        {
            // 在Windows認證時應該等同於 HttpContext.Current.User.Identity.Name的值
            return identity.Name;
        }

        /// <summary>
        /// 檢查指定使用者是否為系統管理員
        /// </summary>
        /// <param name="userId">使用者ID（員工編號）</param>
        /// <param name="connectionString">資料庫連線字串</param>
        /// <returns>若為管理員則返回true，否則返回false</returns>
        public static bool IsAdmin(string userId, string connectionString)
        {
            bool ret = false;
            // 查詢使用者是否具有管理員權限
            string sqlstr = "SELECT IsAdmin FROM dbo.Users WHERE EmpNo=@EmpNo AND IsAdmin=1;";

            // 建立SQL參數以防止SQL注入攻擊
            SqlParameter parameter = new SqlParameter("@EmpNo", System.Data.SqlDbType.VarChar, 5)
            {
                Value = userId
            };

            // 執行查詢並取得結果
            System.Data.DataTable dt = SqlUtil.GetDataTable(connectionString, sqlstr, parameter);

            // 如果查詢結果有一筆記錄，表示該使用者為管理員
            if (dt.Rows.Count == 1)
            {
                ret = true;
            }
            return ret;
        }

        /// <summary>
        /// 驗證電子郵件地址格式是否正確
        /// </summary>
        /// <param name="strIn">要驗證的電子郵件地址字串</param>
        /// <returns>若格式正確則返回true，否則返回false</returns>
        public static bool IsValidEmail(string strIn)
        {
            // 使用正規表達式驗證電子郵件格式
            return Regex.IsMatch(strIn,
                @"^([\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$");
        }

        /// <summary>
        /// 檢查字串是否表示真值（true）
        /// </summary>
        /// <param name="strVariable">要檢查的字串變數</param>
        /// <returns>若字串表示真值則返回true，否則返回false</returns>
        public static bool CheckTrue(string strVariable)
        {
            bool ret = false;
            // 檢查字串是否為空
            if (strVariable != null)
            {
                // 檢查字串是否為表示真值的字串
                switch (strVariable)
                {
                    case "on":      // HTML表單中checkbox的值
                    case "checked": // 已勾選狀態
                    case "true":    // 布林真值的字串表示
                        ret = true;
                        break;
                }
            }
            return ret;
        }

        /// <summary>
        /// 從使用者名稱中取得有效的使用者ID（僅限指定網域）
        /// </summary>
        /// <param name="username">完整的使用者名稱（包含網域，格式：網域\帳號）</param>
        /// <returns>有效的使用者ID，若不符合指定網域則返回空字串</returns>
        public static string GetValidUserID(string username)
        {
            string userid = "";
            // 取得網域名稱並轉為小寫
            string domain = GetDomain(username).ToLower();

            // 只有secinc或sinotech網域的使用者才被視為有效
            if (domain == "secinc" || domain == "sinotech")
            {
                if (!string.IsNullOrEmpty(username))
                {
                    // 從完整使用者名稱中提取帳號部分
                    userid = username.Substring(username.IndexOf(@"\") + 1);
                }
            }

            return userid;
        }

        /// <summary>
        /// 從使用者名稱中提取使用者ID（不限制網域）
        /// </summary>
        /// <param name="username">完整的使用者名稱（包含網域，格式：網域\帳號）</param>
        /// <returns>使用者ID（帳號部分）</returns>
        public static string GetUserID(string username)
        {
            string userid = "";
            if (!string.IsNullOrEmpty(username))
            {
                // 從完整使用者名稱中提取帳號部分（反斜線後的部分）
                userid = username.Substring(username.IndexOf(@"\") + 1);
            }

            return userid;
            // 以下為測試用的註解代碼
            //return "0349";
            //return "2214";
            //return "1455";
            //return "0316";
        }

        /// <summary>
        /// 將輸入字串編碼為MIME Base64格式
        /// </summary>
        /// <param name="input">要編碼的輸入字串</param>
        /// <returns>MIME Base64編碼後的字串</returns>
        public static string MimeBase64Encoding(string input)
        {
            // 將字串轉換為UTF-8位元組陣列，再轉為Base64，最後包裝成MIME格式
            return string.Format(@"=?UTF-8?B?{0}?=", Convert.ToBase64String(Encoding.UTF8.GetBytes(input)));
        }

        /// <summary>
        /// 從使用者名稱中提取網域名稱
        /// </summary>
        /// <param name="username">完整的使用者名稱（格式：網域\帳號）</param>
        /// <returns>網域名稱（大寫）</returns>
        public static string GetDomain(string username)
        {
            // 提取反斜線前的網域部分並轉為大寫
            return username.Substring(0, username.IndexOf(@"\")).ToUpper();
        }
    }
}
