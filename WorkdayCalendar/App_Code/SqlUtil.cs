﻿using Microsoft.Data.SqlClient;
using System;
using System.Collections.Generic;
using System.Data;

namespace WorkdayCalendar
{
    /// <summary>
    /// SQL 工具類別，提供資料庫操作的靜態方法
    /// 包含執行 SQL 查詢、預存程序、以及資料庫更新等功能
    /// </summary>
    public static class SqlUtil
    {
        /// <summary>
        /// 資料庫命令執行逾時時間（秒）
        /// 預設值為 36000 秒（10 小時），可透過 SetCommandTimeout 方法設定
        /// 或從應用程式配置檔案中讀取 DatabaseSettings:CommandTimeoutSeconds
        /// </summary>
        public static int CommandTimeoutSeconds { get; private set; } = 36000;

        /// <summary>
        /// 設定資料庫命令執行逾時時間
        /// 通常在應用程式啟動時呼叫，從配置檔案讀取設定值
        /// </summary>
        /// <param name="timeoutSeconds">逾時時間（秒）</param>
        public static void SetCommandTimeout(int timeoutSeconds)
        {
            CommandTimeoutSeconds = timeoutSeconds;
        }

        /// <summary>
        /// 執行 SQL 查詢並取得資料表
        /// </summary>
        /// <param name="connstr">資料庫連線字串</param>
        /// <param name="sqlstr">SQL 查詢語句</param>
        /// <returns>查詢結果的 DataTable 物件</returns>
        public static DataTable GetDataTable(string connstr, string sqlstr)
        {
            // 建立資料集物件用於儲存查詢結果
            DataSet myDataSet = new DataSet();

            // 使用 using 確保所有 IDisposable 物件會被正確釋放
            using (SqlConnection mySqlConnection = new SqlConnection(connstr))
            {
                using (SqlDataAdapter mySqlDataAdapter = new SqlDataAdapter(sqlstr, mySqlConnection))
                {
                    try
                    {
                        // 設定命令執行逾時時間，從配置檔案讀取或使用預設值
                        mySqlDataAdapter.SelectCommand.CommandTimeout = CommandTimeoutSeconds;
                        // 執行查詢並將結果填入資料集
                        mySqlDataAdapter.Fill(myDataSet, "Result");
                    }
                    // 捕捉 SQL 例外狀況
                    catch (SqlException ex)
                    {
                        // 輸出例外資訊到控制台
                        Console.WriteLine(ex);
                    }
                }
            }
            // 回傳查詢結果資料表
            return myDataSet.Tables["Result"];
        }

        /// <summary>
        /// 透過資料庫執行 SQL 查詢並回傳資料表（帶單一參數）
        /// </summary>
        /// <param name="connStr">資料庫連線字串</param>
        /// <param name="sqlCommand">SQL 命令語句</param>
        /// <param name="sp">SQL 參數</param>
        /// <returns>查詢結果的 DataTable 物件</returns>
        public static DataTable GetDataTable(string connStr, string sqlCommand, SqlParameter sp)
        {
            // 建立參數陣列，將單一參數放入陣列中
            SqlParameter[] parameters = new SqlParameter[1];
            parameters[0] = sp;
            // 呼叫多參數版本的方法
            return GetDataTable(connStr, sqlCommand, parameters);
        }

        /// <summary>
        /// 透過資料庫執行 SQL 查詢並回傳資料表（帶多個參數）
        /// </summary>
        /// <param name="connStr">資料庫連線字串</param>
        /// <param name="sqlCommand">SQL 命令語句</param>
        /// <param name="sp">SQL 參數陣列</param>
        /// <returns>查詢結果的 DataTable 物件</returns>
        public static DataTable GetDataTable(string connStr, string sqlCommand, SqlParameter[] sp)
        {
            // 使用 using 確保資料庫連線會被正確釋放
            using (SqlConnection conn = new SqlConnection(connStr))
            {
                // 使用 using 確保 SQL 命令物件會被正確釋放
                using (SqlCommand cmd = new SqlCommand(sqlCommand, conn))
                {
                    // 設定命令執行逾時時間，從配置檔案讀取或使用預設值
                    cmd.CommandTimeout = CommandTimeoutSeconds;
                    // 開啟資料庫連線
                    conn.Open();

                    // 檢查是否有參數需要加入
                    if (sp != null)
                    {
                        // 遍歷所有參數並加入到命令物件中
                        foreach (SqlParameter param in sp)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }

                    // 使用 using 確保 SqlDataAdapter 會被正確釋放
                    using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                    {
                        // 建立資料表物件
                        DataTable dt = new DataTable();
                        // 設定資料表名稱為成功表
                        dt.TableName = "SuccessTable";

                        // 嘗試執行查詢
                        try
                        {
                            // 執行查詢並填入資料表
                            da.Fill(dt);
                            return dt;
                        }
                        // 捕捉任何例外狀況
                        catch (Exception e)
                        {
                            // 建立錯誤資料表
                            DataTable errorTable = new DataTable();
                            // 設定錯誤表名稱
                            errorTable.TableName = "FailTable";
                            // 加入錯誤訊息欄位
                            errorTable.Columns.Add("Message", typeof(string));
                            // 將例外訊息加入錯誤表
                            errorTable.Rows.Add(e.ToString());
                            return errorTable;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 透過資料庫執行 SQL 查詢並回傳資料表（帶參數清單）
        /// </summary>
        /// <param name="connStr">資料庫連線字串</param>
        /// <param name="sqlCommand">SQL 命令語句</param>
        /// <param name="sp">SQL 參數清單</param>
        /// <returns>查詢結果的 DataTable 物件</returns>
        public static DataTable GetDataTable(string connStr, string sqlCommand, List<SqlParameter> sp)
        {
            // 使用 using 確保資料庫連線會被正確釋放
            using (SqlConnection conn = new SqlConnection(connStr))
            {
                // 使用 using 確保 SQL 命令物件會被正確釋放
                using (SqlCommand cmd = new SqlCommand(sqlCommand, conn))
                {
                    // 設定命令執行逾時時間，從配置檔案讀取或使用預設值
                    cmd.CommandTimeout = CommandTimeoutSeconds;
                    // 開啟資料庫連線
                    conn.Open();

                    // 檢查是否有參數需要加入
                    if (sp != null)
                    {
                        // 遍歷所有參數並加入到命令物件中
                        foreach (SqlParameter param in sp)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }

                    // 使用 using 確保 SqlDataAdapter 會被正確釋放
                    using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                    {
                        // 建立資料表物件
                        DataTable dt = new DataTable();
                        // 設定資料表名稱為成功表
                        dt.TableName = "SuccessTable";

                        // 嘗試執行查詢
                        try
                        {
                            // 執行查詢並填入資料表
                            da.Fill(dt);
                            return dt;
                        }
                        // 捕捉任何例外狀況
                        catch (Exception e)
                        {
                            // 建立錯誤資料表
                            DataTable errorTable = new DataTable();
                            // 設定錯誤表名稱
                            errorTable.TableName = "FailTable";
                            // 加入錯誤訊息欄位
                            errorTable.Columns.Add("Message", typeof(string));
                            // 將例外訊息加入錯誤表
                            errorTable.Rows.Add(e.ToString());
                            return errorTable;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 執行 SQL 查詢並取得資料集
        /// </summary>
        /// <param name="connstr">資料庫連線字串</param>
        /// <param name="sqlstr">SQL 查詢語句</param>
        /// <returns>查詢結果的 DataSet 物件</returns>
        public static DataSet GetFields(string connstr, string sqlstr)
        {
            // 建立資料集物件
            DataSet myDataSet = new DataSet();
            // 使用 using 確保資料庫連線會被正確釋放
            using (SqlConnection mySqlConnection = new SqlConnection(connstr))
            {
                // 使用 using 確保 SqlDataAdapter 會被正確釋放
                using (SqlDataAdapter mySqlDataAdapter = new SqlDataAdapter(sqlstr, mySqlConnection))
                {
                    // 嘗試執行資料庫查詢
                    try
                    {
                        // 設定命令執行逾時時間，從配置檔案讀取或使用預設值
                        mySqlDataAdapter.SelectCommand.CommandTimeout = CommandTimeoutSeconds;
                        // 執行查詢並將結果填入資料集
                        mySqlDataAdapter.Fill(myDataSet, "Result");
                    }
                    // 捕捉 SQL 例外狀況
                    catch (SqlException e)
                    {
                        // 輸出例外資訊到控制台
                        Console.WriteLine(e);
                    }
                }
            }
            // 回傳查詢結果資料集
            return myDataSet;
        }

        /// <summary>
        /// 透過預存程序回傳資料表（帶單一參數）
        /// </summary>
        /// <param name="connectionString">資料庫連線字串</param>
        /// <param name="storedProcedureName">預存程序名稱</param>
        /// <param name="sqlParam">SQL 參數</param>
        /// <returns>執行結果的 DataTable 物件</returns>
        public static DataTable StoredProcedureToDataTable(string connectionString, string storedProcedureName,
            SqlParameter sqlParam)
        {
            // 建立參數陣列，將單一參數放入陣列中
            SqlParameter[] sqlParams = new SqlParameter[1];
            sqlParams[0] = sqlParam;
            // 呼叫多參數版本的方法
            return StoredProcedureToDataTable(connectionString, storedProcedureName, sqlParams);
        }

        /// <summary>
        /// 透過預存程序回傳資料集（帶多個參數）
        /// </summary>
        /// <param name="connectionString">資料庫連線字串</param>
        /// <param name="storedProcedureName">預存程序名稱</param>
        /// <param name="sqlParams">SQL 參數陣列</param>
        /// <returns>執行結果的 DataSet 物件</returns>
        public static DataSet StoredProcedureToDataSet(string connectionString, string storedProcedureName,
            SqlParameter[] sqlParams)
        {
            // 使用 using 確保資料庫連線會被正確釋放
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                // 使用 using 確保 SQL 命令物件會被正確釋放
                using (SqlCommand cmd = new SqlCommand(storedProcedureName, conn))
                {
                    // 設定命令類型為預存程序
                    cmd.CommandType = CommandType.StoredProcedure;
                    // 設定命令執行逾時時間，從配置檔案讀取或使用預設值
                    cmd.CommandTimeout = CommandTimeoutSeconds;

                    // 檢查是否有參數需要加入
                    if (sqlParams != null)
                    {
                        // 遍歷所有參數並加入到命令物件中
                        foreach (SqlParameter param in sqlParams)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }
                    // 使用 using 確保 SqlDataAdapter 會被正確釋放
                    using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                    {
                        // 建立資料集物件
                        DataSet ds = new DataSet();

                        // 嘗試執行預存程序
                        try
                        {
                            // 執行查詢並填入資料集
                            da.Fill(ds);
                            return ds;
                        }
                        // 捕捉任何例外狀況
                        catch (Exception e)
                        {
                            // 建立錯誤資料表
                            DataTable errorTable = new DataTable();
                            // 設定錯誤表名稱
                            errorTable.TableName = "FailTable";
                            // 加入錯誤訊息欄位
                            errorTable.Columns.Add("Message", typeof(string));
                            // 將例外訊息加入錯誤表
                            errorTable.Rows.Add(e.Message);
                            //ErrorTable.Rows.Add(new object[] { e.ToString() });
                            // 將錯誤表加入資料集
                            ds.Tables.Add(errorTable);
                            return ds;
                        }
                        finally
                        {
                            // 清除命令參數
                            cmd.Parameters.Clear();
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 透過預存程序回傳資料集（帶參數清單）
        /// </summary>
        /// <param name="connectionString">資料庫連線字串</param>
        /// <param name="storedProcedureName">預存程序名稱</param>
        /// <param name="sqlParams">SQL 參數清單</param>
        /// <returns>執行結果的 DataSet 物件</returns>
        public static DataSet StoredProcedureToDataSet(string connectionString, string storedProcedureName,
            List<SqlParameter> sqlParams)
        {
            // 使用 using 確保資料庫連線會被正確釋放
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                // 使用 using 確保 SQL 命令物件會被正確釋放
                using (SqlCommand cmd = new SqlCommand(storedProcedureName, conn))
                {
                    // 設定命令類型為預存程序
                    cmd.CommandType = CommandType.StoredProcedure;
                    // 設定命令執行逾時時間，從配置檔案讀取或使用預設值
                    cmd.CommandTimeout = CommandTimeoutSeconds;

                    // 檢查是否有參數需要加入
                    if (sqlParams != null)
                    {
                        // 遍歷所有參數並加入到命令物件中
                        foreach (SqlParameter param in sqlParams)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }
                    // 使用 using 確保 SqlDataAdapter 會被正確釋放
                    using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                    {
                        // 建立資料集物件
                        DataSet ds = new DataSet();

                        // 嘗試執行預存程序
                        try
                        {
                            // 執行查詢並填入資料集
                            da.Fill(ds);
                            return ds;
                        }
                        // 捕捉任何例外狀況
                        catch (Exception e)
                        {
                            // 建立錯誤資料表
                            DataTable errorTable = new DataTable();
                            // 設定錯誤表名稱
                            errorTable.TableName = "FailTable";
                            // 加入錯誤訊息欄位
                            errorTable.Columns.Add("Message", typeof(string));
                            // 將例外訊息加入錯誤表
                            errorTable.Rows.Add(e.Message);
                            //ErrorTable.Rows.Add(new object[] { e.ToString() });
                            // 將錯誤表加入資料集
                            ds.Tables.Add(errorTable);
                            return ds;
                        }
                        finally
                        {
                            // 清除命令參數
                            cmd.Parameters.Clear();
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 透過預存程序回傳資料表（帶多個參數）
        /// </summary>
        /// <param name="connectionString">資料庫連線字串</param>
        /// <param name="storedProcedureName">預存程序名稱</param>
        /// <param name="sqlParams">SQL 參數陣列</param>
        /// <returns>執行結果的 DataTable 物件</returns>
        public static DataTable StoredProcedureToDataTable(string connectionString, string storedProcedureName,
            SqlParameter[] sqlParams)
        {
            // 使用 using 確保資料庫連線會被正確釋放
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                // 使用 using 確保 SQL 命令物件會被正確釋放
                using (SqlCommand cmd = new SqlCommand(storedProcedureName, conn))
                {
                    // 設定命令類型為預存程序
                    cmd.CommandType = CommandType.StoredProcedure;
                    // 設定命令執行逾時時間，從配置檔案讀取或使用預設值
                    cmd.CommandTimeout = CommandTimeoutSeconds;

                    // 檢查是否有參數需要加入
                    if (sqlParams != null)
                    {
                        // 遍歷所有參數並加入到命令物件中
                        foreach (SqlParameter param in sqlParams)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }

                    // 使用 using 確保 SqlDataAdapter 會被正確釋放
                    using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                    {
                        // 建立資料表物件
                        DataTable dt = new DataTable();
                        // 設定資料表名稱為成功表
                        dt.TableName = "SuccessTable";

                        // 嘗試執行預存程序
                        try
                        {
                            // 執行查詢並填入資料表
                            da.Fill(dt);
                            return dt;
                        }
                        // 捕捉任何例外狀況
                        catch (Exception e)
                        {
                            // 建立錯誤資料表
                            DataTable errorTable = new DataTable();
                            // 設定錯誤表名稱
                            errorTable.TableName = "FailTable";
                            // 加入錯誤訊息欄位
                            errorTable.Columns.Add("Message", typeof(string));
                            // 將例外訊息加入錯誤表
                            errorTable.Rows.Add(e.ToString());
                            return errorTable;
                        }
                        finally
                        {
                            // 清除命令參數
                            cmd.Parameters.Clear();
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 透過預存程序回傳資料表（帶參數清單）
        /// </summary>
        /// <param name="connectionString">資料庫連線字串</param>
        /// <param name="storedProcedureName">預存程序名稱</param>
        /// <param name="sqlParams">SQL 參數清單</param>
        /// <returns>執行結果的 DataTable 物件</returns>
        public static DataTable StoredProcedureToDataTable(string connectionString, string storedProcedureName,
            List<SqlParameter> sqlParams)
        {
            // 使用 using 確保資料庫連線會被正確釋放
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                // 使用 using 確保 SQL 命令物件會被正確釋放
                using (SqlCommand cmd = new SqlCommand(storedProcedureName, conn))
                {
                    // 設定命令類型為預存程序
                    cmd.CommandType = CommandType.StoredProcedure;
                    // 設定命令執行逾時時間，從配置檔案讀取或使用預設值
                    cmd.CommandTimeout = CommandTimeoutSeconds;

                    // 檢查是否有參數需要加入
                    if (sqlParams != null)
                    {
                        // 遍歷所有參數並加入到命令物件中
                        foreach (SqlParameter param in sqlParams)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }

                    // 使用 using 確保 SqlDataAdapter 會被正確釋放
                    using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                    {
                        // 建立資料表物件
                        DataTable dt = new DataTable();
                        // 設定資料表名稱為成功表
                        dt.TableName = "SuccessTable";

                        // 嘗試執行預存程序
                        try
                        {
                            // 執行查詢並填入資料表
                            da.Fill(dt);
                            return dt;
                        }
                        // 捕捉任何例外狀況
                        catch (Exception e)
                        {
                            // 建立錯誤資料表
                            DataTable errorTable = new DataTable();
                            // 設定錯誤表名稱
                            errorTable.TableName = "FailTable";
                            // 加入錯誤訊息欄位
                            errorTable.Columns.Add("Message", typeof(string));
                            // 將例外訊息加入錯誤表
                            errorTable.Rows.Add(e.ToString());
                            return errorTable;
                        }
                        finally
                        {
                            // 清除命令參數
                            cmd.Parameters.Clear();
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 透過預存程序回傳資料表（無參數）
        /// </summary>
        /// <param name="connectionString">資料庫連線字串</param>
        /// <param name="storedProcedureName">預存程序名稱</param>
        /// <returns>執行結果的 DataTable 物件</returns>
        public static DataTable StoredProcedureToDataTable(string connectionString, string storedProcedureName)
        {
            // 使用 using 確保資料庫連線會被正確釋放
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                using (SqlCommand cmd = new SqlCommand(storedProcedureName, conn))
                {
                    // 設定命令執行逾時時間，從配置檔案讀取或使用預設值
                    cmd.CommandTimeout = CommandTimeoutSeconds;
                    // 設定命令類型為預存程序
                    cmd.CommandType = CommandType.StoredProcedure;
                    // 開啟資料庫連線
                    conn.Open();

                    // 使用 using 確保 SqlDataAdapter 會被正確釋放
                    using (SqlDataAdapter da = new SqlDataAdapter(cmd))
                    {
                        // 建立資料表物件
                        DataTable dt = new DataTable();
                        // 設定資料表名稱為成功表
                        dt.TableName = "SuccessTable";

                        // 嘗試執行預存程序
                        try
                        {
                            // 執行查詢並填入資料表
                            da.Fill(dt);
                            return dt;
                        }
                        // 捕捉任何例外狀況
                        catch (Exception e)
                        {
                            // 建立錯誤資料表
                            DataTable errorTable = new DataTable();
                            // 設定錯誤表名稱
                            errorTable.TableName = "FailTable";
                            // 加入錯誤訊息欄位
                            errorTable.Columns.Add("Message", typeof(string));
                            // 將例外訊息加入錯誤表
                            errorTable.Rows.Add(e.ToString());
                            return errorTable;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 執行 SQL 更新操作（INSERT、UPDATE、DELETE）
        /// </summary>
        /// <param name="connstr">資料庫連線字串</param>
        /// <param name="sqlstr">SQL 語句</param>
        /// <returns>受影響的資料列數，-1 表示執行失敗</returns>
        public static int ExecuteUpdate(string connstr, string sqlstr)
        {
            // 宣告受影響的資料列數變數，初始值設為 -1 表示失敗
            int numRowsAffected = -1;

            // 使用 using 確保所有 IDisposable 物件會被正確釋放
            using (SqlConnection mySqlConnection = new SqlConnection(connstr))
            {
                using (SqlCommand mySqlCommand = new SqlCommand(sqlstr, mySqlConnection))
                {
                    try
                    {
                        // 設定命令執行逾時時間
                        mySqlCommand.CommandTimeout = CommandTimeoutSeconds;
                        // 開啟資料庫連線
                        mySqlConnection.Open();

                        // 執行命令並取得受影響的資料列數
                        numRowsAffected = mySqlCommand.ExecuteNonQuery();
                    }
                    // 捕捉 SQL 例外狀況
                    catch (SqlException e)
                    {
                        // 輸出例外資訊到控制台
                        Console.WriteLine(e);
                        // 設定回傳值為 -1 表示失敗
                        numRowsAffected = -1;
                    }
                }
            }
            // 回傳受影響的資料列數
            return numRowsAffected;
        }

        /// <summary>
        /// 執行 SQL 查詢並讀取第一筆第一欄的資料
        /// </summary>
        /// <param name="connstr">資料庫連線字串</param>
        /// <param name="sqlstr">SQL 查詢語句</param>
        /// <returns>查詢結果的第一筆第一欄資料，若無資料或發生錯誤則回傳空字串或 null</returns>
        public static string DataReader(string connstr, string sqlstr)
        {
            // 宣告回傳值變數
            string ret = "";

            // 使用 using 確保所有 IDisposable 物件會被正確釋放
            using (SqlConnection connection = new SqlConnection(connstr))
            {
                using (SqlCommand myCmd = new SqlCommand(sqlstr, connection))
                {
                    try
                    {
                        // 設定命令執行逾時時間，從配置檔案讀取或使用預設值
                        myCmd.CommandTimeout = CommandTimeoutSeconds;
                        // 開啟資料庫連線
                        connection.Open();

                        // 執行命令並取得資料讀取器
                        using (SqlDataReader dataReader = myCmd.ExecuteReader())
                        {
                            // 檢查是否有資料可讀取
                            if (dataReader.Read())
                            {
                                // 取得第一筆第一欄的資料並轉換為字串
                                ret = dataReader.GetValue(0).ToString();
                            }
                        }
                    }
                    // 捕捉任何例外狀況
                    catch (Exception e)
                    {
                        // 設定回傳值為 null 表示發生錯誤
                        ret = null;
                        // 輸出例外資訊到控制台
                        Console.WriteLine(e);
                    }
                }
            }
            // 回傳查詢結果
            return ret;
        }

        /// <summary>
        /// 執行 SQL 查詢並將所有欄位和資料組成特定格式的字串
        /// 欄位名稱使用 ` 分隔，每筆資料使用 # 分隔
        /// </summary>
        /// <param name="connstr">資料庫連線字串</param>
        /// <param name="sqlstr">SQL 查詢語句</param>
        /// <returns>格式化的查詢結果字串，若發生錯誤則回傳 null</returns>
        public static string DataReaders(string connstr, string sqlstr)
        {
            // 宣告回傳值變數
            string ret = "";

            // 使用 using 確保所有 IDisposable 物件會被正確釋放
            using (SqlConnection mySqlConnection = new SqlConnection(connstr))
            {
                using (SqlCommand myCmd = new SqlCommand(sqlstr, mySqlConnection))
                {
                    try
                    {
                        // 設定命令執行逾時時間，從配置檔案讀取或使用預設值
                        myCmd.CommandTimeout = CommandTimeoutSeconds;
                        // 開啟資料庫連線
                        mySqlConnection.Open();

                        // 執行命令並取得資料讀取器
                        using (SqlDataReader myReader = myCmd.ExecuteReader())
                        {
                            // 組合欄位名稱字串（除了最後一欄用 ` 分隔）
                            for (int i = 0; i < myReader.FieldCount - 1; i++)
                            {
                                // 將欄位名稱與 ` 符號連接
                                ret = string.Concat(ret, myReader.GetName(i), "`");
                            }
                            // 加入最後一欄欄位名稱，後面用 # 分隔
                            ret = string.Concat(ret, myReader.GetName(myReader.FieldCount - 1), "#");

                            // 讀取每一筆資料並組合成字串
                            while (myReader.Read())
                            {
                                // 組合每筆資料的各欄位值（除了最後一欄用 ` 分隔）
                                for (int i = 0; i < myReader.FieldCount - 1; i++)
                                {
                                    // 檢查資料是否為 DBNull，如果是則使用空字串，否則使用實際值
                                    ret = string.Concat(ret, Convert.IsDBNull(myReader.GetValue(i)) ? "" : myReader.GetValue(i), "`");
                                }
                                // 加入最後一欄的值，後面用 # 分隔
                                ret = string.Concat(ret,
                                    Convert.IsDBNull(myReader.GetValue(myReader.FieldCount - 1))
                                        ? ""
                                        : myReader.GetValue(myReader.FieldCount - 1), "#");
                            }
                        }
                    }
                    // 捕捉任何例外狀況
                    catch (Exception e)
                    {
                        // 設定回傳值為 null 表示發生錯誤
                        ret = null;
                        // 輸出例外資訊到控制台
                        Console.WriteLine(e);
                    }
                }
            }
            // 回傳格式化的查詢結果字串
            return ret;
        }

        /// <summary>
        ///     執行SQL Command(Insert、Update、Delete, -1代表失敗)
        /// </summary>
        /// <param name="connStr">The connection string.</param>
        /// <param name="sql">SQL語法字串</param>
        /// <returns>
        ///     回傳影響筆數
        /// </returns>
        public static int ExecuteSqlCommand(string connStr, string sql)
        {
            int returnValue = -1;

            using (SqlConnection conn = new SqlConnection(connStr))
            {
                using (SqlCommand sqlCommand = new SqlCommand(sql, conn))
                {
                    try
                    {
                        sqlCommand.Connection.Open();
                        returnValue = sqlCommand.ExecuteNonQuery();
                    }
                    catch (Exception)
                    {
                        return returnValue;
                    }
                }
            }
            return returnValue;
        }

        /// <summary>
        ///     執行SQL Command(Insert、Update、Delete, -1代表失敗)，帶參數
        /// </summary>
        /// <param name="connStr">The connection string.</param>
        /// <param name="sql">SQL語法字串</param>
        /// <param name="sp">Params</param>
        /// <returns>
        ///     回傳影響筆數
        /// </returns>
        public static int ExecuteSqlCommand(string connStr, string sql, SqlParameter[] sp)
        {
            int returnValue = -1;

            using (SqlConnection conn = new SqlConnection(connStr))
            {
                using (SqlCommand sqlCommand = new SqlCommand(sql, conn))
                {
                    if (sp != null)
                    {
                        foreach (SqlParameter param in sp)
                        {
                            sqlCommand.Parameters.Add(param);
                        }
                    }

                    try
                    {
                        sqlCommand.Connection.Open();
                        returnValue = sqlCommand.ExecuteNonQuery();
                    }
                    catch (Exception)
                    {
                        return returnValue;
                    }
                    finally
                    {
                        sqlCommand.Parameters.Clear();
                    }
                }
            }
            return returnValue;
        }


        /// <summary>
        ///     執行SQL Command，回傳第一個資料值(Insert、Update、Delete, null代表失敗)
        /// </summary>
        /// <param name="connStr"></param>
        /// <param name="sql">SQL語法字串</param>
        /// <returns>回傳影響筆數</returns>
        public static object ExecuteSqlScalar(string connStr, string sql)
        {
            object returnValue;

            using (SqlConnection conn = new SqlConnection(connStr))
            {
                using (SqlCommand sqlCommand = new SqlCommand(sql, conn))
                {
                    try
                    {
                        sqlCommand.Connection.Open();
                        returnValue = sqlCommand.ExecuteScalar();
                    }
                    catch (Exception e)
                    {
                        throw new Exception(e.Message);
                    }
                }
            }
            return returnValue;
        }

        /// <summary>
        /// 執行SQL Command，回傳第一個資料值(Insert、Update、Delete, null代表失敗)，帶參數
        /// 以上是錯的，其實不會有傳回值 <br />
        /// </summary>
        /// <param name="connStr"></param>
        /// <param name="sql">SQL語法字串</param>
        /// <param name="sp">Parameter</param>
        /// <returns>回傳影響筆數</returns>
        public static object ExecuteSqlScalar(string connStr, string sql, SqlParameter sp)
        {
            SqlParameter[] parameters = new SqlParameter[1];
            parameters[0] = sp;
            return ExecuteSqlScalar(connStr, sql, parameters);
        }

        /// <summary>
        /// 執行SQL Command，回傳第一個資料值(Insert、Update、Delete, null代表失敗)，帶參數<br />
        /// 以上是錯的，其實不會有傳回值
        /// </summary>
        /// <param name="connStr"></param>
        /// <param name="sql">SQL語法字串</param>
        /// <param name="sp">Parammeters</param>
        /// <returns>回傳影響筆數</returns>
        public static object ExecuteSqlScalar(string connStr, string sql, SqlParameter[] sp)
        {
            object returnValue = null;

            using (SqlConnection conn = new SqlConnection(connStr))
            {
                using (SqlCommand sqlCommand = new SqlCommand(sql, conn))
                {
                    if (sp != null)
                    {
                        foreach (SqlParameter param in sp)
                        {
                            sqlCommand.Parameters.Add(param);
                        }
                    }

                    try
                    {
                        sqlCommand.Connection.Open();
                        returnValue = sqlCommand.ExecuteScalar();
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex.StackTrace + "\n" + ex);
                        return returnValue;
                    }
                    finally
                    {
                        sqlCommand.Parameters.Clear();
                    }
                }
            }
            return returnValue;
        }

        /// <summary>
        ///     執行SQL Command，回傳第一個資料值(Insert、Update、Delete, null代表失敗)
        /// </summary>
        /// <param name="connStr"></param>
        /// <param name="sql">SQL語法字串</param>
        /// <returns>回傳影響筆數</returns>
        public static int ExecuteSqlNonQuery(string connStr, string sql)
        {
            int returnValue;

            using (SqlConnection conn = new SqlConnection(connStr))
            {
                using (SqlCommand sqlCommand = new SqlCommand(sql, conn))
                {
                    try
                    {
                        sqlCommand.Connection.Open();
                        returnValue = sqlCommand.ExecuteNonQuery();
                    }
                    catch (Exception e)
                    {
                        throw new Exception(e.Message);
                    }
                }
            }
            return returnValue;
        }

        /// <summary>
        /// 執行SQL Command，回傳受影響的列數
        /// </summary>
        /// <param name="connStr"></param>
        /// <param name="sql">SQL語法字串</param>
        /// <param name="sp">Parameter</param>
        /// <returns>回傳影響筆數</returns>
        public static int ExecuteSqlNonQuery(string connStr, string sql, SqlParameter sp)
        {
            SqlParameter[] parameters = new SqlParameter[1];
            parameters[0] = sp;
            return ExecuteSqlNonQuery(connStr, sql, parameters);
        }

        /// <summary>
        /// 執行SQL Command，回傳受影響的列數<br />
        /// </summary>
        /// <param name="connStr"></param>
        /// <param name="sql">SQL語法字串</param>
        /// <param name="sp">Parammeters</param>
        /// <returns>回傳影響筆數</returns>
        public static int ExecuteSqlNonQuery(string connStr, string sql, SqlParameter[] sp)
        {
            int returnValue = -1;

            using (SqlConnection conn = new SqlConnection(connStr))
            {
                using (SqlCommand sqlCommand = new SqlCommand(sql, conn))
                {
                    if (sp != null)
                    {
                        foreach (SqlParameter param in sp)
                        {
                            sqlCommand.Parameters.Add(param);
                        }
                    }

                    try
                    {
                        sqlCommand.Connection.Open();
                        returnValue = sqlCommand.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex.StackTrace + "\n" + ex);
                        return returnValue;
                    }
                    finally
                    {
                        sqlCommand.Parameters.Clear();
                    }
                }
            }
            return returnValue;
        }
    }
}
