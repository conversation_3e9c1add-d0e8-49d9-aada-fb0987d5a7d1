﻿using System.Data;

namespace WorkdayCalendar
{
	/// <summary>
	///     員工介面的摘要描述。
	///     定義員工相關的基本操作方法，包括使用者驗證、部門資訊取得等功能。
	/// </summary>
	public interface IEmployee	{
		// string getUserID(string username); // 已註解的方法：根據使用者名稱取得使用者ID

		/// <summary>
		///     取得使用者名稱。
		///     用於獲取當前員工的使用者名稱。
		/// </summary>
		/// <returns>回傳使用者名稱字串</returns>
		string GetUserName();
		/// <summary>
		///     判斷目前實例是否為員工。
		///     用於驗證該使用者是否具有員工身分。
		/// </summary>
		/// <returns>
		///     如果是員工則回傳 <c>true</c>；否則回傳 <c>false</c>。
		/// </returns>
		bool IsEmployee();
		/// <summary>
		///     驗證指定的使用者ID是否有效。
		///     檢查傳入的使用者ID是否存在於系統中且為有效狀態。
		/// </summary>
		/// <param name="userId">要驗證的使用者ID</param>
		/// <returns>
		///     如果使用者ID有效則回傳 <c>true</c>；否則回傳 <c>false</c>。
		/// </returns>
		bool IsValidUserid(string userId);
		/// <summary>
		///     取得部門識別碼。
		///     獲取該員工所屬部門的唯一識別碼。
		/// </summary>
		/// <returns>回傳部門ID字串</returns>
		string GetDepartmentId();
		/// <summary>
		///     取得部門名稱。
		///     獲取該員工所屬部門的完整名稱。
		/// </summary>
		/// <returns>回傳部門名稱字串</returns>
		string GetDepartmentName();
		/// <summary>
		///     取得網域使用者清單。
		///     從Active Directory或其他網域服務中獲取所有使用者的資料表。
		/// </summary>
		/// <returns>包含網域使用者資訊的DataTable物件</returns>
		DataTable GetDomainUsers();
	}
	/// <summary>
	/// 員工角色類別。
	/// 用於表示員工在系統中的角色資訊，包含角色ID和角色名稱。
	/// </summary>
	public class EmployeeRole
	{
		/// <summary>
		/// 角色識別碼。
		/// 用於唯一標識該角色的ID。
		/// </summary>
		public string RoleId { get; set; }

		/// <summary>
		/// 角色名稱。
		/// 該角色的顯示名稱或描述。
		/// </summary>
		public string RoleName { get; set; }

		/// <summary>
		/// EmployeeRole類別的建構函式。
		/// 初始化新的員工角色實例，設定角色ID和角色名稱。
		/// </summary>
		/// <param name="roleId">角色識別碼</param>
		/// <param name="roleName">角色名稱</param>
		public EmployeeRole(string roleId, string roleName)
		{
			// 設定角色識別碼
			RoleId = roleId;
			// 設定角色名稱
			RoleName = roleName;
		}
	}
}