﻿using System.Data;

namespace WorkdayCalendar
{
    /// <summary>
    ///     中興社員工類別，實作 IEmployee 介面。
    ///     用於處理中興社員工相關的資料庫操作和身分驗證功能。
    /// </summary>
    public class SinotechEmployee : IEmployee
    {
        /// <summary>
        /// 資料庫連線字串。
        /// 用於連接到員工資料庫進行查詢操作。
        /// </summary>
        private string ConnectionString;

        /// <summary>
        /// 員工所屬部門名稱。
        /// 儲存從資料庫查詢到的部門完整名稱。
        /// </summary>
        private string _department;

        /// <summary>
        /// 員工所屬部門識別碼。
        /// 儲存從資料庫查詢到的部門唯一識別碼。
        /// </summary>
        private string _departmentId;

        /// <summary>
        /// 員工有效性標記。
        /// 表示該員工ID是否在資料庫中存在且為有效狀態。
        /// </summary>
        private bool _isValid;

        /// <summary>
        /// 員工使用者識別碼。
        /// 儲存員工的唯一識別碼（員工編號）。
        /// </summary>
        private string _userid;

        /// <summary>
        /// 員工使用者名稱。
        /// 儲存從資料庫查詢到的員工中文姓名。
        /// </summary>
        private string _username;

        /// <summary>
        /// 初始化 SinotechEmployee 類別的新執行個體。
        /// 根據提供的使用者ID和資料庫連線字串，從資料庫查詢員工資訊並初始化相關屬性。
        /// </summary>
        /// <param name="userid">員工使用者識別碼（員工編號）</param>
        /// <param name="connectionString">資料庫連線字串</param>
        public SinotechEmployee(string userid, string connectionString)
        {
            // 設定資料庫連線字串
            ConnectionString = connectionString;
            // 設定使用者ID
            _userid = userid;

            // 建立SQL查詢語句，聯合查詢員工資料表和部門資料表
            // 透過員工編號查詢員工的基本資訊，包含姓名、部門編號和部門名稱
            string strsql =
                "SELECT EmpData.EMPNO AS userid, EmpData.CNAME AS username, EmpData.DEPTNO AS deptno, DeptData.DeptName AS deptname FROM EmpData INNER JOIN DeptData ON EmpData.DEPTNO = DeptData.DeptNo WHERE ( EmpData.EMPNO='" +
                userid + "' )";

            // 執行資料庫查詢，取得員工資料
            DataTable dtb = SqlUtil.GetDataTable(ConnectionString, strsql);

            // 檢查查詢結果是否為空
            if (dtb.Rows.Count == 0)
            {
                // 如果查詢結果為空，表示該員工ID不存在或無效
                _isValid = false;
                _username = null;
                _department = null;
                _departmentId = null;
            }
            else
            {
                // 如果查詢到資料，設定員工為有效並填入相關資訊
                _isValid = true;
                // 從查詢結果第一列取得員工姓名（索引1）
                _username = dtb.Rows[0].ItemArray.GetValue(1).ToString();
                // 從查詢結果第一列取得部門編號（索引2）
                _departmentId = dtb.Rows[0].ItemArray.GetValue(2).ToString();
                // 從查詢結果第一列取得部門名稱（索引3）
                _department = dtb.Rows[0].ItemArray.GetValue(3).ToString();
            }
        }

        /// <summary>
        /// 設定資料庫連線字串。
        /// 用於動態更新或重新設定資料庫連線參數。
        /// </summary>
        /// <param name="connectionString">新的資料庫連線字串</param>
        public void SetConnectionString(string connectionString)
        {
            // 更新內部儲存的資料庫連線字串
            ConnectionString = connectionString;

        }

        /// <summary>
        /// 取得使用者識別碼。
        /// 回傳當前員工實例的使用者ID（員工編號）。
        /// </summary>
        /// <value>使用者識別碼字串</value>
        public string UserId
        {
            // 回傳私有變數 _userid 的值
            get { return _userid; }
        }

        /// <summary>
        ///     取得使用者名稱。
        ///     實作 IEmployee 介面的方法，回傳當前員工的中文姓名。
        /// </summary>
        /// <returns>員工的中文姓名字串，如果員工不存在則回傳 null</returns>
        public string GetUserName()
        {
            // 回傳從資料庫查詢到的員工姓名
            return _username;
        }

        /// <summary>
        ///     判斷目前實例是否為有效員工。
        ///     實作 IEmployee 介面的方法，檢查該使用者ID是否在資料庫中存在。
        /// </summary>
        /// <returns>
        ///     如果是有效員工則回傳 <c>true</c>；否則回傳 <c>false</c>。
        /// </returns>
        public bool IsEmployee()
        {
            // 回傳在建構函式中設定的有效性標記
            return _isValid;
        }

        /// <summary>
        ///     驗證指定的使用者ID是否有效。
        ///     實作 IEmployee 介面的方法，透過資料庫查詢檢查使用者ID是否存在於員工資料表中。
        /// </summary>
        /// <param name="userId">要驗證的使用者識別碼</param>
        /// <returns>
        ///     如果使用者ID有效（存在於資料庫中）則回傳 <c>true</c>；否則回傳 <c>false</c>。
        /// </returns>
        public bool IsValidUserid(string userId)
        {
            // 建立SQL查詢語句，查詢指定的員工編號是否存在
            string strsql = "select EMPNO  from EmpData where EMPNO='" + userId + "'";

            // 執行資料庫查詢
            DataSet da1 = SqlUtil.GetFields(ConnectionString, strsql);
            // Console.WriteLine(strsql); // 除錯用：印出SQL語句

            // 取得查詢結果資料表
            DataTable dtb = da1.Tables["Result"];

            // 檢查查詢結果是否為空
            if (dtb.Rows.Count == 0)
            {
                // 如果查詢結果為空，表示該使用者ID不存在
                return false;
            }
            // 如果查詢到資料，表示該使用者ID存在且有效
            return true;
        }

        /// <summary>
        /// 取得部門識別碼。
        /// 實作 IEmployee 介面的方法，回傳當前員工所屬部門的唯一識別碼。
        /// </summary>
        /// <returns>部門識別碼字串，如果員工不存在則回傳 null</returns>
        public string GetDepartmentId()
        {
            // 回傳從資料庫查詢到的部門編號
            return _departmentId;
        }

        /// <summary>
        /// 取得部門名稱。
        /// 實作 IEmployee 介面的方法，回傳當前員工所屬部門的完整名稱。
        /// </summary>
        /// <returns>部門名稱字串，如果員工不存在則回傳 null</returns>
        public string GetDepartmentName()
        {
            // 回傳從資料庫查詢到的部門名稱
            return _department;
        }

        /// <summary>
        /// 取得網域使用者清單。
        /// 實作 IEmployee 介面的方法，查詢所有狀態為啟用（STATUS = 0）的員工資料。
        /// 包含員工編號、姓名、部門編號和部門名稱等資訊。
        /// </summary>
        /// <returns>
        ///     包含所有啟用員工資訊的 DataTable 物件，如果查詢結果為空則回傳 null。
        ///     資料表欄位包括：userid（員工編號）、username（員工姓名）、DEPTNO（部門編號）、DeptName（部門名稱）。
        /// </returns>
        public DataTable GetDomainUsers()
        {
            // 建立SQL查詢語句，聯合查詢員工資料表和部門資料表
            // 只查詢狀態為啟用（STATUS = 0）的員工資料
            string strsql = "SELECT EmpData.EMPNO AS userid, EmpData.CNAME AS username, " +
                     " EmpData.DEPTNO, DeptData.DeptName FROM EmpData INNER JOIN " +
                     " DeptData ON EmpData.DEPTNO = DeptData.DeptNo " +
                     " WHERE (EmpData.STATUS = 0);";

            // 執行資料庫查詢
            DataSet da1 = SqlUtil.GetFields(ConnectionString, strsql);
            // Console.WriteLine(strsql); // 除錯用：印出SQL語句

            // 取得查詢結果資料表
            DataTable dtb = da1.Tables["Result"];

            // 檢查查詢結果是否為空
            if (dtb.Rows.Count == 0)
            {
                // 如果查詢結果為空，回傳 null
                return null;
            }
            // 回傳包含員工資料的資料表
            return dtb;
        }
    }
}
