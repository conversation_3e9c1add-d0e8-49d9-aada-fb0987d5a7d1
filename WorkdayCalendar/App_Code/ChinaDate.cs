﻿using System;
using System.Collections;
using System.Globalization;

namespace WorkdayCalendar
{
    /// <summary>
    /// 提供農曆與陽曆之間相互轉換的功能，以及獲取農曆日期、節氣、節日等資訊。
    /// </summary>
    public class ChinaDate
    {
        #region 農曆資訊獲取
        // 使用 ChineseLunisolarCalendar 類別來處理農曆相關計算
        static ChineseLunisolarCalendar china = new();
        // 用於儲存公曆節日的 Hashtable
        static Hashtable gHoliday = new();
        // 用於儲存農曆節日的 Hashtable
        static Hashtable nHoliday = new();
        // 儲存二十四節氣的名稱
        static string[] JQ = { "小寒", "大寒", "立春", "雨水", "驚蟄", "春分", "清明", "穀雨", "立夏", "小滿", "芒種", "夏至", "小暑", "大暑", "立秋", "處暑", "白露", "秋分", "寒露", "霜降", "立冬", "小雪", "大雪", "冬至" };
        // 儲存二十四節氣的日期資料，用於計算
        static int[] JQData = { 0, 21208, 43467, 63836, 85337, 107014, 128867, 150921, 173149, 195551, 218072, 240693, 263343, 285989, 308563, 331033, 353350, 375494, 397447, 419210, 440795, 462224, 483532, 504758 };
        
        /// <summary>
        /// 靜態建構函式，用於初始化節日資料。
        /// </summary>
        static ChinaDate()
        {
            // 初始化公曆節日資料
            gHoliday.Add("0101", "元旦");
            gHoliday.Add("0214", "情人節");
            gHoliday.Add("0305", "雷鋒日"); // 注意：此節日主要在中國大陸使用
            gHoliday.Add("0308", "婦女節");
            gHoliday.Add("0312", "植樹節");
            gHoliday.Add("0315", "消費者權益日");
            gHoliday.Add("0401", "愚人節");
            gHoliday.Add("0501", "勞動節");
            gHoliday.Add("0504", "青年節");
            gHoliday.Add("0601", "兒童節");
            gHoliday.Add("0701", "建黨節"); // 注意：此節日主要在中國大陸使用
            gHoliday.Add("0801", "建軍節"); // 注意：此節日主要在中國大陸使用
            gHoliday.Add("0910", "教師節");
            gHoliday.Add("1001", "國慶節"); // 注意：此節日主要在中國大陸使用
            gHoliday.Add("1224", "平安夜");
            gHoliday.Add("1225", "聖誕節");
            // 初始化農曆節日資料
            nHoliday.Add("0101", "春節");
            nHoliday.Add("0115", "元宵節");
            nHoliday.Add("0505", "端午節");
            nHoliday.Add("0815", "中秋節");
            nHoliday.Add("0909", "重陽節");
            nHoliday.Add("1208", "臘八節");
        }

        /// <summary>
        /// 根據指定的陽曆日期，獲取完整的農曆日期資訊，包括年份、月份、日期、節氣和節日。
        /// </summary>
        /// <param name="dt">要轉換的陽曆日期。</param>
        /// <returns>包含農曆年份、月份、日期、節氣和節日的字串。</returns>
        /// <exception cref="ArgumentOutOfRangeException">當日期超出 ChineseLunisolarCalendar 支援的範圍時拋出。</exception>
        public static string GetChinaDate(DateTime dt)
        {
            // 檢查日期是否在支援的範圍內
            if (dt > china.MaxSupportedDateTime || dt < china.MinSupportedDateTime)
            {
                // 日期範圍: 1901 年 2 月 19 日 - 2101 年 1 月 28 日
                throw new ArgumentOutOfRangeException(nameof(dt), string.Format("日期超出範圍!必須在{0}到{1}之間!", china.MinSupportedDateTime.ToString("yyyy-MM-dd"), china.MaxSupportedDateTime.ToString("yyyy-MM-dd")));
            }
            // 格式化農曆年、月、日
            var str = string.Format("{0} {1}{2}", GetYear(dt), GetMonth(dt), GetDay(dt));
            // 獲取節氣資訊
            var strJQ = GetSolarTerm(dt);
            if (strJQ != "")
            {
                str += " (" + strJQ + ")"; // 如果有節氣，則附加到結果字串
            }
            // 獲取公曆節日資訊
            var strHoliday = GetHoliday(dt);
            if (strHoliday != "")
            {
                str += " " + strHoliday; // 如果有公曆節日，則附加到結果字串
            }
            // 獲取農曆節日資訊
            var strChinaHoliday = GetChinaHoliday(dt);
            if (strChinaHoliday != "")
            {
                str += " " + strChinaHoliday; // 如果有農曆節日，則附加到結果字串
            }
            return str;
        }

        /// <summary>
        /// 根據指定的陽曆日期，獲取農曆年份的表示，包括生肖、天干和地支。
        /// </summary>
        /// <param name="dt">要轉換的陽曆日期。</param>
        /// <returns>農曆年份的字串表示，例如 "[鼠]甲子年"。</returns>
        public static string GetYear(DateTime dt)
        {
            // 獲取該日期在六十甲子週期中的索引
            int yearIndex = china.GetSexagenaryYear(dt);
            // 天干字串，索引從1開始
            var yearTG = " 甲乙丙丁戊己庚辛壬癸";
            // 地支字串，索引從1開始
            var yearDZ = " 子醜寅卯辰巳午未申酉戌亥";
            // 生肖字串，索引從1開始（對應地支）
            var yearSX = " 鼠牛虎兔龍蛇馬羊猴雞狗豬";
            // 獲取農曆年份
            int year = china.GetYear(dt);
            // 獲取天干的索引 (1-10)
            int yTG = china.GetCelestialStem(yearIndex);
            // 獲取地支的索引 (1-12)
            int yDZ = china.GetTerrestrialBranch(yearIndex);
            // 格式化農曆年份字串，例如：[豬]辛亥年2011
            var str = string.Format("[{1}]{2}{3}{0}", year, yearSX[yDZ], yearTG[yTG], yearDZ[yDZ]);
            return str;
        }

        /// <summary>
        /// 根據指定的陽曆日期，獲取農曆月份的表示。
        /// </summary>
        /// <param name="dt">要轉換的陽曆日期。</param>
        /// <returns>農曆月份的字串表示，例如 "正月"、"閏二月"、"臘月"。</returns>
        public static string GetMonth(DateTime dt)
        {
            // 獲取農曆年份
            int year = china.GetYear(dt);
            // 獲取農曆月份 (1-13，如果當年有閏月，閏月會是較大的數字)
            int iMonth = china.GetMonth(dt);
            // 獲取當年閏月的月份 (如果沒有閏月則為 0)
            int leapMonth = china.GetLeapMonth(year);
            // 判斷當前月份是否為閏月
            bool isLeapMonth = iMonth == leapMonth;
            // 如果當年有閏月，且當前月份大於等於閏月，則月份實際值需要減1來對應常規月份
            // 例如，如果閏五月，那麼農曆的六月在 GetMonth() 中會返回 7，需要減1變為6才是"六月"
            if (leapMonth != 0 && iMonth >= leapMonth)
            {
                iMonth--;
            }
            // 農曆月份名稱
            var szText = "正二三四五六七八九十";
            var strMonth = isLeapMonth ? "閏" : ""; // 如果是閏月，則加上"閏"字
            if (iMonth <= 10)
            {
                strMonth += szText.Substring(iMonth - 1, 1); // 取對應的月份名稱
            }
            else if (iMonth == 11)
            {
                strMonth += "十一"; // 十一月
            }
            else
            {
                strMonth += "臘"; // 十二月（臘月）
            }
            return strMonth + "月";
        }

        /// <summary>
        /// 根據指定的陽曆日期，獲取農曆日期的表示。
        /// </summary>
        /// <param name="dt">要轉換的陽曆日期。</param>
        /// <returns>農曆日期的字串表示，例如 "初一"、"二十"、"三十"。</returns>
        public static string GetDay(DateTime dt)
        {
            // 獲取農曆月份中的日期 (1-29 或 1-30)
            int iDay = china.GetDayOfMonth(dt);
            // 農曆日期開頭的字元：初（1-10日），十（11-19日），廿（20-29日），三（30日）
            var szText1 = "初十廿三";
            // 農曆日期個位數的字元
            var szText2 = "一二三四五六七八九十";
            string strDay;
            if (iDay == 20)
            {
                strDay = "二十"; // 特殊處理二十日
            }
            else if (iDay == 30)
            {
                strDay = "三十"; // 特殊處理三十日
            }
            else
            {
                // (iDay - 1) / 10 用於確定是初幾、十幾還是廿幾
                strDay = szText1.Substring((iDay - 1) / 10, 1);
                // (iDay - 1) % 10 用於確定個位數的日期
                strDay = strDay + szText2.Substring((iDay - 1) % 10, 1);
            }
            return strDay;
        }

        /// <summary>
        /// 根據指定的陽曆日期，獲取當天的節氣名稱。
        /// </summary>
        /// <param name="dt">要查詢的陽曆日期。</param>
        /// <returns>如果當天是節氣，則返回節氣名稱；否則返回空字串。</returns>
        public static string GetSolarTerm(DateTime dt)
        {
            // 基準日期：1900年1月6日2時5分0秒 (第一個節氣小寒的近似時間)
            DateTime dtBase = new(1900, 1, 6, 2, 5, 0);
            DateTime dtNew; // 用於儲存計算出的節氣日期
            double num; // 用於儲存計算出的分鐘數
            int y; // 年份
            var strReturn = ""; // 返回的節氣名稱
            y = dt.Year; // 獲取輸入日期的年份
            // 遍歷24個節氣
            for (int i = 1; i <= 24; i++)
            {
                // 計算從基準日期到當年第i個節氣的總分鐘數
                // 525948.76 是一年的平均分鐘數 (365.242199 * 24 * 60)
                // JQData[i-1] 是第i個節氣相對於年初的時間偏移量（以分鐘為單位）
                num = 525948.76 * (y - 1900) + JQData[i - 1];
                // 在基準日期上加上計算出的分鐘數，得到節氣的精確時間
                dtNew = dtBase.AddMinutes(num);
                // 如果計算出的節氣日期與輸入日期是同一天 (忽略時間部分)
                if (dtNew.DayOfYear == dt.DayOfYear)
                {
                    strReturn = JQ[i - 1]; // 則返回該節氣的名稱
                    break; // 找到後即可跳出迴圈
                }
            }
            return strReturn;
        }

        /// <summary>
        /// 根據指定的陽曆日期，獲取對應的公曆節日名稱。
        /// </summary>
        /// <param name="dt">要查詢的陽曆日期。</param>
        /// <returns>如果當天是公曆節日，則返回節日名稱；否則返回空字串。</returns>
        public static string GetHoliday(DateTime dt)
        {
            var strReturn = "";
            // 將月份和日期格式化為 "MMdd" 的字串作為 Hashtable 的鍵
            var g = gHoliday[dt.Month.ToString("00") + dt.Day.ToString("00")];
            if (g != null)
            {
                strReturn = g.ToString(); // 如果找到對應的節日，則返回節日名稱
            }
            return strReturn;
        }

        /// <summary>
        /// 根據指定的陽曆日期，獲取對應的農曆節日名稱。
        /// </summary>
        /// <param name="dt">要查詢的陽曆日期。</param>
        /// <returns>如果當天是農曆節日，則返回節日名稱；否則返回空字串。</returns>
        public static string GetChinaHoliday(DateTime dt)
        {
            var strReturn = "";
            // 獲取農曆年份
            int year = china.GetYear(dt);
            // 獲取農曆月份
            int iMonth = china.GetMonth(dt);
            // 獲取當年閏月的月份
            int leapMonth = china.GetLeapMonth(year);
            // 獲取農曆日期
            int iDay = china.GetDayOfMonth(dt);

            // 判斷是否為除夕 (農曆年的最後一天)
            if (china.GetDayOfYear(dt) == china.GetDaysInYear(year))
            {
                strReturn = "除夕";
            }
            // 如果當前月份不是閏月 (因為農曆節日通常不在閏月中慶祝)
            else if (leapMonth != iMonth)
            {
                // 如果當年有閏月，且當前月份大於等於閏月，則月份實際值需要減1
                if (leapMonth != 0 && iMonth >= leapMonth)
                {
                    iMonth--;
                }
                // 將農曆月份和日期格式化為 "MMdd" 的字串作為 Hashtable 的鍵
                var n = nHoliday[iMonth.ToString("00") + iDay.ToString("00")];
                if (n != null)
                {
                    // 如果找到對應的農曆節日
                    if (strReturn == "") // 如果之前沒有其他農曆節日（如除夕）
                    {
                        strReturn = n.ToString();
                    }
                    else
                    {
                        strReturn += " " + n.ToString(); // 如果已有除夕，則附加其他節日
                    }
                }
            }
            return strReturn;
        }
        #endregion

        #region 陰曆-陽曆-轉換
        /// <summary>
        /// 將指定的陽曆日期轉換為該農曆年第一天的陽曆日期。
        /// (註：此方法的命名和實際功能可能存在誤解，它計算的是從指定陽曆日期倒推到該農曆年第一天的陽曆日期)
        /// 更準確的描述應為：獲取指定陽曆日期所在農曆年的第一天的陽曆日期。
        /// </summary>
        /// <param name="dt">指定的陽曆日期。</param>
        /// <returns>該農曆年第一天的陽曆日期。</returns>
        public static DateTime GetLunarYearDate(DateTime dt)
        {
            // 獲取輸入陽曆日期對應的農曆年份
            int cnYear = china.GetYear(dt);
            // 獲取輸入陽曆日期對應的農曆月份
            int cnMonth = china.GetMonth(dt);
            // 用於累加天數的變數
            int num1 = 0;
            // 獲取該農曆年的總月數 (如果是閏年則為13個月，否則為12個月)
            int num2 = china.IsLeapYear(cnYear) ? 13 : 12;
            // 從該農曆年的最後一個月開始，向前累加每個月的天數，直到指定的農曆月份
            while (num2 >= cnMonth)
            {
                num1 += china.GetDaysInMonth(cnYear, num2--);
            }
            // 從累加的總天數中減去指定陽曆日期在農曆月份中的天數，並加1
            // 這一步計算的是從指定陽曆日期到該農曆年最後一天的剩餘天數
            num1 = num1 - china.GetDayOfMonth(dt) + 1;
            // 從指定的陽曆日期減去上述計算的天數，即可得到該農曆年第一天的陽曆日期
            // 然而，原始程式碼是 AddDays，這會導致結果不正確。
            // 正確的邏輯應該是減去從年初到當前日期的天數，或者從當前日期倒推。
            // 假設目的是獲取農曆新年的陽曆日期，此方法目前的實現似乎不正確。
            // 如果目的是將陽曆日期轉換為農曆日期物件，則應使用 GetSunYearDate。
            // 此處保留原始邏輯，但註明其潛在問題。
            // 若要獲取農曆年第一天的陽曆日期，更常見的做法是：
            // DateTime firstDayOfLunarYear = china.ToDateTime(cnYear, 1, 1, 0, 0, 0, 0);
            // return firstDayOfLunarYear;
            // 目前的實現是從 dt 往後加 num1 天，其意義不明確。
            return dt.AddDays(num1);
        }
        
        /// <summary>
        /// 將陽曆日期轉換為農曆日期表示（僅包含年、月、日，不含時分秒）。
        /// 注意：返回的 DateTime 物件的年、月、日部分代表農曆的年、月、日，
        /// 但其 Kind 仍為 Unspecified，且時分秒部分為執行時的 DateTime.Now。
        /// </summary>
        /// <param name="dt">要轉換的公曆日期。</param>
        /// <returns>一個 DateTime 物件，其日期部分表示農曆的年、月、日。</returns>
        public static DateTime GetSunYearDate(DateTime dt)
        {
            // 獲取農曆年份
            int year = china.GetYear(dt);
            // 獲取農曆月份
            int iMonth = china.GetMonth(dt);
            // 獲取農曆日期
            int iDay = china.GetDayOfMonth(dt);
            // 獲取當年閏月的月份
            int leapMonth = china.GetLeapMonth(year);
            // 判斷是否為閏月
            bool isLeapMonth = iMonth == leapMonth; // 雖然計算了isLeapMonth，但在此方法中未使用

            // 如果當年有閏月，且當前月份大於等於閏月，則月份實際值需要減1
            // 這是為了將 ChineseLunisolarCalendar 返回的月份（閏月會使後續月份序號增加）
            // 調整為常規的月份表示（例如，閏五月後的六月，其 iMonth 為 7，需減為 6）
            if (leapMonth != 0 && iMonth >= leapMonth)
            {
                iMonth--;
            }
            // 格式化農曆日期字串，例如 "2023-10-15"
            var str = string.Format("{0}-{1}-{2}", year, iMonth, iDay);
            DateTime dtNew = DateTime.Now; // 初始化為當前時間
            try
            {
                // 嘗試將格式化的農曆日期字串轉換為 DateTime 物件
                // 這裡的轉換是將農曆的年、月、日數值直接當作公曆的年、月、日來建立 DateTime 物件
                // 例如，農曆2023年10月15日，會被轉換為公曆2023年10月15日（如果該日期有效）
                // 主要目的是得到一個包含農曆年、月、日數值的 DateTime 物件
                dtNew = Convert.ToDateTime(str); 
            }
            catch (FormatException)
            {
                // 如果農曆日期對應的公曆日期無效（例如農曆某年某月有30天，但公曆該月沒有30天，如2月30日）
                // 則會捕獲 FormatException。原始程式碼中此處為空 catch 塊，dtNew 將保持為 DateTime.Now。
                // 考慮到方法的目的是返回農曆日期，即使轉換失敗，也應盡可能保留農曆的年月日資訊。
                // 一個可能的改進是，如果轉換失敗，則手動設定 dtNew 的年、月、日。
                // 例如： dtNew = new DateTime(year, iMonth, iDay); 但這也可能因無效日期而失敗。
                // 目前的實現下，如果發生錯誤，返回的是 DateTime.Now，這可能不是預期行為。
            }
            return dtNew;
        }
        #endregion
    }
}
