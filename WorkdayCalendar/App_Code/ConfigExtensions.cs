using Microsoft.Extensions.Configuration;

#nullable enable

// 定義 WorkdayCalendar 命名空間，將相關類別組織在一起
namespace WorkdayCalendar
{
    /// <summary>
    /// ConfigExtensions 靜態類別，提供組態（如連線字串）加解密的延伸方法
    /// </summary>
    public static class ConfigExtensions
    {
        /// <summary>
        /// 取得加密的連線字串並進行解密後回傳。
        /// </summary>
        /// <param name="config">IConfiguration 介面，表示應用程式的組態來源。</param>
        /// <param name="key">組態中連線字串的鍵值。</param>
        /// <returns>解密後的連線字串，如果找不到則回傳空字串。</returns>
        public static string GetSecuredConnectionString(this IConfiguration config, string key)
        {
            // 宣告回傳用變數，預設為空字串
            // 宣告回傳用變數，預設為空字串
            string ret = string.Empty;
            // 從組態取得指定 key 的連線字串（可能為 null 或加密過的字串）
            string? connectionString = config.GetConnectionString(key);
            // 如果連線字串不為空，進行解密
            if (!string.IsNullOrEmpty(connectionString))
            {
                // 執行 RSA 解密，取得原始明文連線字串
                ret = RsaCrypto.RsaDecrypt(connectionString);
                if (string.IsNullOrWhiteSpace(ret))
                {
                    ret = connectionString;
                }
            }
            // 回傳解密後的連線字串（若無則回傳空字串）
            return ret;
        }

        /// <summary>
        /// 取得經過加密的設定值並解密後回傳。
        /// </summary>
        /// <param name="config">IConfiguration 介面，表示應用程式的組態來源。</param>
        /// <param name="key">組態中設定值的鍵值。</param>
        /// <returns>解密後的設定值，如果找不到則回傳空字串。</returns>
        public static string GetSecuredConfigurationString(this IConfiguration config, string key)
        {
            string ret = string.Empty;
            // 直接從組態取得對應 key 的設定值（可能為 null）
            string? configString = config[key];
            // 如果設定值不為空，則進行解密
            if (!string.IsNullOrEmpty(configString))
            {
                // 執行解密，取得原始設定值
                ret = RsaCrypto.RsaDecrypt(configString);
            }
            return ret;
        }
    }
}

