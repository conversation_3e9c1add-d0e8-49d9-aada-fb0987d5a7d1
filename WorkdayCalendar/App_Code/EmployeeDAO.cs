using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections;
using System.Data;

namespace Org.Sinotech
{
	/// <summary>
	///     員工資料存取物件 - 負責處理所有與員工相關的資料庫操作
	/// </summary>
	public class EmployeeDao
	{
		// 記錄最大使用者ID，用於判斷是否有新進員工
		private static int maxUserId;

		// 最後讀取資料的時間，用於控制重新載入的頻率
		private static DateTime LastReadTime { get; set; } = DateTime.Now;

		// 應用程式設定檔案的設定物件，用於取得資料庫連線字串
		private readonly IConfiguration Configuration;

		/// <summary>
		/// 日誌記錄器，用於記錄錯誤和資訊
		/// </summary>
		private readonly ILogger<EmployeeDao> _logger;

		/// <summary>
		/// 建構子 - 初始化員工資料存取物件
		/// </summary>
		/// <param name="configuration">應用程式設定物件，包含資料庫連線字串</param>
		/// <param name="logger">日誌記錄器</param>
		public EmployeeDao(IConfiguration configuration, ILogger<EmployeeDao> logger)
		{
			Configuration = configuration;
			_logger = logger;
		}

		/// <summary>
		/// 更新最大使用者ID - 當發現新的員工編號時使用
		/// </summary>
		/// <param name="userId">要更新的使用者ID</param>
		private static void UpdateMaxUserId(int userId)
		{
			maxUserId = userId;
		}

		/// <summary>
		///     取得所有員工資料 - 回傳按中文姓名排序的員工資料表
		/// </summary>
		/// <returns>包含所有員工資料的DataTable，已按中文姓名排序</returns>
		public DataTable GetEmployee()
		{
			// 取得單例模式的使用者名稱物件
			SingletonUserName sun = new SingletonUserName(Configuration);

			// 取得原始的員工資料表
			DataTable dt = sun.Users;

			// 複製資料表結構以建立新的資料表
			DataTable d = sun.Users.Clone();

			// 設定排序條件為中文姓名
			dt.DefaultView.Sort = "CName";

			// 將排序後的資料逐筆複製到新的資料表中
			foreach (DataRow row in dt.Rows)
			{
				DataRow r = d.NewRow();  // 建立新的資料列
				r.ItemArray = row.ItemArray;  // 複製原始資料列的所有欄位值
				d.Rows.Add(r);  // 將新資料列加入到新資料表
			}
			return d;
		}

		/// <summary>
		/// 取得部門中沒有組別的員工清單
		/// 找出部門中沒有組別的員工
		/// </summary>
		/// <param name="deptid">部門代號</param>
		/// <returns>沒有組別的員工編號清單</returns>
		public ArrayList GetNoTeamUsers(int deptid)
		{
			// 白燕菁提供用 SQL 的作法
			//  SELECT EmpNo, CName, DeptNo, DeptSName, RankNo, JobNo 
			// FROM MIS.dbo.vwCard_Emp 
			// WHERE DeptNo = 14 AND Status = '0' AND EmpNo Not In ( 
			// SELECT EmpNo FROM MIS.dbo.vwDeptTeamEmp ) 

			// 取得指定部門的所有員工資料
			DataTable dt = GetEmployee(deptid);

			// 篩選出TeamId為null的員工（即沒有組別的員工）
			DataRow[] drs = dt.Select("TeamId is null");

			// 建立回傳的員工編號清單
			ArrayList ret = new ArrayList();

			// 將沒有組別的員工編號加入清單
			foreach (DataRow dr in drs)
			{
				ret.Add(dr["EmpNo"]);  // 加入員工編號
			}
			return ret;
		}

		/// <summary>
		/// 取得指定部門的員工資料表
		/// </summary>
		/// <param name="deptid">部門代號</param>
		/// <returns>該部門所有員工的資料表，按中文姓名排序</returns>
		public DataTable GetEmployee(int deptid)
		{
			try
			{
				// 取得單例模式的使用者名稱物件
				SingletonUserName sun = new SingletonUserName(Configuration);

				// 取得所有員工的原始資料表
				DataTable dt = sun.Users;

				// 複製資料表結構
				DataTable d = dt.Clone();

				// 設定原始資料表的排序條件
				dt.DefaultView.Sort = "CName";

				// 篩選出指定部門的員工，並複製到新資料表
				foreach (DataRow row in dt.Select("DeptNo=" + deptid))
				{
					DataRow r = d.NewRow();  // 建立新的資料列
					r.ItemArray = row.ItemArray;  // 複製原始資料列的所有欄位值
					d.Rows.Add(r);  // 將新資料列加入到新資料表
				}

				// 設定新資料表的排序條件為中文姓名
				d.DefaultView.Sort = "CName";
				return d;
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "依部門 ID ({DeptId}) 取得員工資料時發生錯誤", deptid);
				throw; // 重新拋出例外，讓上層處理
			}
		}

		/// <summary>
		///     根據組別ID取得員工資料
		/// </summary>
		/// <param name="teamid">組別ID</param>
		/// <returns>該組別所有員工的資料表，按中文姓名排序</returns>
		public DataTable GetEmployeeByTeamid(int teamid)
		{
			// 取得單例模式的使用者名稱物件
			SingletonUserName sun = new SingletonUserName(Configuration);

			// 取得所有員工的原始資料表
			DataTable dt = sun.Users;

			// 複製資料表結構
			DataTable d = dt.Clone();

			// 設定原始資料表的排序條件
			dt.DefaultView.Sort = "CName";

			// 篩選出指定組別的員工，並複製到新資料表
			foreach (DataRow row in dt.Select("TeamId=" + teamid))
			{
				DataRow r = d.NewRow();  // 建立新的資料列
				r.ItemArray = row.ItemArray;  // 複製原始資料列的所有欄位值
				d.Rows.Add(r);  // 將新資料列加入到新資料表
			}

			// 設定新資料表的排序條件為中文姓名
			d.DefaultView.Sort = "CName";
			return d;
		}

		/// <summary>
		/// 驗證使用者ID是否有效
		/// </summary>
		/// <param name="userID">要驗證的使用者ID</param>
		/// <returns>
		///     如果使用者ID有效則回傳true，否則回傳false
		/// </returns>
		public bool IsValidUserid(string userID)
		{
			// 取得單例模式的使用者名稱物件
			SingletonUserName sun = new SingletonUserName(Configuration);

			// 在員工資料表中搜尋指定的員工編號，若找到一筆資料則表示有效
			return (sun.Users.Select("EmpNo ='" + userID + "'").Length == 1);
		}
#nullable enable
		/// <summary>
		/// 從設定檔中取得特殊員工的姓名
		/// 特殊員工是指在正常員工資料庫中找不到但需要特殊處理的員工
		/// </summary>
		/// <param name="userid">員工編號</param>
		/// <returns>特殊員工的中文姓名，若不是特殊員工則回傳null</returns>
		private string? GetSpecialEmployeeName(string userid)
		{
			try
			{
				// 從 appsettings.json 中的 SpecialEmployees 區段讀取特殊員工資訊
				var specialEmployees = Configuration.GetSection("SpecialEmployees");
				return specialEmployees[userid];
			}
			catch (Exception)
			{
				// 如果讀取配置失敗，回傳 null 以便後續使用正常流程
				return null;
			}
		}

		/// <summary>
		///     根據使用者ID取得員工姓名
		/// </summary>
		/// <param name="userid">員工編號</param>
		/// <returns>員工中文姓名，若找不到則回傳null</returns>
		public string? GetName(string userid)
		{
			// 檢查是否為特殊員工，特殊員工資訊從 appsettings.json 的 SpecialEmployees 區段讀取
			string? specialEmployeeName = GetSpecialEmployeeName(userid);
			if (!string.IsNullOrEmpty(specialEmployeeName))
			{
				return specialEmployeeName;
			}

			// 取得單例模式的使用者名稱物件
			SingletonUserName sun = new SingletonUserName(Configuration);
			string? ret = null;  // 回傳值初始化

			// 嘗試從快取中取得使用者姓名
			object o = sun.UserName(userid);
			if (o != null)
			{
				// 若找到資料，轉換為字串回傳
				ret = o.ToString();
			}
			else
			{
				// 當user == null時的處理邏輯
				// 記錄最大的使用者ID
				// 如果使用者ID大於最大值，記錄下來
				// 若使用者名稱不在雜湊表中，重新載入資料！

				int iUserid;  // 轉換後的整數使用者ID
				bool bt = int.TryParse(userid, out iUserid);  // 嘗試將字串轉為整數

				// 如果轉換成功且使用者ID大於目前最大值，且距離上次讀取已超過2小時
				if (bt && (iUserid > maxUserId && DateTime.Now > LastReadTime.AddHours(2.0d)))
				{
					UpdateMaxUserId(iUserid);  // 更新最大使用者ID
					LastReadTime = DateTime.Now;  // 更新最後讀取時間
					Reload(); // 若不是新進員工，就不必檢查，一定沒有資料

					// 重新載入後再次嘗試取得使用者姓名
					o = sun.UserName(userid);
					if (o != null)
					{
						ret = o.ToString();
					}
				}
			}
			return ret;
		}

		/// <summary>
		/// 根據使用者ID取得職位代號
		/// </summary>
		/// <param name="userid">員工編號</param>
		/// <returns>職位代號，若找不到則回傳null</returns>
		public string? GetJobNo(string userid)
		{
			string? ret = null;  // 回傳值初始化

			// 取得單例模式的使用者名稱物件
			SingletonUserName sun = new SingletonUserName(Configuration);

			// 從快取中取得職位代號
			string? o = (string?)sun.GetJobNo(userid);
			if (!string.IsNullOrEmpty(o))
			{
				ret = o;  // 若找到資料且不為空，設定回傳值
			}
			return ret;
		}

		/// <summary>
		/// 根據使用者ID取得職級代號
		/// </summary>
		/// <param name="userid">員工編號</param>
		/// <returns>職級代號，若找不到則回傳null</returns>
		public string? GetRankNo(string userid)
		{
			string? ret = null;  // 回傳值初始化

			// 取得單例模式的使用者名稱物件
			SingletonUserName sun = new SingletonUserName(Configuration);

			// 從快取中取得職級代號
			string o = (string)sun.GetRankNo(userid);
			if (!string.IsNullOrEmpty(o))
			{
				ret = o;  // 若找到資料且不為空，設定回傳值
			}
			return ret;
		}

		/// <summary>
		/// 根據使用者ID取得職級名稱
		/// </summary>
		/// <param name="userid">員工編號</param>
		/// <returns>職級名稱，若找不到則回傳null</returns>
		public string? GetRankName(string userid)
		{
			string? ret = null;  // 回傳值初始化

			// 取得單例模式的使用者名稱物件
			SingletonUserName sun = new SingletonUserName(Configuration);

			// 從快取中取得職級名稱
			string o = (string)sun.GetRankName(userid);
			if (!string.IsNullOrEmpty(o))
			{
				ret = o;  // 若找到資料且不為空，設定回傳值
			}
			return ret;
		}

		/// <summary>
		/// 根據使用者ID取得職位名稱
		/// </summary>
		/// <param name="userid">員工編號</param>
		/// <returns>職位名稱，若找不到則回傳null</returns>
		public string? GetJobName(string userid)
		{
			string? ret = null;  // 回傳值初始化

			// 取得單例模式的使用者名稱物件
			SingletonUserName sun = new SingletonUserName(Configuration);

			// 從快取中取得職位名稱
			string? o = (string?)sun.GetJobName(userid);
			if (!string.IsNullOrEmpty(o))
			{
				ret = o;  // 若找到資料且不為空，設定回傳值
			}
			return ret;
		}

		/// <summary>
		/// 根據使用者ID取得組別ID
		/// </summary>
		/// <param name="userid">員工編號</param>
		/// <returns>組別ID，若找不到或發生錯誤則回傳0</returns>
		public int GetTeamId(string userid)
		{
			int ret = 0;  // 回傳值初始化為0

			// 取得單例模式的使用者名稱物件
			SingletonUserName sun = new SingletonUserName(Configuration);
			try
			{
				// 嘗試從快取中取得組別ID
				ret = (int)sun.GetTeamId(userid);
			}
			catch (Exception)
			{
				// 若發生例外（例如資料為null或轉換失敗），保持預設值0
			}
			return ret;
		}

		/// <summary>
		/// 根據姓名取得使用者ID
		/// </summary>
		/// <param name="name">員工中文姓名</param>
		/// <returns>員工編號，若找不到則回傳null</returns>
		public string? GetUserID(string name)
		{
			string? ret = null;  // 回傳值初始化

			// 取得單例模式的使用者名稱物件
			SingletonUserName sun = new SingletonUserName(Configuration);

			// 取得所有員工資料表
			DataTable dt = sun.Users;

			// 逐一搜尋每筆員工資料
			foreach (DataRow dr in dt.Rows)
			{
				name = name.Trim();  // 移除輸入姓名的前後空白
				string cname = (string)dr["CName"];  // 取得資料表中的中文姓名

				// 比較姓名是否相符
				if (name.Equals(cname))
				{
					ret = (string)dr["EmpNo"];  // 找到相符的姓名，取得員工編號
					break;  // 找到後即跳出迴圈
				}
			}
			return ret;
		}

		/// <summary>
		/// 根據使用者ID取得電子郵件地址
		/// </summary>
		/// <param name="userid">員工編號</param>
		/// <returns>電子郵件地址</returns>
		public string GetEmail(string userid)
		{
			// 取得單例模式的使用者名稱物件
			SingletonUserName sun = new SingletonUserName(Configuration);

			// 從快取中取得電子郵件地址並轉換為字串回傳
			return (string)sun.GetEmail(userid);
		}

		/// <summary>
		/// 根據使用者ID取得部門ID（與GetDeptNo功能相同）
		/// </summary>
		/// <param name="userid">員工編號</param>
		/// <returns>部門ID，若找不到則回傳0</returns>
		public int GetDeptId(string userid)
		{
			// 取得單例模式的使用者名稱物件
			SingletonUserName sun = new SingletonUserName(Configuration);

			// 從快取中取得部門代號字串
			string x = (string)sun.GetDeptNo(userid);
			if (x == null)
			{
				return 0;  // 若取得的值為null，回傳0
			}

			// 將部門代號字串轉換為整數回傳
			return int.Parse(x);
		}

		/// <summary>
		/// 根據使用者ID取得部門代號
		/// </summary>
		/// <param name="userid">員工編號</param>
		/// <returns>部門代號，若找不到則回傳0</returns>
		public int GetDeptNo(string userid)
		{
			int ret = 0;  // 回傳值初始化為0

			// 取得單例模式的使用者名稱物件
			SingletonUserName sun = new SingletonUserName(Configuration);

			// 從快取中取得部門代號
			object obj = sun.GetDeptNo(userid);
			if (obj != null)
			{
				// 若取得的物件不為null，將字串轉換為整數
				ret = int.Parse((string)obj);
			}
			return ret;
		}

		/// <summary>
		/// 根據使用者ID取得部門全名
		/// </summary>
		/// <param name="userid">員工編號</param>
		/// <returns>部門全名</returns>
		public string GetDeptName(string userid)
		{
			// 取得單例模式的使用者名稱物件
			SingletonUserName sun = new SingletonUserName(Configuration);

			// 從快取中取得部門全名並轉換為字串回傳
			return (string)sun.GetDeptName(userid);
		}

		/// <summary>
		/// 根據使用者ID取得部門簡稱
		/// </summary>
		/// <param name="userid">員工編號</param>
		/// <returns>部門簡稱</returns>
		public string GetDeptSName(string userid)
		{
			// 取得單例模式的使用者名稱物件
			SingletonUserName sun = new SingletonUserName(Configuration);

			// 從快取中取得部門簡稱並轉換為字串回傳
			return (string)sun.GetDeptSName(userid);
		}

		/// <summary>
		/// 重新載入員工資料快取
		/// </summary>
		public void Reload()
		{
			// 取得單例模式的使用者名稱物件
			SingletonUserName sun = new SingletonUserName(Configuration);

			// 呼叫重新載入方法，從資料庫重新讀取最新的員工資料
			sun.Reload();
		}
	}
}
