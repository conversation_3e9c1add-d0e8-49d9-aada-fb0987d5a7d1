using System.Collections;
using System.Data;

using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;

using WorkdayCalendar;

namespace Org.Sinotech
{
    /// <summary>
    /// 職務資料存取物件類別
    /// 負責處理職務相關的資料庫操作，包括查詢職務資料、獲取職務名稱等功能
    /// 使用單例模式來管理職務資料的快取
    /// </summary>
    public sealed class JobDAO
    {
        /// <summary>
        /// 組態設定物件，用於讀取應用程式設定值
        /// </summary>
        private readonly IConfiguration Configuration;

        /// <summary>
        /// JobDAO 建構函式
        /// </summary>
        /// <param name="configuration">依賴注入的組態設定物件</param>
        public JobDAO(IConfiguration configuration)
        {
            // 將傳入的組態設定物件指派給私有成員變數
            this.Configuration = configuration;
        }

        /// <summary>
        /// 取得所有職務資料的雜湊表
        /// 透過單例模式取得快取的職務資料
        /// </summary>
        /// <returns>包含職務編號與職務名稱對應關係的雜湊表</returns>
        public static Hashtable GetJobs()
        {
            // 取得單例物件實例
            SingletonJob sdn = SingletonJob.Instance;
            // 回傳職務雜湊表
            return sdn.HashtableJobs;
        }

        /// <summary>
        /// 取得所有職務資料的資料表
        /// 透過單例模式取得快取的職務資料表物件
        /// </summary>
        /// <returns>包含職務資料的 DataTable 物件</returns>
        public static DataTable GetJobsDataTable()
        {
            // 取得單例物件實例
            SingletonJob sdn = SingletonJob.Instance;
            // 回傳職務資料表
            return sdn.Jobs;
        }

        /// <summary>
        /// 根據職務編號取得職務名稱
        /// 查詢快取中的職務資料並回傳對應的職務名稱
        /// </summary>
        /// <param name="jobno">職務編號</param>
        /// <returns>職務名稱字串，如果找不到則回傳 null</returns>
        public static string GetName(string jobno)
        {
            // 取得單例物件實例
            SingletonJob sdn = SingletonJob.Instance;
            // 透過職務編號查詢職務名稱
            object o = sdn.JobName(jobno);
            // 檢查查詢結果是否為空
            if (o != null)
            {
                // 將結果轉換為字串並回傳
                return o.ToString();
            }
            // 如果找不到對應的職務，回傳 null
            return null;
        }

        /// <summary>
        /// 重新載入職務資料
        /// 建立新的單例物件並重新從資料庫載入職務資料
        /// </summary>
        public void Reload()
        {
            // 建立新的單例職務物件，傳入組態設定
            SingletonJob sj = new SingletonJob(Configuration);
            // 執行重新載入動作
            sj.Reload();
        }
    }

    /// <summary>
    /// 職務單例模式類別
    /// 使用單例設計模式來管理職務資料的快取，確保整個應用程式中只有一個職務資料實例
    /// 提供職務資料的載入、查詢及快取功能
    /// </summary>
    public sealed class SingletonJob
    {
        /// <summary>
        /// 靜態職務雜湊表，用於快速查詢職務編號與職務名稱的對應關係
        /// </summary>
        private static Hashtable htJob { get; set; }

        /// <summary>
        /// 靜態職務資料表，儲存完整的職務資料
        /// </summary>
        private static DataTable dt { get; set; }

        /// <summary>
        /// 組態設定物件，用於讀取資料庫連線字串等設定值
        /// </summary>
        private readonly IConfiguration Configuration;

        /// <summary>
        /// SingletonJob 公開建構函式
        /// 接受組態設定物件作為參數
        /// </summary>
        /// <param name="configuration">依賴注入的組態設定物件</param>
        public SingletonJob(IConfiguration configuration)
        {
            // 將傳入的組態設定物件指派給私有成員變數
            this.Configuration = configuration;
        }

        /// <summary>
        /// SingletonJob 私有建構函式
        /// 用於單例模式的內部初始化，自動載入職務資料
        /// </summary>
        private SingletonJob()
        {
            // 執行職務資料載入
            Reload();
        }

        /// <summary>
        /// 取得單例物件實例
        /// 透過巢狀類別實現執行緒安全的單例模式
        /// </summary>
        /// <value>SingletonJob 的唯一實例</value>
        public static SingletonJob Instance
        {
            get { return Nested.instance; }
        }

        /// <summary>
        /// 取得職務資料表
        /// 提供對內部職務 DataTable 的唯讀存取
        /// </summary>
        /// <value>包含職務資料的 DataTable 物件</value>
        public DataTable Jobs
        {
            get { return dt; }
        }

        /// <summary>
        /// 取得職務雜湊表
        /// 提供對內部職務 Hashtable 的唯讀存取，可快速查詢職務編號與名稱的對應關係
        /// </summary>
        /// <value>包含職務編號與名稱對應關係的 Hashtable 物件</value>
        public Hashtable HashtableJobs
        {
            get { return htJob; }
        }

        /// <summary>
        /// 重新載入職務資料
        /// 從資料庫重新讀取職務資料並更新快取
        /// </summary>
        public void Reload()
        {
            // 初始化職務雜湊表
            htJob = new Hashtable();
            // 初始化職務資料表
            dt = new DataTable();

            // 判斷是否為除錯模式，決定使用的資料庫連線字串
            //#if DEBUG
            //            string strConnMIS = Components.GetConnectionString("MIS", @"server=localhost;uid=step;password=nextstep;database=MIS;pooling=true;");
            //#else
            // 從組態設定中取得 MIS 資料庫連線字串
            string strConnMIS = Configuration.GetSecuredConnectionString("MIS");
            //#endif

            // 定義 SQL 查詢語句：取得職務編號大於2個字元的職務資料
            const string strSql = @"SELECT RankNo, JOBNAME FROM dbo.RankName WHERE LEN(JOBNAME) > 2";

            // 使用 using 確保 SqlDataAdapter 會被正確釋放
            using (SqlDataAdapter da = new SqlDataAdapter(strSql, strConnMIS))
            {
                // 執行查詢並將結果填入資料表
                da.Fill(dt);
            }

            // 遍歷資料表中的每一列，建立職務編號與職務名稱的對應關係
            foreach (DataRow row in dt.Rows)
            {
                // 將職務編號作為鍵值，職務名稱作為值，加入雜湊表中
                htJob.Add(row["RankNo"].ToString(), row["JOBNAME"].ToString());
            }
        }

        /// <summary>
        /// 根據職務編號取得職務名稱
        /// 在職務雜湊表中查詢指定職務編號對應的職務名稱
        /// </summary>
        /// <param name="jobno">職務編號</param>
        /// <returns>職務名稱物件，如果找不到則回傳 null</returns>
        public object JobName(string jobno)
        {
            // 透過職務編號在雜湊表中查詢對應的職務名稱
            return htJob[jobno];
        }

        #region Nested type: Nested

        /// <summary>
        /// 巢狀類別，用於實現執行緒安全的單例模式
        /// 利用 .NET CLR 的類別載入機制確保單例物件的執行緒安全性
        /// </summary>
        private class Nested
        {
            /// <summary>
            /// 靜態唯讀的 SingletonJob 實例
            /// 由 CLR 保證在多執行緒環境下的安全初始化
            /// </summary>
            internal static readonly SingletonJob instance = new SingletonJob();
        }

        #endregion
    }
}
