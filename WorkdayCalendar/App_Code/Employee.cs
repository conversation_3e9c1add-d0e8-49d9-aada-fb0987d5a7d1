﻿using System.Collections;

namespace Org.Sinotech
{
    /// <summary>
    ///     Team 是一個 Struct，代表組織中的團隊或組別資訊<br />
    ///     採用 Pascal Case 命名慣例符合 C# 編碼標準
    /// </summary>
    public struct Team
    {
        /// <summary>
        ///     Department Number 部門編號
        ///     使用整數型別儲存部門的唯一識別碼
        /// </summary>
        public int DeptId { get; set; }

        /// <summary>
        ///     Department Name 部門名稱
        ///     儲存部門的中文或英文名稱
        /// </summary>
        public string DeptName { get; set; }

        /// <summary>
        ///     Team Id 組別編號
        ///     使用整數型別儲存組別的唯一識別碼
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        ///     Team Name 組別名稱
        ///     儲存組別的中文或英文名稱
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        ///     組別順序
        ///     用於決定組別在清單中的排列順序
        /// </summary>
        public int Seq { get; set; }

        /// <summary>
        ///     組長員工編號
        ///     儲存擔任該組組長的員工識別碼
        /// </summary>
        public string TeamLeaderId { get; set; }

        /// <summary>
        ///     組長姓名
        ///     儲存組長的中文姓名，便於顯示使用
        /// </summary>
        public string TeamLeaderName { get; set; }

    }

    /// <summary>
    ///     StructEmployee 是一個 Struct，代表員工的基本資訊結構
    ///     用於在系統中傳遞和儲存員工的核心識別資料
    /// </summary>
    public struct StructEmployee
    {
        /// <summary>
        ///     使用者ID（員工編號）
        ///     儲存員工在系統中的唯一識別碼，通常為4位數字字串
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        ///     使用者姓名（員工姓名）
        ///     儲存員工的中文姓名，用於顯示和識別
        /// </summary>
        public string Username { get; set; }
    }
}

namespace Org.Sinotech
{
    /// <summary>
    ///     //Employee 是一個抽象類別<br />
    ///     //使用方法:<br />
    ///     Sinotech.Employee employee =
    ///     new Sinotech.SinotechEmployee(userId);<br />
    ///     userId 可由 Sinotech.WebUtil.getUserID(Request) 取得<br />
    ///     //取得職等編號:<br />
    ///     string rankno = emp.RankNo;<br />
    ///     //取得職等名稱:<br />
    ///     string rank = emp.Rank;<br />
    ///     //取得職稱編號，若為空字串表示沒有預設職稱:<br />
    ///     string jobno = emp.JobNo;<br />
    ///     //取得職稱名稱，若沒有職稱則為null<br />
    ///     string job = emp.Job;<br />
    ///     //取得組別:<br />
    ///     string teamid = emp.TeamID;<br />
    ///     //取得組長員工編號: <br />
    ///     string teamLeaderId = emp.TeamLeaderID;<br />
    ///     //取得組長姓名: <br />
    ///     string teamLeaderName = emp.TeamLeaderName;<br />
    ///     //取得所有的組別<br />
    ///     Sinotech.Team[] teams = emp.getTeams();<br />
    ///     teams[i].Id; //組編號<br />
    ///     teams[i].Name; //組名稱<br />
    ///     teams[i].DeptId; //部門編號<br />
    ///     teams[i].DeptName; //部門名稱<br />
    ///     teams[i].TeamLeaderId; //組長員工編號<br />
    ///     teams[i].TeamLeaderName; //組長名稱<br />
    ///     //取得組長員工編號: <br />
    ///     string teamLeaderId = emp.TeamLeaderID;<br />
    ///     或是 string teamLeaderId = emp.GetTeamLeaderID(int teamId);<br />
    ///     //注意：目前的設計為一人一組，並未考慮一人多組
    /// </summary>
    public abstract class Employee
    {
        ///// <summary>
        ///// 取得所有組名
        ///// </summary>
        //Team[] Teams { get {return getTeams(); } }

        /// <summary>
        ///     部門中文名稱
        /// </summary>
        protected string department;

        /// <summary>
        ///     部門編號
        /// </summary>
        protected int departmentID;

        /// <summary>
        ///     E-mail
        /// </summary>
        protected string email;

        /// <summary>
        ///     是否為員工
        /// </summary>
        protected bool isValid;

        /// <summary>
        ///     職稱(名稱)
        /// </summary>
        protected string job;

        /// <summary>
        ///     職稱編號
        /// </summary>
        protected string jobno;

        /// <summary>
        ///     職等(名稱)
        /// </summary>
        protected string rank;

        /// <summary>
        ///     職等編號
        /// </summary>
        protected string rankno;

        /// <summary>
        ///     組別編號
        /// </summary>
        protected int teamID;

        /// <summary>
        ///     組長員工編號
        /// </summary>
        protected string teamleaderID;

        /// <summary>
        ///     組別名稱
        /// </summary>
        protected string teamName;

        /// <summary>
        /// </summary>
        protected string userid;

        /// <summary>
        ///     員工姓名
        /// </summary>
        protected string username;


        /// <summary>
        ///     Gets or sets the phone extension. 分機號碼
        /// </summary>
        /// <value>
        ///     The phone extension.  分機號碼
        /// </value>
        protected string PhoneExtension { get; set; }

        /// <summary>
        ///     Gets or sets the special line. 專線電話
        /// </summary>
        /// <value>
        ///     The special line. 專線電話
        /// </value>
        protected string SpecialLine { get; set; }

        /// <summary>
        ///     員工編號
        /// </summary>
        /// <value>The user ID.</value>
        public string UserID
        {
            get { return userid; }
            // set { userId = value; }
        }

        /// <summary>
        ///     員工姓名
        /// </summary>
        public string Username
        {
            get { return username; }
        }

        /// <summary>
        ///     員工email
        /// </summary>
        public string Email
        {
            get { return email; }
        }

        /// <summary>
        ///     部門中文名稱
        /// </summary>
        public string Department
        {
            get { return department; }
        }

        /// <summary>
        ///     職稱編號，若為空字串表示沒有預設職稱
        /// </summary>
        /// <value>The job no.</value>
        public string JobNo
        {
            get { return jobno; }
        }

        /// <summary>
        ///     是否為經理
        /// </summary>
        public abstract bool IsManager { get; }

        /// <summary>
        ///     職稱(名稱)，沒有職稱為null
        /// </summary>
        /// <value>The job.</value>
        public string Job
        {
            get { return job; }
        }

        /// <summary>
        ///     職等編號
        /// </summary>
        /// <value>The rank no.</value>
        public string RankNo
        {
            get { return rankno; }
        }

        /// <summary>
        ///     職等(名稱)
        /// </summary>
        /// <value>The rank.</value>
        public string Rank
        {
            get { return rank; }
        }

        /// <summary>
        ///     部門編號
        /// </summary>
        public int DepartmentID
        {
            get { return departmentID; }
        }

        /// <summary>
        ///     組別編號
        /// </summary>
        public int TeamID
        {
            get { return teamID; }
        }

        /// <summary>
        ///     組別名稱
        /// </summary>
        public string TeamName
        {
            get { return teamName; }
        }

        /// <summary>
        ///     組長員工編號
        /// </summary>
        public string TeamLeaderID
        {
            get { return teamleaderID; }
        }


        /// <summary>
        ///     Gets a value indicating whether this user is deputy manager.
        ///     是否為副理
        /// </summary>
        /// <value>
        ///     <c>true</c> if user is deputy manager; otherwise, <c>false</c>.
        /// </value>
        public abstract bool IsDeputyManager { get; }

        /// <summary>
        ///     Gets a value indicating whether this user is deputy manager.
        ///     是否為副理以上
        /// </summary>
        /// <value>
        ///     <c>true</c> if user is deputy manager; otherwise, <c>false</c>.
        /// </value>
        public abstract bool IsAboveDeputyManager { get; }

        /// <summary>
        ///     Gets a value indicating whether this user is a manager.
        ///     是否為經理以上
        /// </summary>
        /// <value>
        ///     <c>true</c> if user is a deputy manager; otherwise, <c>false</c>.
        /// </value>
        public abstract bool IsAboveManager { get; }

        /// <summary>
        /// Gets a value indicating whether this instance is a supervisor. <br />
        /// 是否為治理
        /// </summary>
        /// <value>
        /// 	<c>true</c> if this instance is a supervisor; otherwise, <c>false</c>.
        /// </value>
        public abstract bool IsSupervisor { get; }

        /// <summary>
        ///     Gets the name of the team leader.
        /// </summary>
        /// <value>The name of the team leader.</value>
        public abstract string TeamLeaderName { get; }


        /// <summary>
        ///     Gets the id of the manager. 部門經理員工編號
        /// </summary>
        /// <param name="deptId">部門編號</param>
        /// <returns></returns>
        public abstract string GetManagerId(int deptId);

        /// <summary>
        ///     Gets the id of the manager. 部門經理員工編號
        /// </summary>
        /// <returns></returns>
        public abstract string GetManagerId();

        /// <summary>
        ///     Gets the name of the manager. 部門經理姓名
        /// </summary>
        /// <param name="deptId">The dept id.</param>
        /// <returns></returns>
        public abstract string GetManagerName(int deptId);

        /// <summary>
        ///     Gets the name of the manager. 部門經理姓名
        /// </summary>
        /// <returns></returns>
        public abstract string GetManagerName();

        /// <summary>
        ///     Gets the name of the user. 同employee.UserName屬性
        /// </summary>
        /// <returns></returns>
        public abstract string GetUserName();

        /// <summary>
        ///     Gets the email. 同employee.Email屬性
        /// </summary>
        /// <returns></returns>
        public abstract string GetEmail();

        /// <summary>
        ///     Determines whether this instance is employee.
        ///     是否為員工
        /// </summary>
        /// <returns>
        ///     <c>true</c> if this instance is employee; otherwise, <c>false</c>.
        /// </returns>
        public abstract bool IsEmployee();

        /// <summary>
        ///     Determines whether [is valid userId] [the specified userId].
        ///     是否存在的員工編號
        /// </summary>
        /// <param name="userid">The user id.</param>
        /// <returns>
        ///     <c>true</c> if [is valid userId] [the specified userId]; otherwise, <c>false</c>.
        /// </returns>
        public abstract bool ValidUserid(string userid);

        /// <summary>
        ///     同employee.DepartmentID
        /// </summary>
        /// <returns></returns>
        public abstract int GetDepartmentID();

        /// <summary>
        ///     取得部門中文名稱
        /// </summary>
        /// <returns>部門中文名稱</returns>
        public abstract string GetDepartmentName();

        /// <summary>
        ///     取得所有部門
        /// </summary>
        /// <returns>Hashtable存部門編號及名稱</returns>
        public abstract Hashtable GetDepartments();


        /// <summary>
        ///     取得部門所有員工的userid
        /// </summary>
        /// <param name="departmentid">部門編號</param>
        /// <returns>userId, cname的Hashtable</returns>
        public abstract StructEmployee[] GetDepartmentUsers(int departmentid);

        /// <summary>
        ///     取得某部門中文名稱
        /// </summary>
        /// <param name="departmentid">部門id</param>
        /// <returns>部門中文名稱</returns>
        public abstract string GetDepartmentName(int departmentid);

        /// <summary>
        ///     取得所有的組別<br />
        ///     使用範例: Sinotech.Team[] teams = emp.getTeams();<br />
        ///     teams[i].Id; // 組編號<br />
        ///     teams[i].Name; // 組名稱<br />
        ///     teams[i].DeptId; // 部門編號<br />
        ///     teams[i].DeptName; // 部門名稱<br />
        ///     teams[i].TeamLeaderId; // 組長員工編號<br />
        ///     teams[i].TeamLeaderName; // 組長姓名<br />
        /// </summary>
        /// <returns></returns>
        public abstract Team[] GetTeams();

        /// <summary>
        ///     Gets teams by the department id.
        /// </summary>
        /// <param name="deptId">The department id.</param>
        /// <returns></returns>
        public abstract Team[] GetTeamsByDeptId(int deptId);

        /// <summary>
        ///     取得某一組的所有組員
        /// </summary>
        /// <param name="teamid"></param>
        /// <returns></returns>
        public abstract string[] GetTeamEmps(int teamid);

        /// <summary>
        ///     某一組的組長員工編號
        /// </summary>
        /// <param name="teamid"></param>
        /// <returns></returns>
        public abstract string GetTeamLeaderID(int teamid);
    }
}