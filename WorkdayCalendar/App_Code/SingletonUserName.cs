// 引用 Microsoft SQL Server 資料存取相關的命名空間
// 引用 .NET 系統核心功能命名空間
using System;
// 引用 .NET 集合類別命名空間，主要使用 Hashtable
using System.Collections;
// 引用 .NET 資料處理相關命名空間，主要使用 DataTable 和 DataRow
using System.Data;

using Microsoft.Data.SqlClient;
// 引用 Microsoft 組態設定相關的命名空間，用於讀取應用程式設定
using Microsoft.Extensions.Configuration;

using WorkdayCalendar;

// 定義 Org.Sinotech 命名空間，用於組織相關的業務邏輯類別
namespace Org.Sinotech
{
	/// <summary>
	/// 單例模式的使用者名稱管理類別 - 負責快取和管理所有員工的基本資料
	/// 使用Hashtable快取員工資訊以提升查詢效能，確保整個應用程式只有一個員工資料實例
	/// 採用執行緒安全的雙重檢查鎖定模式實現單例
	/// </summary>
	public sealed class SingletonUserName
	{
		/// <summary>
		/// 員工姓名快取雜湊表
		/// 鍵值：員工編號（字串）
		/// 數值：員工中文姓名（字串）
		/// 用於快速查詢員工編號對應的中文姓名
		/// </summary>
		private static Hashtable ht { get; set; }
		
		/// <summary>
		/// 職位代號快取雜湊表
		/// 鍵值：員工編號（字串）
		/// 數值：職位代號（字串）
		/// 儲存每位員工對應的職位識別碼
		/// </summary>
		private static Hashtable htJobNo { get; set; }
		
		/// <summary>
		/// 部門全名快取雜湊表
		/// 鍵值：員工編號（字串）
		/// 數值：部門完整名稱（字串）
		/// 儲存員工所屬部門的完整名稱
		/// </summary>
		private static Hashtable htDeptName { get; set; }
		
		/// <summary>
		/// 部門簡稱快取雜湊表
		/// 鍵值：員工編號（字串）
		/// 數值：部門簡稱（字串）
		/// 儲存員工所屬部門的簡化名稱，用於顯示空間有限的界面
		/// </summary>
		private static Hashtable htDeptSName { get; set; }
		
		/// <summary>
		/// 部門代號快取雜湊表
		/// 鍵值：員工編號（字串）
		/// 數值：部門代號（字串）
		/// 儲存員工所屬部門的唯一識別代號
		/// </summary>
		private static Hashtable htDeptNo { get; set; }
		
		/// <summary>
		/// 電子郵件快取雜湊表
		/// 鍵值：員工編號（字串）
		/// 數值：電子郵件地址（字串）
		/// 儲存員工的公司電子郵件信箱，可能為空值
		/// </summary>
		private static Hashtable htEmail { get; set; }
		
		/// <summary>
		/// 職級代號快取雜湊表
		/// 鍵值：員工編號（字串）
		/// 數值：職級代號（字串）
		/// 儲存員工的職級等級識別碼，可能為空值
		/// </summary>
		private static Hashtable htRankNo { get; set; }
		
		/// <summary>
		/// 職級名稱快取雜湊表
		/// 鍵值：員工編號（字串）
		/// 數值：職級名稱（字串）
		/// 儲存員工職級的中文名稱，可能為空值
		/// </summary>
		private static Hashtable htRankName { get; set; }
		
		/// <summary>
		/// 組別快取雜湊表
		/// 鍵值：員工編號（字串）
		/// 數值：組別識別碼（物件）
		/// 儲存員工所屬的工作小組識別碼，可能為空值
		/// </summary>
		private static Hashtable htTeam { get; set; }

		/// <summary>
		/// 完整的員工資料表
		/// 包含所有員工的詳細資訊，包括基本資料、部門資訊、職位資訊等
		/// 作為所有快取雜湊表的資料來源
		/// </summary>
		private static DataTable dt { get; set; }
		
		/// <summary>
		/// 單例模式的靜態實例物件
		/// 確保整個應用程式生命週期中只存在一個 SingletonUserName 實例
		/// </summary>
		private static SingletonUserName instance { get; set; }
		
		/// <summary>
		/// 多執行緒同步鎖定物件
		/// 用於雙重檢查鎖定模式，確保在多執行緒環境下的執行緒安全性
		/// 防止多個執行緒同時建立實例或修改快取資料
		/// </summary>
		private static readonly object syncRoot = new object();

		/// <summary>
		/// 應用程式組態設定物件
		/// 透過依賴注入取得，用於讀取資料庫連線字串等應用程式設定值
		/// 主要用於取得 MIS 資料庫的連線字串
		/// </summary>
		private readonly IConfiguration Configuration;

		/// <summary>
		/// SingletonUserName 建構函式
		/// 初始化單例模式的使用者名稱管理物件，接受組態設定參數
		/// 建構完成後立即執行員工資料載入作業
		/// </summary>
		/// <param name="configuration">應用程式組態設定物件，包含資料庫連線字串等設定值</param>
		public SingletonUserName(IConfiguration configuration)
		{
			// 將傳入的組態設定物件指派給實例變數，供後續資料庫操作使用
			this.Configuration = configuration;
			// 立即載入員工資料，初始化所有快取雜湊表
			Reload();
		}

		/// <summary>
		/// 取得完整的員工資料表屬性
		/// 提供對內部員工 DataTable 的唯讀存取，包含所有員工的完整資訊
		/// </summary>
		/// <value>包含所有員工詳細資訊的 DataTable 物件，包括員工編號、姓名、部門、職位等完整資料</value>
		public DataTable Users
		{
			get { return dt; }
		}

		/// <summary>
		/// 取得 SingletonUserName 的唯一實例屬性
		/// 實現執行緒安全的雙重檢查鎖定單例模式
		/// 確保在多執行緒環境下只會建立一個實例
		/// </summary>
		/// <value>SingletonUserName 的唯一實例物件</value>
		public SingletonUserName Instance
		{
			get
			{
				// 第一次檢查：如果實例尚未建立
				if (instance == null)
				{
					// 使用執行緒鎖定，確保只有一個執行緒能進入此區塊
					lock (syncRoot)
					{
						// 第二次檢查：再次確認實例是否尚未建立（雙重檢查）
						if (instance == null)
							// 建立新的實例，傳入組態設定物件
							instance = new SingletonUserName(Configuration);
					}
				}

				// 回傳單例實例
				return instance;
			}
		}

		/// <summary>
		/// 重新載入員工資料方法
		/// 從資料庫重新讀取所有員工資料並更新快取雜湊表
		/// 使用執行緒鎖定確保資料載入過程的執行緒安全性
		/// </summary>
		public void Reload()
		{
			// 使用執行緒鎖定，確保同時只有一個執行緒執行資料重新載入
			lock (syncRoot)
			{
				// 初始化所有快取雜湊表，清除舊有資料
				ht = new Hashtable();           // 員工姓名快取
				dt = new DataTable();           // 員工資料表
				htDeptNo = new Hashtable();     // 部門代號快取
				htDeptName = new Hashtable();   // 部門全名快取
				htDeptSName = new Hashtable();  // 部門簡稱快取
				htEmail = new Hashtable();      // 電子郵件快取
				htJobNo = new Hashtable();      // 職位代號快取
				htRankNo = new Hashtable();     // 職級代號快取
				htRankName = new Hashtable();   // 職級名稱快取
				htTeam = new Hashtable();       // 組別快取

				// 從組態設定中取得 MIS 資料庫連線字串
				string strConnMIS = Configuration.GetSecuredConnectionString("MIS");

				// 定義主要的員工資料查詢 SQL 語句
				// 包含員工基本資料、部門資訊、職位資訊及組別資訊的聯合查詢
				string selectText = "SELECT vwCard_Emp.EmpNo, vwCard_Emp.CName, vwCard_Emp.DeptNo, vwDeptData.DeptName, vwDeptData.DeptSName,  vwCard_Emp.Email, vwCard_Emp.JobNo, vwCard_Emp.RankNo, vwCard_Emp.RankName, vwCard_Emp.JobName, vwDeptTeamEmp.TeamID, vwDeptTeam.TeamCName FROM vwDeptTeam INNER JOIN vwDeptTeamEmp ON vwDeptTeam.TeamID = vwDeptTeamEmp.TeamID RIGHT OUTER JOIN vwCard_Emp ON  vwDeptTeamEmp.EmpNo = vwCard_Emp.EmpNo LEFT OUTER JOIN vwDeptData ON vwCard_Emp.DeptNo = vwDeptData.DeptNo WHERE Status=0 ORDER BY vwCard_Emp.EmpNo;";

				// 使用 using 確保 SqlDataAdapter 會被正確釋放
				using (SqlDataAdapter da = new SqlDataAdapter(selectText, strConnMIS))
				{
					da.Fill(dt);

					// 遍歷查詢結果，建立各種快取雜湊表的對應關係
					foreach (DataRow row in dt.Rows)
					{
						// 建立員工編號與中文姓名的對應關係
						ht.Add(row["EMPNO"].ToString(), row["CNAME"].ToString());
						// 建立員工編號與部門代號的對應關係
						htDeptNo.Add(row["EMPNO"].ToString(), row["DeptNo"].ToString());
						
						// 檢查職位代號是否為空值，避免加入 null 值到雜湊表
						if (row["JobNo"] != DBNull.Value)
						{
							htJobNo.Add(row["EMPNO"].ToString(), row["JobNo"].ToString());
						}
						
						// 建立員工編號與部門全名的對應關係
						htDeptName.Add(row["EMPNO"].ToString(), row["DeptName"].ToString());
						// 建立員工編號與部門簡稱的對應關係
						htDeptSName.Add(row["EMPNO"].ToString(), row["DeptSName"].ToString());
						
						// 檢查電子郵件是否為空值，避免加入 null 值到雜湊表
						if (row["Email"] != DBNull.Value)
						{
							htEmail.Add(row["EMPNO"].ToString(), row["Email"].ToString());
						}
						
						// 檢查職級代號是否為空值，避免加入 null 值到雜湊表
						if (row["RankNo"] != DBNull.Value)
						{
							htRankNo.Add(row["EMPNO"].ToString(), row["RankNo"].ToString());
						}
						
						// 檢查職級名稱是否為空值，避免加入 null 值到雜湊表
						if (row["RankName"] != DBNull.Value)
						{
							htRankName.Add(row["EMPNO"].ToString(), row["RankName"].ToString());
						}
					}
				}

				// 執行第二次查詢：專門取得員工組別資料
				// 由於組別資料結構較為複雜，需要額外的查詢來確保資料完整性
				selectText =
					"SELECT vwCard_Emp.EmpNo, vwCard_Emp.CName, vwCard_Emp.DeptNo, vwDeptData.DeptName, vwDeptData.DeptSName, vwCard_Emp.Email, vwCard_Emp.JobNo, vwCard_Emp.RankNo, vwCard_Emp.RankName, vwCard_Emp.JobName, vwDeptTeamEmp.TeamID FROM vwCard_Emp LEFT OUTER JOIN vwDeptTeamEmp ON vwCard_Emp.EmpNo = vwDeptTeamEmp.EmpNo LEFT OUTER JOIN vwDeptData ON vwCard_Emp.DeptNo = vwDeptData.DeptNo ORDER BY vwCard_Emp.EmpNo;";
				
				// 使用 using 確保第二個 SqlDataAdapter 會被正確釋放
				using (SqlDataAdapter da2 = new SqlDataAdapter(selectText, strConnMIS))
				{
					// 建立臨時資料表來儲存組別查詢結果
					DataTable dt2 = new DataTable();
					da2.Fill(dt2);
					
					// 遍歷組別查詢結果，建立員工編號與組別識別碼的對應關係
					foreach (DataRow row in dt2.Rows)
					{
						htTeam.Add(row["EmpNo"], row["TeamID"]);
					}
				}
			}
		}

		/// <summary>
		/// 根據員工編號取得員工中文姓名
		/// 在員工姓名快取雜湊表中查詢指定員工編號對應的中文姓名
		/// </summary>
		/// <param name="userid">員工編號（字串格式）</param>
		/// <returns>員工中文姓名物件，如果找不到對應員工或發生例外則回傳 null</returns>
		public object UserName(string userid)
		{
			try
			{
				// 在員工姓名快取雜湊表中查詢對應的中文姓名
				return ht[userid];
			}
			catch (Exception)
			{
				// 如果查詢過程發生任何例外（如員工編號不存在），回傳 null
				return null;
			}
		}

		/// <summary>
		/// 根據員工編號取得職位代號
		/// 在職位代號快取雜湊表中查詢指定員工編號對應的職位識別碼
		/// </summary>
		/// <param name="userid">員工編號（字串格式）</param>
		/// <returns>職位代號物件，如果找不到對應職位或發生例外則回傳 null</returns>
		public object GetJobNo(string userid)
		{
			try
			{
				// 在職位代號快取雜湊表中查詢對應的職位代號
				return htJobNo[userid];
			}
			catch (Exception)
			{
				// 如果查詢過程發生任何例外（如員工編號不存在或職位為空），回傳 null
				return null;
			}
		}

		/// <summary>
		/// 根據員工編號取得組別識別碼
		/// 在組別快取雜湊表中查詢指定員工編號對應的工作小組識別碼
		/// </summary>
		/// <param name="userid">員工編號（字串格式）</param>
		/// <returns>組別識別碼物件，如果找不到對應組別或發生例外則回傳 null</returns>
		public object GetTeamId(string userid)
		{
			try
			{
				// 在組別快取雜湊表中查詢對應的組別識別碼
				return htTeam[userid];
			}
			catch (Exception)
			{
				// 如果查詢過程發生任何例外（如員工編號不存在或組別為空），回傳 null
				return null;
			}
		}

		/// <summary>
		/// 根據員工編號取得職級代號
		/// 在職級代號快取雜湊表中查詢指定員工編號對應的職級等級識別碼
		/// </summary>
		/// <param name="userid">員工編號（字串格式）</param>
		/// <returns>職級代號物件，如果找不到對應職級或發生例外則回傳 null</returns>
		public object GetRankNo(string userid)
		{
			try
			{
				// 在職級代號快取雜湊表中查詢對應的職級代號
				return htRankNo[userid];
			}
			catch (Exception)
			{
				// 如果查詢過程發生任何例外（如員工編號不存在或職級為空），回傳 null
				return null;
			}
		}

		/// <summary>
		/// 根據員工編號取得職級名稱
		/// 在職級名稱快取雜湊表中查詢指定員工編號對應的職級中文名稱
		/// </summary>
		/// <param name="userid">員工編號（字串格式）</param>
		/// <returns>職級名稱物件，如果找不到對應職級名稱或發生例外則回傳 null</returns>
		public object GetRankName(string userid)
		{
			try
			{
				// 在職級名稱快取雜湊表中查詢對應的職級名稱
				return htRankName[userid];
			}
			catch (Exception)
			{
				// 如果查詢過程發生任何例外（如員工編號不存在或職級名稱為空），回傳 null
				return null;
			}
		}

		/// <summary>
		/// 根據員工編號取得職位名稱
		/// 先取得員工的職位代號，再透過 JobDAO 查詢對應的職位中文名稱
		/// 結合了本地快取與職位資料存取物件的查詢功能
		/// </summary>
		/// <param name="userid">員工編號（字串格式）</param>
		/// <returns>職位名稱物件，如果找不到對應職位或發生例外則回傳 null</returns>
		public object GetJobName(string userid)
		{
			try
			{
				// 先從職位代號快取中取得該員工的職位代號，再透過 JobDAO 取得職位名稱
				return JobDAO.GetName((string)htJobNo[userid]);
			}
			catch (Exception)
			{
				// 如果查詢過程發生任何例外（如員工編號不存在、職位代號為空或職位名稱查詢失敗），回傳 null
				return null;
			}
		}

		/// <summary>
		/// 根據員工編號取得部門簡稱
		/// 在部門簡稱快取雜湊表中查詢指定員工編號對應的部門簡化名稱
		/// 通常用於顯示空間有限的使用者界面
		/// </summary>
		/// <param name="userid">員工編號（字串格式）</param>
		/// <returns>部門簡稱物件，如果找不到對應部門或發生例外則回傳 null</returns>
		public object GetDeptSName(string userid)
		{
			try
			{
				// 在部門簡稱快取雜湊表中查詢對應的部門簡稱
				return htDeptSName[userid];
			}
			catch (Exception)
			{
				// 如果查詢過程發生任何例外（如員工編號不存在），回傳 null
				return null;
			}
		}

		/// <summary>
		/// 根據員工編號取得部門全名
		/// 在部門全名快取雜湊表中查詢指定員工編號對應的部門完整名稱
		/// 提供最完整的部門名稱資訊
		/// </summary>
		/// <param name="userid">員工編號（字串格式）</param>
		/// <returns>部門全名物件，如果找不到對應部門或發生例外則回傳 null</returns>
		public object GetDeptName(string userid)
		{
			try
			{
				// 在部門全名快取雜湊表中查詢對應的部門全名
				return htDeptName[userid];
			}
			catch (Exception)
			{
				// 如果查詢過程發生任何例外（如員工編號不存在），回傳 null
				return null;
			}
		}

		/// <summary>
		/// 根據員工編號取得部門代號
		/// 在部門代號快取雜湊表中查詢指定員工編號對應的部門唯一識別代號
		/// 用於系統內部的部門識別和資料關聯
		/// </summary>
		/// <param name="userid">員工編號（字串格式）</param>
		/// <returns>部門代號物件，如果找不到對應部門或發生例外則回傳 null</returns>
		public object GetDeptNo(string userid)
		{
			try
			{
				// 在部門代號快取雜湊表中查詢對應的部門代號
				return htDeptNo[userid];
			}
			catch (Exception)
			{
				// 如果查詢過程發生任何例外（如員工編號不存在），回傳 null
				return null;
			}
		}

		/// <summary>
		/// 根據員工編號取得電子郵件地址
		/// 在電子郵件快取雜湊表中查詢指定員工編號對應的公司電子郵件信箱
		/// 注意：此方法不包含例外處理，呼叫者需自行處理可能的例外情況
		/// </summary>
		/// <param name="userid">員工編號（字串格式）</param>
		/// <returns>電子郵件地址物件，如果員工沒有設定電子郵件則可能回傳 null</returns>
		public object GetEmail(string userid)
		{
			// 直接從電子郵件快取雜湊表中查詢對應的電子郵件地址
			// 注意：此方法未包含 try-catch，如果員工編號不存在可能會拋出例外
			return htEmail[userid];
		}
	}
}
