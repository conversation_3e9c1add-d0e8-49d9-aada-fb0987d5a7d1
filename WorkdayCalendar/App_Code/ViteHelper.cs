﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.SpaServices;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Net.NetworkInformation;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace WorkdayCalendar
{
    /// <summary>
    /// Vite 開發伺服器輔助類別，用於整合 ASP.NET Core 與 Vite 前端開發環境
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class ViteHelper
    {
        /// <summary>
        /// 'npm run dev' 命令完成時的訊息字串，用於判斷開發伺服器是否啟動完成
        /// </summary>
        private static string DoneMessage { get; } = "Starting the development server";

        /// <summary>
        /// 設定 Vite 開發伺服器連線
        /// 配置 SPA 建構器以使用 Vite 託管的 Vue 應用程式
        /// 注意：首次執行時會在您的 Vue 應用程式中建立 devcert.pfx 和 vite.config.js
        /// </summary>
        /// <param name="spa">SPA 建構器實例</param>
        /// <param name="port">埠號（可選，預設為 5173）</param>
        public static void UseViteDevelopmentServer(this ISpaBuilder spa, int? port = null)
        {
            // 設定預設託管埠號為 5173
            if (spa.Options.DevServerPort == 0)
            {
                spa.Options.DevServerPort = 5173;
            }

            // 如果有指定埠號，則使用指定的埠號
            if (port.HasValue)
            {
                spa.Options.DevServerPort = port.Value;
            }

            // 檢查是否已設定 SPA 原始碼路徑
            if (string.IsNullOrWhiteSpace(spa.Options.SourcePath))
            {
                throw new ArgumentNullException("ISpaBuilder.Options.SourcePath", "Must specific Spa Client App path");
            }

            // 建立開發伺服器端點 URI
            var devServerEndpoint = new Uri($"https://localhost:{spa.Options.DevServerPort}");

            // 取得相關服務實例
            var loggerFactory = spa.ApplicationBuilder.ApplicationServices.GetService<ILoggerFactory>();
            var webHostEnvironment = spa.ApplicationBuilder.ApplicationServices.GetService<IWebHostEnvironment>();
            var logger = loggerFactory.CreateLogger("Vue");

            // 檢查指定埠號是否已經有服務在執行
            bool IsRunning = IPGlobalProperties.GetIPGlobalProperties()
                .GetActiveTcpListeners()
                .Select(x => x.Port)
                .Contains(spa.Options.DevServerPort);

            // 如果開發伺服器尚未啟動，則進行初始化和啟動程序
            if (!IsRunning)
            {
                // 判斷當前作業系統是否為 Windows
                var isWindows = RuntimeInformation.IsOSPlatform(OSPlatform.Windows);

                // 匯出開發憑證並設定 Vite 配置
                var spaFolder = Path.Combine(webHostEnvironment.ContentRootPath, spa.Options.SourcePath);
                if (!Directory.Exists(spaFolder))
                    throw new DirectoryNotFoundException(spaFolder);

                // 取得 Vite 配置檔案路徑
                var viteConfigPath = GetViteConfigFile(spaFolder);

                // 設定憑證檔案和伺服器選項檔案的路徑
                var tempPfx = Path.Combine(spaFolder, "devcert.pfx");
                var serverOptionFile = Path.Combine(spaFolder, $"serverOption{new FileInfo(viteConfigPath).Extension}");

                // 檢查開發憑證是否存在，如不存在則建立
                if (!File.Exists(serverOptionFile) || !File.Exists(tempPfx))
                {
                    // 產生隨機密碼用於憑證
                    var pfxPassword = Guid.NewGuid().ToString("N");
                    logger.LogInformation($"Exporting dotnet dev cert to {tempPfx} for Vite");
                    logger.LogDebug($"Export password: {pfxPassword}");

                    // 設定憑證匯出命令的啟動參數
                    var certExport = new ProcessStartInfo
                    {
                        FileName = isWindows ? "cmd" : "dotnet",
                        Arguments = $"{(isWindows ? "/c dotnet " : "")}dev-certs https -v -ep {tempPfx} -p {pfxPassword}",
                        RedirectStandardError = true,
                        RedirectStandardInput = true,
                        RedirectStandardOutput = true,
                        UseShellExecute = false,
                    };

                    // 執行憑證匯出程序
                    var exportProcess = Process.Start(certExport);
                    exportProcess.WaitForExit();
                    if (exportProcess.ExitCode == 0)
                        logger.LogInformation(exportProcess.StandardOutput.ReadToEnd());
                    else
                        logger.LogError(exportProcess.StandardError.ReadToEnd());

                    // 建立伺服器選項配置檔案
                    File.WriteAllText(serverOptionFile, BuildServerOption(tempPfx, pfxPassword));
                    logger.LogInformation($"Creating Vite config: {serverOptionFile}");

                    // 將伺服器選項注入到 Vite 配置檔案中
                    InjectionViteConfig(viteConfigPath, serverOptionFile);
                }

                // 檢查 node_modules 資料夾是否存在，若不存在則執行 npm install
                if (!Directory.Exists(Path.Combine(spa.Options.SourcePath, "node_modules")))
                {
                    logger.LogWarning($"node_modules not found , run npm install...");

                    // 安裝 Node.js 模組
                    var ps = Process.Start(new ProcessStartInfo()
                    {
                        FileName = isWindows ? "cmd" : "npm",
                        Arguments = $"{(isWindows ? "/c npm " : "")}install",
                        WorkingDirectory = spa.Options.SourcePath,
                        RedirectStandardError = true,
                        RedirectStandardInput = true,
                        RedirectStandardOutput = true,
                        UseShellExecute = false,
                    });

                    ps.WaitForExit();
                    logger.LogWarning($"npm install done.");
                }

                // 啟動 Vite 開發伺服器
                // 如果有指定埠號，則在命令中加入埠號參數
                var runningPort = port.HasValue ? $" -- --port {port.Value} --debug " : string.Empty;
                var processInfo = new ProcessStartInfo
                {
                    FileName = isWindows ? "cmd" : "npm",
                    Arguments = $"{(isWindows ? "/c npm " : "")}run dev{runningPort}",
                    WorkingDirectory = spa.Options.SourcePath,
                    RedirectStandardError = true,
                    RedirectStandardInput = true,
                    RedirectStandardOutput = true,
                    UseShellExecute = false,
                };

                // 啟動 Vite 開發伺服器程序
                var process = Process.Start(processInfo);
                // 建立任務完成來源，用於等待伺服器啟動完成
                var tcs = new TaskCompletionSource<int>();

                // 在背景任務中監控標準輸出，尋找啟動完成訊息
                _ = Task.Run(() =>
                {
                    try
                    {
                        string line;
                        while ((line = process.StandardOutput.ReadLine()?.Trim()) != null)
                        {
                            if (!String.IsNullOrEmpty(line))
                            {
                                logger.LogInformation(line);
                                // 當檢測到啟動完成訊息時，設定任務完成
                                if (!tcs.Task.IsCompleted && line.Contains(DoneMessage, StringComparison.OrdinalIgnoreCase))
                                {
                                    tcs.SetResult(1);
                                }
                            }
                        }
                    }
                    catch (EndOfStreamException ex)
                    {
                        logger.LogError(ex.ToString());
                        tcs.SetException(new InvalidOperationException("'npm run dev' failed.", ex));
                    }
                });

                // 在背景任務中監控標準錯誤輸出
                _ = Task.Run(() =>
                {
                    try
                    {
                        string line;
                        while ((line = process.StandardError.ReadLine()?.Trim()) != null)
                        {
                            logger.LogError(line);
                        }
                    }
                    catch (EndOfStreamException ex)
                    {
                        logger.LogError(ex.ToString());
                        tcs.SetException(new InvalidOperationException("'npm run dev' failed.", ex));
                    }
                });

                // 等待伺服器啟動完成，如果超時則拋出例外
                if (!tcs.Task.Wait(spa.Options.StartupTimeout))
                {
                    throw new TimeoutException();
                }
            }

            // 設定代理到 SPA 開發伺服器
            spa.UseProxyToSpaDevelopmentServer(devServerEndpoint);
        }

        /// <summary>
        /// 將伺服器選項注入到 Vite 配置檔案中
        /// 此方法會修改 vite.config 檔案，加入 HTTPS 憑證設定
        /// </summary>
        /// <param name="viteConfigPath">Vite 配置檔案路徑</param>
        /// <param name="serverOptionFile">伺服器選項檔案路徑</param>
        private static void InjectionViteConfig(string viteConfigPath, string serverOptionFile)
        {
            // 取得選項檔案資訊，移除副檔名以取得檔案名稱
            var optionFile = new FileInfo(serverOptionFile);
            var serverOption = optionFile.Name[..^optionFile.Extension.Length];

            // 讀取 Vite 配置檔案的所有行
            var data = File.ReadAllLines(viteConfigPath).ToList();

            // 檢查是否已經注入過伺服器選項，避免重複注入
            if (data.Any(x => x.Contains($"./{serverOption}")))
                return;

            // 在檔案開頭加入 import 語句
            data.Insert(0, $"import serverOption from './{serverOption}'");

            // 尋找 export default 所在的行
            var exportDefaultLine = data.FindIndex(x => x.Contains("export default"));
            if (exportDefaultLine == -1)
                return;

            // 在 export default 後面加入伺服器配置
            data.Insert(exportDefaultLine + 1, "  server : serverOption,");

            // 將修改後的內容寫回檔案
            File.WriteAllLines(viteConfigPath, data);
        }

        /// <summary>
        /// 取得 Vite 配置檔案路徑，支援 .ts 和 .js 副檔名
        /// 搜尋指定根目錄下的 vite.config 檔案
        /// </summary>
        /// <param name="rootPath">要搜尋的根目錄路徑</param>
        /// <returns>Vite 配置檔案的完整路徑</returns>
        private static string GetViteConfigFile(string rootPath)
        {
            // 在根目錄中搜尋符合 vite.config 名稱的檔案（不區分大小寫）
            var configFile = Directory.GetFiles(rootPath)
                                      .Where(x =>
                                      {
                                          var file = new FileInfo(x);
                                          // 取得不含副檔名的檔案名稱
                                          var fileName = file.Name[..^file.Extension.Length];
                                          return fileName.Equals("vite.config",
                                                                 StringComparison.OrdinalIgnoreCase);
                                      })
                                      .Single(); // 應該只有一個符合的檔案

            return configFile;
        }

        /// <summary>
        /// 建立 Vite HTTPS 伺服器選項配置內容
        /// 產生包含憑證檔案和密碼的 JavaScript/TypeScript 配置物件
        /// </summary>
        /// <param name="certfile">憑證檔案的完整路徑</param>
        /// <param name="pass">憑證檔案的密碼</param>
        /// <returns>格式化的伺服器選項配置字串</returns>
        private static string BuildServerOption(string certfile, string pass)
        {
            // 使用 StringBuilder 建立配置檔案內容
            var sb = new StringBuilder();
            sb.AppendLine("export default {");
            // 設定 HTTPS 選項，使用 PFX 憑證檔案和對應密碼
            sb.AppendLine($"https: {{ pfx: '{Path.GetFileName(certfile)}', passphrase: '{pass}' }}");
            sb.AppendLine("}");
            sb.AppendLine(); // 加入空行

            return sb.ToString();
        }
    }
}
