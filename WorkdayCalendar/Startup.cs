// 引入必要的命名空間
using System.Diagnostics.CodeAnalysis;                // 程式碼分析相關屬性

using Microsoft.AspNetCore.Authentication.Negotiate;  // Windows 驗證相關功能
using Microsoft.AspNetCore.Builder;                    // 應用程式建置相關功能
using Microsoft.AspNetCore.Hosting;                   // 主機設定相關功能
using Microsoft.AspNetCore.Server.IISIntegration;     // IIS 整合相關功能
using Microsoft.EntityFrameworkCore;                  // Entity Framework Core 相關功能
using Microsoft.Extensions.Configuration;             // 設定檔讀取相關功能
using Microsoft.Extensions.DependencyInjection;        // 相依性注入相關功能
using Microsoft.Extensions.Hosting;                   // 主機生命週期相關功能

using WorkdayCalendar.Data;                          // 資料存取層相關功能

namespace WorkdayCalendar
{
    // 標記此類別排除在程式碼覆蓋率分析之外
    [ExcludeFromCodeCoverage]
    public class Startup  // 啟動類別，用於配置應用程式的服務和請求處理管道
    {
        /// <summary>初始化 <see cref="Startup" /> 類別的新實例。</summary>
        /// <param name="configuration">應用程式的配置設定</param>
        /// <remarks>
        /// 建構函式會注入 IConfiguration 實例，用於讀取應用程式設定
        /// </remarks>
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;  // 儲存配置物件供後續使用
        }

        /// <summary>
        /// 取得應用程式的配置設定
        /// </summary>
        public IConfiguration Configuration { get; }


        /// <summary>設定應用程式的服務。</summary>
        /// <param name="services">服務集合</param>
        /// <remarks>
        /// 此方法由運行時調用，用於將服務添加到依賴注入容器中。
        /// 這裡設定了 MVC 控制器、身份驗證、靜態檔案服務和 Swagger 文檔生成。
        /// </remarks>
        public void ConfigureServices(IServiceCollection services)
        {
            // 添加 MVC 控制器服務
            services.AddControllers();

            // 註冊 Entity Framework DbContext
            // 使用 SQL Server 作為資料庫提供者，連接字串從 appsettings.json 的 "Workday" 設定讀取
            services.AddDbContext<WorkdayDbContext>(options =>
                options.UseSqlServer(Configuration.GetSecuredConnectionString("Workday")));
            
            // 設定 Windows 身份驗證
            services.AddAuthentication(IISDefaults.AuthenticationScheme);
            services.AddAuthentication(NegotiateDefaults.AuthenticationScheme).AddNegotiate();
            
            // 設定 SPA 靜態檔案服務，指定前端應用程式的根目錄
            services.AddSpaStaticFiles(configuration =>
            {
                configuration.RootPath = "ClientApp/dist";
            });

            // 添加 Swagger 文檔生成服務
            services.AddSwaggerGen();

            // 註冊 EmployeeDao 服務
            services.AddScoped<Org.Sinotech.EmployeeDao>();

            // 初始化 SqlUtil 的資料庫逾時設定
            // 從 appsettings.json 的 DatabaseSettings:CommandTimeoutSeconds 讀取設定值
            var timeoutSeconds = Configuration.GetValue<int>("DatabaseSettings:CommandTimeoutSeconds", 36000);
            SqlUtil.SetCommandTimeout(timeoutSeconds);
        }


        /// <summary>配置 HTTP 請求管道。</summary>
        /// <param name="app">應用程式建置器</param>
        /// <param name="env">Web 主機環境</param>
        /// <remarks>
        /// 此方法由運行時調用，用於配置 HTTP 請求處理管道。
        /// 中間件的順序很重要，會影響請求的處理流程。
        /// </remarks>
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            // 根據環境配置不同的錯誤處理
            if (env.IsDevelopment())
            {
                // 開發環境：顯示詳細的錯誤頁面
                app.UseDeveloperExceptionPage();
                // 啟用 Swagger 和 Swagger UI
                app.UseSwagger();
                app.UseSwaggerUI();
            }
            else
            {
                // 生產環境：使用自定義錯誤處理頁面
                app.UseExceptionHandler("/Error");
                // 啟用 HTTP 嚴格傳輸安全協議 (HSTS)
                // 預設值為 30 天，生產環境中可根據需求調整
                // 詳見：https://aka.ms/aspnetcore-hsts
                app.UseHsts();
            }

            // 啟用路由功能
            app.UseRouting();
            
            // 啟用靜態檔案服務
            app.UseSpaStaticFiles();
            
            // 啟用身份驗證和授權
            app.UseAuthentication();
            app.UseAuthorization();

            // 配置端點路由
            app.UseEndpoints(endpoints =>
            {
                // 映射控制器路由
                endpoints.MapControllers();
            });

            // 配置 SPA 服務
            app.UseSpa(spa =>
            {
                // 指定 SPA 應用的源路徑
                spa.Options.SourcePath = "ClientApp";

                // 開發環境下使用 Vite 開發伺服器
                if (env.IsDevelopment())
                {
                    spa.UseViteDevelopmentServer();
                }
            });
        }
    }
}
