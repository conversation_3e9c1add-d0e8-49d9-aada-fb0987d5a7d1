// 引入必要的命名空間
using System;
using System.ComponentModel.DataAnnotations.Schema;

using Microsoft.EntityFrameworkCore;   // 基礎系統功能，包含 DateTime 等資料型別

namespace WorkdayCalendar.Models
{    /// <summary>
     /// 員工工作日模型類別
     /// 定義特定日期的工作時間安排和相關資訊
     /// 包含班別、工作時間、休息時間等詳細設定
     /// </summary>
    [Table("EmpWorkday")] // 指定資料表名稱
    [PrimaryKey(nameof(EmpNo), nameof(WorkDate))] // 設定複合主鍵
    public class EmpWorkday
    {
        [Column("EmpNo", TypeName = "char(4)")]
        /// <summary>
        /// 員工編號
        /// 關聯到 Employee 資料表的主鍵
        /// </summary>
        public string EmpNo { get; set; }

        /// <summary>
        /// 班別編號
        /// 關聯到 WorkShift 資料表的主鍵
        /// </summary>
        public short ShiftId { get; set; }
        
        /// <summary>
        /// 工作日期
        /// 指定此工作安排適用的日期
        /// </summary>
        public DateTime WorkDate { get; set; }
        
        /// <summary>
        /// 日期類型
        /// 定義當日的工作性質 (例如: 正常工作日、假日、特殊日等)
        /// </summary>
        public short DayType { get; set; }
        
        /// <summary>
        /// 到班時間
        /// 當日實際的上班時間點
        /// </summary>
        public DateTime ArrivalTime { get; set; }
        
        /// <summary>
        /// 下班時間
        /// 當日實際的下班時間點
        /// </summary>
        public DateTime DepartureTime { get; set; }
        
        /// <summary>
        /// 彈性到班時間 - 最早可到班時間
        /// 允許比標準到班時間提前到班的最早時間點
        /// </summary>
        public DateTime FlexibleArrivalBefore { get; set; }
        
        /// <summary>
        /// 彈性到班時間 - 最晚可到班時間
        /// 允許比標準到班時間延後到班的最晚時間點
        /// </summary>
        public DateTime FlexibleArrivalAfter { get; set; }
        
        /// <summary>
        /// 彈性下班時間 - 最早可下班時間
        /// 允許比標準下班時間提前下班的最早時間點
        /// </summary>
        public DateTime FlexibleDepartureBefore { get; set; }
        
        /// <summary>
        /// 彈性下班時間 - 最晚可下班時間
        /// 允許比標準下班時間延後下班的最晚時間點
        /// </summary>
        public DateTime FlexibleDepartureAfter { get; set; }
        
        /// <summary>
        /// 午休開始時間
        /// 當日午休時段的開始時間點
        /// </summary>
        public DateTime MiddayBreakStart { get; set; }
        
        /// <summary>
        /// 午休結束時間
        /// 當日午休時段的結束時間點
        /// </summary>
        public DateTime MiddayBreakEnd { get; set; }
        
        /// <summary>
        /// 上午休息開始時間
        /// 當日上午休息時段的開始時間點
        /// </summary>
        public DateTime MorningRestStart { get; set; }
        
        /// <summary>
        /// 上午休息結束時間
        /// 當日上午休息時段的結束時間點
        /// </summary>
        public DateTime MorningRestEnd { get; set; }
        
        /// <summary>
        /// 下午休息開始時間
        /// 當日下午休息時段的開始時間點
        /// </summary>
        public DateTime AfternoonRestStart { get; set; }
        
        /// <summary>
        /// 下午休息結束時間
        /// 當日下午休息時段的結束時間點
        /// </summary>
        public DateTime AfternoonRestEnd { get; set; }
        
        /// <summary>
        /// 工作時數
        /// 當日的總工作時數 (單位: 小時)
        /// 計算公式: (下班時間 - 到班時間) + (午休結束時間 - 午休開始時間)
        /// </summary>
        /// <remarks>
        /// 此屬性可用於計算實際工作時間，排除休息時間
        /// </remarks>
        public double WorkHours { get; set; } // => ((DepartureTime - ArrivalTime) + (MiddayBreakEnd - MiddayBreakStart)).TotalHours;
        
        /// <summary>
        /// 週間日期
        /// 表示當日是星期幾 (1=星期一, 2=星期二, ..., 7=星期日)
        /// </summary>
        public short WeekDay { get; set; }
        
        /// <summary>
        /// 備註說明
        /// 用於記錄當日工作安排的特殊說明或注意事項
        /// </summary>
        public string Comment { get; set; }
    }
}