// 引入必要的命名空間
using Microsoft.EntityFrameworkCore;

namespace WorkdayCalendar.Models
{
    /// <summary>
    /// 假日模型類別
    /// 定義假日資訊，包含日期、類型和名稱
    /// 支援國曆和農曆假日的表示
    /// </summary>
    [PrimaryKey(nameof(Date), nameof(IsLunar))]
    public class Holiday
    {
        /// <summary>
        /// 假日日期
        /// 日期格式依照 IsLunar 屬性決定使用國曆或農曆表示法
        /// </summary>
        /// <remarks>
        /// 國曆格式: YYYY-MM-DD
        /// 農曆格式: 依照傳統農曆表示方式
        /// </remarks>
        public string Date { get; set; }
        
        /// <summary>
        /// 是否為農曆假日
        /// true: 農曆假日 (如春節、中秋節等)
        /// false: 國曆假日 (如元旦、國慶日等)
        /// </summary>
        public bool IsLunar { get; set; }
        
        /// <summary>
        /// 假日名稱
        /// 假日的正式名稱或簡稱 (例如: "春節"、"中秋節"、"國慶日")
        /// </summary>
        public string Name { get; set; }
    }
    
}
