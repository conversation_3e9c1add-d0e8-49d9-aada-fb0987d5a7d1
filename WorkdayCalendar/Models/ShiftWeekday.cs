using System;

using Microsoft.EntityFrameworkCore;   // 基礎系統功能

namespace WorkdayCalendar.Models
{
    /// <summary>
    /// 班別週間工作日模型類別
    /// 定義特定週間日期的班別工作時間安排
    /// </summary>
    [PrimaryKey(nameof(ShiftId))]
    public class ShiftWeekday
    {
        /// <summary>
        /// 班別編號
        /// </summary>
        public int ShiftId { get; set; }
        
        /// <summary>
        /// 班別名稱
        /// </summary>
        public string ShiftName { get; set; }
        
        /// <summary>
        /// 週間日期 (1=星期一, 2=星期二, ..., 7=星期日)
        /// </summary>
        public int WeekDay { get; set; }
        
        /// <summary>
        /// 到班時間 (格式: HH:mm:ss)
        /// </summary>
        public string ArrivalTime { get; set; }
        
        /// <summary>
        /// 下班時間 (格式: HH:mm:ss)
        /// </summary>
        public string DepartureTime { get; set; }
        
        /// <summary>
        /// 彈性到班時間 - 提前時間 (格式: HH:mm:ss)
        /// 允許比標準到班時間提前多久到班
        /// </summary>
        public string FlexibleArrivalBefore { get; set; }
        
        /// <summary>
        /// 彈性到班時間 - 延後時間 (格式: HH:mm:ss)
        /// 允許比標準到班時間延後多久到班
        /// </summary>
        public string FlexibleArrivalAfter { get; set; }
        
        /// <summary>
        /// 彈性下班時間 - 提前時間 (格式: HH:mm:ss)
        /// 允許比標準下班時間提前多久下班
        /// </summary>
        public string FlexibleDepartureBefore { get; set; }
        
        /// <summary>
        /// 彈性下班時間 - 延後時間 (格式: HH:mm:ss)
        /// 允許比標準下班時間延後多久下班
        /// </summary>
        public string FlexibleDepartureAfter { get; set; }
        
        /// <summary>
        /// 午休開始時間 (格式: HH:mm:ss)
        /// </summary>
        public string MiddayBreakStart { get; set; }
        
        /// <summary>
        /// 午休結束時間 (格式: HH:mm:ss)
        /// </summary>
        public string MiddayBreakEnd { get; set; }
        
        /// <summary>
        /// 上午休息開始時間 (格式: HH:mm:ss)
        /// </summary>
        public string MorningRestStart { get; set; }
        
        /// <summary>
        /// 上午休息結束時間 (格式: HH:mm:ss)
        /// </summary>
        public string MorningRestEnd { get; set; }
        
        /// <summary>
        /// 下午休息開始時間 (格式: HH:mm:ss)
        /// </summary>
        public string AfternoonRestStart { get; set; }
        
        /// <summary>
        /// 下午休息結束時間 (格式: HH:mm:ss)
        /// </summary>
        public string AfternoonRestEnd { get; set; }
        
        /// <summary>
        /// 備註說明
        /// </summary>
        public string Comment { get; set; }
    }
    
    /// <summary>
    /// 測試模型類別
    /// 用於開發測試階段的臨時資料結構
    /// </summary>
    /// <remarks>
    /// 此類別目前僅包含基本的班別編號屬性
    /// 其他屬性已被註解，可能在未來版本中重新啟用
    /// </remarks>
    public class TestModel
    {
        /// <summary>
        /// 班別編號
        /// </summary>
        public int ShiftId { get; set; }
        
        /*
        /// <summary>
        /// 班別名稱
        /// </summary>
        public string ShiftName { get; set; }
        
        /// <summary>
        /// 年份
        /// </summary>
        public int Year { get; set; }
        
        /// <summary>
        /// 週間日期
        /// </summary>
        public string WeekDay { get; set; }
        
        /// <summary>
        /// 到班時間
        /// </summary>
        public string ArrivalTime { get; set; }
        
        /// <summary>
        /// 下班時間
        /// </summary>
        public string DepartureTime { get; set; }
        
        /// <summary>
        /// 彈性到班時間 - 提前時間
        /// </summary>
        public string FlexibleArrivalBefore { get; set; }
        
        /// <summary>
        /// 彈性到班時間 - 延後時間
        /// </summary>
        public string FlexibleArrivalAfter { get; set; }
        
        /// <summary>
        /// 午休開始時間
        /// </summary>
        public string MiddayBreakStart { get; set; }
        
        /// <summary>
        /// 午休結束時間
        /// </summary>
        public string MiddayBreakEnd { get; set; }
        
        /// <summary>
        /// 上午休息開始時間
        /// </summary>
        public string MorningRestStart { get; set; }
        
        /// <summary>
        /// 上午休息結束時間
        /// </summary>
        public string MorningRestEnd { get; set; }
        
        /// <summary>
        /// 下午休息開始時間
        /// </summary>
        public string AfternoonRestStart { get; set; }
        
        /// <summary>
        /// 下午休息結束時間
        /// </summary>
        public string AfternoonRestEnd { get; set; }
        
        /// <summary>
        /// 工作時數 (計算屬性，已註解)
        /// 計算公式: (下班時間 - 到班時間 + 午休結束時間 - 午休開始時間).總時數
        /// </summary>
        //public double WorkHours { get; set; } //=> (DepartureTime - ArrivalTime + MiddayBreakEnd - MiddayBreakStart).TotalHours;
        
        /// <summary>
        /// 開始日期 (DateTime 格式)
        /// </summary>
        public DateTime StartDate { get; set; }
        
        /// <summary>
        /// 結束日期 (DateTime 格式)
        /// </summary>
        public DateTime EndDate { get; set; }
        
        /// <summary>
        /// 開始日期 (字串格式，已註解)
        /// </summary>
        //public string StartDate { get; set; }
        
        /// <summary>
        /// 結束日期 (字串格式，已註解)
        /// </summary>
        //public string EndDate { get; set; }
        
        /// <summary>
        /// 備註說明
        /// </summary>
        public string Comment { get; set; }
        */
    }
}