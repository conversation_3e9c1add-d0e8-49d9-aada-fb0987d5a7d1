using System.ComponentModel.DataAnnotations.Schema;

using Microsoft.EntityFrameworkCore;

namespace WorkdayCalendar.Models
{
    /// <summary>
    /// 工作班別模型類別
    /// 定義班別的基本資訊和時間設定，作為工作日曆排班的基礎範本
    /// </summary>
    /// <remarks>
    /// 班別是工作日曆系統的核心概念，每個班別定義了：
    /// - 基本工作時間 (上下班時間)
    /// - 彈性工時範圍
    /// - 休息時間安排
    /// - 適用的工作日
    /// 
    /// 班別可以被多個員工使用，也可以套用到不同的日期
    /// </remarks>
    [PrimaryKey(nameof(ShiftId))]
    public class WorkShift
    {
        /// <summary>
        /// 班別唯一識別碼
        /// 作為班別的主鍵，用於關聯其他相關資料表
        /// </summary>
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public short ShiftId { get; set; }

        /// <summary>
        /// 班別名稱
        /// 班別的顯示名稱，例如: "正常班"、"早班"、"晚班"、"輪班A"
        /// </summary>
        /// <example>
        /// "正常班", "早班", "晚班", "輪班A", "彈性班"
        /// </example>
        public string ShiftName { get; set; }

        /// <summary>
        /// 工作日設定
        /// 以逗號分隔的數字字串，表示一週中的工作日
        /// 1=星期一, 2=星期二, ..., 7=星期日
        /// </summary>
        /// <example>
        /// "1,2,3,4,5" 表示週一到週五工作
        /// "1,2,3,4,5,6" 表示週一到週六工作
        /// </example>
        public string Workdays { get; set; }

        /// <summary>
        /// 休息日設定
        /// 定義每週的休息日安排
        /// </summary>
        /// <remarks>
        /// 此欄位可能用於特殊的休息日計算邏輯
        /// </remarks>
        public int RestDay { get; set; }

        /// <summary>
        /// 例假日設定
        /// 定義固定的例假日安排
        /// </summary>
        /// <remarks>
        /// 此欄位可能用於法定假日或固定假日的處理
        /// </remarks>
        public int FixedDayOff { get; set; }

        /// <summary>
        /// 標準上班時間
        /// 格式: HH:mm (24小時制)
        /// </summary>
        /// <example>
        /// "08:00", "09:00", "07:30"
        /// </example>
        public string ArrivalTime { get; set; }

        /// <summary>
        /// 標準下班時間
        /// 格式: HH:mm (24小時制)
        /// </summary>
        /// <example>
        /// "17:00", "18:00", "16:30"
        /// </example>
        public string DepartureTime { get; set; }

        /// <summary>
        /// 彈性上班時間 - 最早可到班時間
        /// 允許員工提前到班的最早時間點
        /// 格式: HH:mm (24小時制)
        /// </summary>
        /// <example>
        /// 如果標準上班時間是 "08:00"，彈性時間可能是 "07:30"
        /// </example>
        public string FlexibleArrivalBefore { get; set; }

        /// <summary>
        /// 彈性上班時間 - 最晚可到班時間
        /// 允許員工延後到班的最晚時間點
        /// 格式: HH:mm (24小時制)
        /// </summary>
        /// <example>
        /// 如果標準上班時間是 "08:00"，彈性時間可能是 "09:00"
        /// </example>
        public string FlexibleArrivalAfter { get; set; }

        /// <summary>
        /// 彈性下班時間 - 最早可下班時間
        /// 允許員工提前下班的最早時間點
        /// 格式: HH:mm (24小時制)
        /// </summary>
        /// <example>
        /// 如果標準下班時間是 "17:00"，彈性時間可能是 "16:30"
        /// </example>
        public string FlexibleDepartureBefore { get; set; }

        /// <summary>
        /// 彈性下班時間 - 最晚可下班時間
        /// 允許員工延後下班的最晚時間點
        /// 格式: HH:mm (24小時制)
        /// </summary>
        /// <example>
        /// 如果標準下班時間是 "17:00"，彈性時間可能是 "18:00"
        /// </example>
        public string FlexibleDepartureAfter { get; set; }

        /// <summary>
        /// 午休開始時間
        /// 中午休息時段的開始時間
        /// 格式: HH:mm (24小時制)
        /// </summary>
        /// <example>
        /// "12:00", "12:30"
        /// </example>
        public string MiddayBreakStart { get; set; }

        /// <summary>
        /// 午休結束時間
        /// 中午休息時段的結束時間
        /// 格式: HH:mm (24小時制)
        /// </summary>
        /// <example>
        /// "13:00", "13:30"
        /// </example>
        public string MiddayBreakEnd { get; set; }

        /// <summary>
        /// 上午休息開始時間
        /// 上午休息時段的開始時間 (如茶水時間)
        /// 格式: HH:mm (24小時制)
        /// </summary>
        /// <example>
        /// "10:00", "10:15"
        /// </example>
        public string MorningRestStart { get; set; }

        /// <summary>
        /// 上午休息結束時間
        /// 上午休息時段的結束時間
        /// 格式: HH:mm (24小時制)
        /// </summary>
        /// <example>
        /// "10:15", "10:30"
        /// </example>
        public string MorningRestEnd { get; set; }

        /// <summary>
        /// 下午休息開始時間
        /// 下午休息時段的開始時間 (如茶水時間)
        /// 格式: HH:mm (24小時制)
        /// </summary>
        /// <example>
        /// "15:00", "15:15"
        /// </example>
        public string AfternoonRestStart { get; set; }

        /// <summary>
        /// 下午休息結束時間
        /// 下午休息時段的結束時間
        /// 格式: HH:mm (24小時制)
        /// </summary>
        /// <example>
        /// "15:15", "15:30"
        /// </example>
        public string AfternoonRestEnd { get; set; }

        /// <summary>
        /// 備註說明
        /// 用於記錄班別的特殊說明、適用條件或注意事項
        /// </summary>
        /// <example>
        /// "適用於一般行政人員", "需配合專案需求調整", "試行期間班別"
        /// </example>
        public string Comment { get; set; }
    }
}