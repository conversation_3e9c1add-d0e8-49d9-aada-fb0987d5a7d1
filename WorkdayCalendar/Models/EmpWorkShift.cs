using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using Microsoft.EntityFrameworkCore;

namespace WorkdayCalendar.Models
{
    /// <summary>    
    /// 工作班別    
    /// </summary>    
    [Table("EmpWorkShift")]
    [PrimaryKey(nameof(EmpNo), nameof(WorkDate))] // 設定複合主鍵
    public class EmpWorkShift
    {
        /// <summary>    
        /// 員工編號    
        /// 關聯到 Employee 資料表的主鍵    
        /// </summary>    
        public string EmpNo { get; set; }

        /// <summary>    
        /// 班別ID    
        /// </summary>    
        public short ShiftId { get; set; }

        /// <summary>    
        /// 工作日    
        /// </summary>    
        public DateTime WorkDate { get; set; }
    }
}