using System;

namespace WorkdayCalendar.Models
{
    /// <summary>
    /// 工作日參數模型類別
    /// 用於傳遞生成工作日排程所需的參數資訊
    /// 包含年份、班別、起始日期和假日清單
    /// </summary>
    public class WorkdayParam
    {
        /// <summary>
        /// 年份
        /// 指定要生成工作日排程的年份 (例如: 2025)
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// 班別編號
        /// 關聯到 WorkShift 資料表的主鍵，指定使用的班別設定
        /// </summary>
        public int ShiftId { get; set; }

        /// <summary>
        /// 起始日期
        /// 工作日排程的開始日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 假日陣列
        /// 包含該年度所有假日資訊的陣列
        /// 用於在生成工作日排程時排除假日或調整工作安排
        /// </summary>
        public Holiday[] Holidays { get; set; }
    }

}
