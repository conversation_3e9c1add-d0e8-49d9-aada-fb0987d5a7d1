// 引入必要的命名空間
using Microsoft.AspNetCore.Hosting;  // 提供 ASP.NET Core 的 Web 主機功能
using Microsoft.Extensions.Hosting;  // 提供建立主機的擴充方法

namespace WorkdayCalendar
{
    /// <summary>
    /// 應用程式的進入點類別，負責配置和啟動 Web 主機
    /// </summary>
    public static class Program
    {
        /// <summary>
        /// 應用程式的主要進入點
        /// </summary>
        /// <param name="args">命令列參數</param>
        public static void Main(string[] args)
        {
            // 建立並運行 Web 主機
            // 1. 使用 CreateHostBuilder 建立主機構建器
            // 2. 建置主機
            // 3. 啟動主機並開始監聽 HTTP 請求
            CreateHostBuilder(args).Build().Run();
        }

        /// <summary>
        /// 建立並配置 Web 主機建構器
        /// </summary>
        /// <param name="args">命令列參數</param>
        /// <returns>配置好的 IHostBuilder 實例</returns>
        public static IHostBuilder CreateHostBuilder(string[] args) =>
            // 建立預設的主機建構器，包含日誌、配置等預設服務
            Host.CreateDefaultBuilder(args)
                // 配置 Web 主機預設值
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    // 指定啟動類別 Startup 來配置應用程式的服務和請求處理管道
                    webBuilder.UseStartup<Startup>();
                });
    }
}
