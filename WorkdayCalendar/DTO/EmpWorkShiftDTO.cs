// 引入必要的命名空間
using Microsoft.Data.SqlClient;
using System;
using System.Data;
using WorkdayCalendar.Models;

namespace WorkdayCalendar.DTO
{
    /// <summary>
    /// 員工班別資料傳輸物件 (Data Transfer Object)
    /// 負責處理與 EmpWorkShift 相關的資料庫操作
    /// </summary>
    public class EmpWorkShiftDto
    {
        // 資料庫連接字串
        private string ConnectionString { get; set; }

        /// <summary>
        /// 建構函式，初始化 EmpWorkShiftDto 實例
        /// </summary>
        /// <param name="connectionString">資料庫連接字串</param>
        public EmpWorkShiftDto(string connectionString)
        {
            ConnectionString = connectionString;
        }

        /// <summary>
        /// 根據員工 ID 取得班別資料
        /// </summary>
        /// <param name="EmpNo">員工 ID</param>
        /// <returns>包含該員工班別資料的 DataTable</returns>
        public DataTable Get(string EmpNo)
        {
            string sqlstr = @"
                SELECT 
                    e.EmpNo, 
                    ws.*
                FROM 
                    Employee e
                LEFT JOIN 
                    WorkShift ws ON e.ShiftId = ws.ShiftId
                WHERE 
                    e.EmpNo = @EmpNo;
            ";
            SqlParameter parameter = new SqlParameter("@EmpNo", SqlDbType.Char, 4);
            parameter.Value = EmpNo;
            return SqlUtil.GetDataTable(ConnectionString, sqlstr, parameter);
        }

        /// <summary>
        /// 更新員工的班別
        /// </summary>
        /// <param name="userId">執行操作的使用者 ID</param>
        /// <param name="ip">執行操作的 IP 位址</param>
        /// <param name="EmpNo">要更新的員工 ID</param>
        /// <param name="shiftId">新的班別 ID</param>
        /// <returns>更新成功回傳 true，否則回傳 false</returns>
        /// <remarks>只有管理員可以執行此操作</remarks>
        public bool Update(string userId, string ip, string EmpNo, int shiftId)
        {
            bool ret = false;
            if (Utility.IsAdmin(userId, ConnectionString))
            {
                string sqlstr = "UPDATE dbo.Employee SET ShiftId = @ShiftId WHERE EmpNo = @EmpNo";
                SqlParameter[] parameters = {
                    new SqlParameter("@ShiftId", SqlDbType.Int) { Value = shiftId },
                    new SqlParameter("@EmpNo", SqlDbType.Char, 4) { Value = EmpNo }
                };
                int iRet = SqlUtil.ExecuteSqlNonQuery(ConnectionString, sqlstr, parameters);
                if (iRet == 1) //成功
                {
                    ret = true;
                    Utility.EventLog(21, userId, $"更新員工 {EmpNo} 班別為 {shiftId} 成功", ip, ConnectionString);
                }
                else
                {
                    Utility.EventLog(21, userId, $"更新員工 {EmpNo} 班別為 {shiftId} 失敗", ip, ConnectionString);
                }
            }
            else
            {
                Utility.EventLog(21, userId, $"更新員工班別失敗: 使用者 {userId} 並非管理員", ip, ConnectionString);
            }
            return ret;
        }

        /// <summary>
        /// 刪除員工的班別設定 (將班別設定為 NULL)
        /// </summary>
        /// <param name="userId">執行操作的使用者 ID</param>
        /// <param name="ip">執行操作的 IP 位址</param>
        /// <param name="EmpNo">要刪除班別設定的員工 ID</param>
        /// <returns>刪除成功回傳 true，否則回傳 false</returns>
        /// <remarks>只有管理員可以執行此操作</remarks>
        public bool Delete(string userId, string ip, string EmpNo)
        {
            bool ret = false;
            if (Utility.IsAdmin(userId, ConnectionString))
            {
                string sqlstr = "UPDATE dbo.Employee SET ShiftId = NULL WHERE EmpNo = @EmpNo";
                SqlParameter parameter = new SqlParameter("@EmpNo", SqlDbType.Char, 4) { Value = EmpNo };
                int iRet = SqlUtil.ExecuteSqlNonQuery(ConnectionString, sqlstr, parameter);
                if (iRet == 1) //成功
                {
                    ret = true;
                    Utility.EventLog(23, userId, $"刪除員工 {EmpNo} 班別成功", ip, ConnectionString);
                }
                else
                {
                    Utility.EventLog(23, userId, $"刪除員工 {EmpNo} 班別失敗", ip, ConnectionString);
                }
            }
            else
            {
                Utility.EventLog(23, userId, $"刪除員工班別失敗: 使用者 {userId} 並非管理員", ip, ConnectionString);
            }
            return ret;
        }
    }
}
