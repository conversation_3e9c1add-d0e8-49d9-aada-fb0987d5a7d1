using Microsoft.Data.SqlClient;

using Newtonsoft.Json;

using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

using WorkdayCalendar.Models;

namespace WorkdayCalendar.DTO
{
    /// <summary>
    /// 工作日資料傳輸物件 (Data Transfer Object)
    /// 負責處理工作日相關的資料庫操作，包含查詢、新增、修改、刪除工作日資料
    /// </summary>
    public class WorkdayDto
    {

        /// <summary>
        /// 資料庫連線字串
        /// </summary>
        private string ConnectionString { get; set; }

        /// <summary>
        /// 建構函式，初始化工作日資料傳輸物件
        /// </summary>
        /// <param name="connectionString">資料庫連線字串</param>
        public WorkdayDto(string connectionString)
        {
            ConnectionString = connectionString;
        }

        /// <summary>
        /// 取得所有班別清單
        /// 透過聯接 ShiftWeekday 與 WorkShift 資料表，取得不重複的班別資料
        /// </summary>
        /// <returns>包含班別ID與班別名稱的資料表</returns>
        public DataTable GetShifts()
        {
            // 使用DISTINCT確保班別不重複，並聯接兩個資料表取得完整班別資訊
            string sqlstr = $"SELECT DISTINCT ShiftWeekday.ShiftId, WorkShift.ShiftName FROM ShiftWeekday INNER JOIN WorkShift ON ShiftWeekday.ShiftId = WorkShift.ShiftId;";
            return SqlUtil.GetDataTable(ConnectionString, sqlstr);
        }

        /// <summary>
        /// 取得工作日類型清單
        /// 從 WorkdayType 資料表中取得所有的日期類型定義
        /// </summary>
        /// <returns>包含類型ID與類型名稱的資料表</returns>
        public DataTable GetWorkdayType()
        {
            // 查詢所有工作日類型：1-週間工作日 2-週六休息日 3-週日例假日 4-週間國定假日 等
            string sqlstr = $"SELECT TypeId ,TypeName FROM dbo.WorkdayType ;";
            return SqlUtil.GetDataTable(ConnectionString, sqlstr);
        }

        /// <summary>
        /// 取得指定年度與班別的工作日資料
        /// 為了維持行事曆顯示的完整性，會額外讀取前後各6天的資料
        /// </summary>
        /// <param name="year">查詢的年度</param>
        /// <param name="shiftId">班別ID</param>
        /// <returns>包含完整工作日資訊的資料表</returns>
        public DataTable GetWorkdays(int year, int shiftId)
        {
            // 前後多讀6天，以維持行事曆假日正確性
            // 例如：2024年的資料會從2023/12/26讀到2025/01/06
            string sqlstr = $"SELECT ShiftId,WorkDate,DayType,ArrivalTime,DepartureTime,FlexibleArrivalBefore ,FlexibleArrivalAfter,FlexibleDepartureBefore,FlexibleDepartureAfter,MiddayBreakStart ,MiddayBreakEnd ,MorningRestStart ,MorningRestEnd ,AfternoonRestStart ,AfternoonRestEnd ,WorkHours ,WeekDay, Comment FROM dbo.Workday WHERE (WorkDate BETWEEN '{year - 1}-12-26' AND '{year + 1}-01-06 23:59:59') AND (ShiftId=${shiftId});";
            return SqlUtil.GetDataTable(ConnectionString, sqlstr);
        }

        /// <summary>
        /// 檢查是否可以新增指定年度的工作日資料
        /// 只有管理員才能新增工作日，且該年度尚未建立工作日資料
        /// </summary>
        /// <param name="userId">使用者ID</param>
        /// <param name="ip">使用者IP位址</param>
        /// <param name="param">工作日參數物件</param>
        /// <returns>true表示可以新增，false表示不可新增</returns>
        public bool CanAdd(string userId, string ip, WorkdayParam param)
        {
            bool ret = false;

            // 檢查使用者是否為管理員
            if (!Utility.IsAdmin(userId, ConnectionString))
            {
                // 可選擇記錄事件日誌
                // Utility.EventLog(31, userId, $"查詢是否能建立年度工作日失敗: 使用者 { userId } 並非管理員", ip, ConnectionString);
                return ret;
            }
            param.StartDate = param.StartDate.ToLocalTime().Date;
            // 檢查該年度該班別是否已存在工作日資料
            string sqlstr = $"SELECT COUNT(*) AS Count FROM dbo.Workday WHERE (WorkDate BETWEEN '{param.Year}-1-1' AND '{param.Year}-12-31 23:59:59') AND (ShiftId=${param.ShiftId});";
            int count = (int)SqlUtil.ExecuteSqlScalar(ConnectionString, sqlstr);
            if (count == 0)
            {
                // 該年度該班別尚未建立工作日資料，可以新增
                return true;
            }
            return ret;
        }

        /// <summary>
        /// 新增單一工作日資料
        /// 只有管理員才能執行此操作，會記錄操作日誌
        /// </summary>
        /// <param name="userId">執行操作的使用者ID</param>
        /// <param name="ip">使用者IP位址，用於日誌記錄</param>
        /// <param name="workday">要新增的工作日物件</param>
        /// <returns>true表示新增成功，false表示新增失敗</returns>
        public bool Add(string userId, string ip, Workday workday)
        {
            bool ret = false;

            // 驗證使用者是否為管理員
            if (Utility.IsAdmin(userId, ConnectionString))
            {
                // SQL INSERT 語句，新增所有工作日相關欄位
                string sqlstr = "INSERT INTO dbo.Workday (ShiftId ,WorkDate ,DayType ,ArrivalTime ,DepartureTime ,FlexibleArrivalBefore,FlexibleArrivalAfter,FlexibleDepartureBefore,FlexibleDepartureAfter,MiddayBreakStart ,MiddayBreakEnd ,MorningRestStart ,MorningRestEnd ,AfternoonRestStart ,AfternoonRestEnd ,WorkHours ,WeekDay ,Comment) VALUES (@ShiftId ,@WorkDate ,@DayType ,@ArrivalTime ,@DepartureTime ,@FlexibleArrivalBefore ,@FlexibleArrivalAfter,@FlexibleDepartureBefore,@FlexibleDepartureAfter,@MiddayBreakStart ,@MiddayBreakEnd ,@MorningRestStart ,@MorningRestEnd ,@AfternoonRestStart ,@AfternoonRestEnd ,@WorkHours ,@WeekDay ,@Comment);";
                int iRet = ExecuteNonQuery(sqlstr, workday);

                if (iRet == 1) // 影響一筆資料表示成功
                {
                    ret = true;
                    // 記錄成功日誌，包含完整工作日資料
                    Utility.EventLog(31, userId, $"建立年度工作日成功: {JsonConvert.SerializeObject(workday)}", ip, ConnectionString);
                }
                else
                {
                    // 記錄失敗日誌
                    Utility.EventLog(31, userId, $"建立年度工作日失敗: {JsonConvert.SerializeObject(workday)}", ip, ConnectionString);
                }
            }
            else
            {
                // 非管理員嘗試新增，記錄權限不足日誌
                Utility.EventLog(31, userId, $"建立年度工作日失敗: 使用者 {userId} 並非管理員", ip, ConnectionString);
            }
            return ret;
        }

        /// <summary>
        /// 批量建立整年度的工作日資料
        /// 根據班別週間設定與國定假日清單，自動產生一整年的工作日資料
        /// 只有管理員才能執行此操作，且該年度尚未建立工作日資料
        /// </summary>
        /// <param name="userId">執行操作的使用者ID</param>
        /// <param name="ip">使用者IP位址</param>
        /// <param name="param">工作日參數，包含年度、班別、開始日期、國定假日清單等</param>
        /// <returns>true表示建立成功，false表示建立失敗</returns>
        public bool AddWorkdays(string userId, string ip, WorkdayParam param)
        {
            bool ret = false;
            DateTime startDate = param.StartDate.ToLocalTime().Date;

            // 檢查使用者權限
            if (!Utility.IsAdmin(userId, ConnectionString))
            {
                Utility.EventLog(31, userId, $"建立年度工作日失敗: 使用者 {userId} 並非管理員", ip, ConnectionString);
                return ret;
            }

            // 檢查該年度該班別是否已存在工作日資料，避免重複建立
            string sqlstr = $"SELECT COUNT(*) AS Count FROM dbo.Workday WHERE (WorkDate BETWEEN '{param.Year}-1-1' AND '{param.Year}-12-31 23:59:59') AND (ShiftId=${param.ShiftId});";
            int count = (int)SqlUtil.ExecuteSqlScalar(ConnectionString, sqlstr);
            if (count > 0)
            {
                // 該年度已有工作日資料，不允許重複建立
                return false;
            }
            WorkShiftDto workShiftDto = new WorkShiftDto(ConnectionString);
            WorkShift workShift = workShiftDto.Get(param.ShiftId);

            // 查詢該班別的週間工作設定（週一到週日的上下班時間等）
            ShiftWeekdayDto shiftWeekdayDto = new ShiftWeekdayDto(ConnectionString);
            DataTable dtShiftWeekday = shiftWeekdayDto.GetDataTable(param.ShiftId);

            if (dtShiftWeekday.Rows.Count > 0)
            {
                // 解析國定假日清單，將字串轉換為DateTime陣列
                DateTime[] holidays = new DateTime[param.Holidays.Length];
                for (int i = 0; i < param.Holidays.Length; i++)
                {
                    if (param.Holidays[i].Date != null && param.Holidays[i].Date.Length >= 6)
                    {
                        holidays[i] = DateTime.Parse(param.Holidays[i].Date);
                    }
                }

                // 建立假日查找字典以提升效率 (O(1) 查找時間)
                var holidayLookup = new Dictionary<DateTime, Holiday>();
                for (int i = 0; i < holidays.Length; i++)
                {
                    if (holidays[i] != default(DateTime))
                    {
                        holidayLookup[holidays[i].Date] = param.Holidays[i];
                    }
                }

                // 設定結束日期為該年度的12月31日
                DateTime endDate = new DateTime(param.Year, 12, 31);
                List<ShiftWeekday> shiftWeekdays = ShiftWeekdayDto.DataTableToShiftWeekday(dtShiftWeekday);

                // 逐日產生工作日資料，從開始日期到年底
                for (DateTime date = startDate; date <= endDate; date = date.AddDays(1))
                {
                    // 根據日期、班別設定、國定假日等資訊建立該日的工作日物件
                    //Workday workday = DealWorkday(param, workShift, dtShiftWeekday, holidayLookup, date);
                    Workday workday = DealWorkday(param, workShift, shiftWeekdays, holidayLookup, date);
                    // 新增該日的工作日資料
                    ret = Add(userId, ip, workday);
                }
            }

            // 記錄最終結果
            if (ret)
            {
                Utility.EventLog(31, userId, $"建立年度工作日成功: ", ip, ConnectionString);
            }
            else
            {
                Utility.EventLog(31, userId, $"建立年度工作日失敗: ", ip, ConnectionString);
            }
            return ret;
        }

        /// <summary>
        /// 刪除指定的工作日資料
        /// 只有管理員才能執行此操作，會記錄操作日誌
        /// </summary>
        /// <param name="userId">執行操作的使用者ID</param>
        /// <param name="ip">使用者IP位址</param>
        /// <param name="workday">要刪除的工作日物件（需包含ShiftId和WorkDate）</param>
        /// <returns>true表示刪除成功，false表示刪除失敗</returns>
        public bool Delete(string userId, string ip, Workday workday)
        {
            bool ret = false;

            // 檢查使用者是否為管理員
            if (Utility.IsAdmin(userId, ConnectionString))
            {
                // 根據班別ID和工作日期刪除資料
                string sqlstr = "DELETE FROM dbo.Workday WHERE ShiftId=@ShiftId AND WorkDate=@WorkDate;";
                SqlParameter[] parameters = new SqlParameter[2];
                parameters[0] = new SqlParameter("@ShiftId", SqlDbType.SmallInt);
                parameters[0].Value = workday.ShiftId;
                parameters[1] = new SqlParameter("@WorkDate", SqlDbType.Date);
                parameters[1].Value = workday.WorkDate.ToLocalTime();

                int iRet = SqlUtil.ExecuteSqlNonQuery(ConnectionString, sqlstr, parameters);

                if (iRet == 1) // 影響一筆資料表示刪除成功
                {
                    ret = true;
                    Utility.EventLog(33, userId, $"刪除年度工作日成功: ShiftId = {workday.ShiftId}, Year={workday.WorkDate}", ip, ConnectionString);
                }
                else
                {
                    // 刪除失敗（可能是資料不存在）
                    Utility.EventLog(33, userId, $"刪除年度工作日失敗: ShiftId = {workday.ShiftId}, Year={workday.WorkDate}", ip, ConnectionString);
                }
            }
            else
            {
                // 非管理員嘗試刪除，記錄權限不足日誌
                Utility.EventLog(33, userId, $"刪除年度工作日失敗: 使用者 {userId} 並非管理員。 ShiftId = {workday.ShiftId}, WorkDate={workday.WorkDate}", ip, ConnectionString);
            }
            return ret;
        }

        /// <summary>
        /// 處理單一日期的工作日資料 (使用 WorkShift 物件)
        /// 根據班別設定、國定假日清單等資訊，建立該日期的完整工作日物件
        /// </summary>
        /// <param name="param">工作日參數</param>
        /// <param name="workShift">班別設定物件</param>
        /// <param name="shiftWeekdays">週間班別List</param>
        /// <param name="holidayLookup">國定假日查找字典</param>
        /// <param name="date">要處理的日期</param>
        /// <returns>該日期的工作日物件</returns>
        private static Workday DealWorkday(WorkdayParam param, WorkShift workShift, List<ShiftWeekday> shiftWeekdays, Dictionary<DateTime, Holiday> holidayLookup, DateTime date)
        {
            Workday workday = new Workday();

            // 取得該日期是星期幾（0=週日, 1=週一, ..., 6=週六）
            byte weekday = (byte)date.DayOfWeek;
            workday.WeekDay = weekday;
            workday.ShiftId = Convert.ToInt16(param.ShiftId);
            workday.WorkDate = date;

            // 先檢查 shiftWeekdays 中是否有對應星期幾的設定
            ShiftWeekday matchingShiftWeekday = shiftWeekdays?.FirstOrDefault(sw => sw.WeekDay == weekday);

            if (matchingShiftWeekday != null)
            {
                // 使用 shiftWeekday 的設定
                workday.ArrivalTime = date + TimeSpan.Parse(matchingShiftWeekday.ArrivalTime);
                workday.DepartureTime = date + TimeSpan.Parse(matchingShiftWeekday.DepartureTime);
                workday.FlexibleArrivalBefore = date + TimeSpan.Parse(matchingShiftWeekday.FlexibleArrivalBefore);
                workday.FlexibleArrivalAfter = date + TimeSpan.Parse(matchingShiftWeekday.FlexibleArrivalAfter);
                workday.FlexibleDepartureBefore = date + TimeSpan.Parse(matchingShiftWeekday.FlexibleDepartureBefore);
                workday.FlexibleDepartureAfter = date + TimeSpan.Parse(matchingShiftWeekday.FlexibleDepartureAfter);
                workday.MiddayBreakStart = date + TimeSpan.Parse(matchingShiftWeekday.MiddayBreakStart);
                workday.MiddayBreakEnd = date + TimeSpan.Parse(matchingShiftWeekday.MiddayBreakEnd);
                workday.MorningRestStart = date + TimeSpan.Parse(matchingShiftWeekday.MorningRestStart);
                workday.MorningRestEnd = date + TimeSpan.Parse(matchingShiftWeekday.MorningRestEnd);
                workday.AfternoonRestStart = date + TimeSpan.Parse(matchingShiftWeekday.AfternoonRestStart);
                workday.AfternoonRestEnd = date + TimeSpan.Parse(matchingShiftWeekday.AfternoonRestEnd);
            }
            else
            {
                // 找不到對應的 shiftWeekday 設定，使用 workShift 的預設值
                workday.ArrivalTime = date + TimeSpan.Parse(workShift.ArrivalTime);
                workday.DepartureTime = date + TimeSpan.Parse(workShift.DepartureTime);
                workday.FlexibleArrivalBefore = date + TimeSpan.Parse(workShift.FlexibleArrivalBefore);
                workday.FlexibleArrivalAfter = date + TimeSpan.Parse(workShift.FlexibleArrivalAfter);
                workday.FlexibleDepartureBefore = date + TimeSpan.Parse(workShift.FlexibleDepartureBefore);
                workday.FlexibleDepartureAfter = date + TimeSpan.Parse(workShift.FlexibleDepartureAfter);
                workday.MiddayBreakStart = date + TimeSpan.Parse(workShift.MiddayBreakStart);
                workday.MiddayBreakEnd = date + TimeSpan.Parse(workShift.MiddayBreakEnd);
                workday.MorningRestStart = date + TimeSpan.Parse(workShift.MorningRestStart);
                workday.MorningRestEnd = date + TimeSpan.Parse(workShift.MorningRestEnd);
                workday.AfternoonRestStart = date + TimeSpan.Parse(workShift.AfternoonRestStart);
                workday.AfternoonRestEnd = date + TimeSpan.Parse(workShift.AfternoonRestEnd);
            }

            // 根據星期幾設定基本的日期類型和工作時間
            GetDayType(workShift, workday);

            // 檢查是否為國定假日並進行相應標記
            MarkHolidays(holidayLookup, workday);

            return workday;
        }

        /// <summary>
        /// 標記國定假日 (一般版本)
        /// 檢查指定日期是否為國定假日，如果是則更新工作日的類型和備註
        /// 時間複雜度: O(n)
        /// </summary>
        /// <param name="param">工作日參數</param>
        /// <param name="holidays">國定假日日期陣列</param>
        /// <param name="workday">要更新的工作日物件</param>
        private static void MarkHolidays(WorkdayParam param, DateTime[] holidays, Workday workday)
        {
            // 使用 LINQ 查找匹配的假日索引
            var holidayIndex = holidays
                .Select((date, index) => new { Date = date, Index = index })
                .FirstOrDefault(h => h.Date.Date == workday.WorkDate.Date)?.Index;

            if (holidayIndex.HasValue)
            {
                int index = holidayIndex.Value;

                // 設定假日名稱作為備註
                workday.Comment = param.Holidays[index].Name;
                // 國定假日不計工作時數
                workday.WorkHours = 0;

                // 根據原本是星期幾來設定國定假日的類型
                SetHolidayDayType(workday);
            }
        }

        /// <summary>
        /// 標記國定假日 (高效率版本)
        /// 使用字典查找指定日期是否為國定假日，如果是則更新工作日的類型和備註
        /// 時間複雜度: O(1)
        /// </summary>
        /// <param name="param">工作日參數</param>
        /// <param name="holidayLookup">國定假日查找字典</param>
        /// <param name="workday">要更新的工作日物件</param>
        private static void MarkHolidays(Dictionary<DateTime, Holiday> holidayLookup, Workday workday)
        {
            // 使用字典進行 O(1) 時間複雜度的查找
            if (holidayLookup.TryGetValue(workday.WorkDate.Date, out Holiday holiday))
            {
                // 設定假日名稱作為備註
                workday.Comment = holiday.Name;
                // 國定假日不計工作時數
                workday.WorkHours = 0;

                // 根據原本是星期幾來設定國定假日的類型
                SetHolidayDayType(workday);
            }
        }

        /// <summary>
        /// 根據星期幾設定國定假日的日期類型
        /// </summary>
        /// <param name="workday">要更新的工作日物件</param>
        private static void SetHolidayDayType(Workday workday)
        {
            switch (workday.WeekDay)
            {
                case 0: // 週日國定假日
                    workday.DayType = 6;
                    break;
                case 1: // 週一到週五的國定假日
                case 2:
                case 3:
                case 4:
                case 5:
                    workday.DayType = 4; // 週間國定假日
                    break;
                case 6: // 週六國定假日
                    workday.DayType = 5;
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 根據設定的WorkShift設定日期類型
        /// 設定基本的工作日類型，並根據班別設定計算工作時間
        /// </summary>
        /// <param name="workShift">班別設定物件</param>
        /// <param name="workday">要設定的工作日物件</param>
        private static void GetDayType(WorkShift workShift, Workday workday)
        {
            // 根據 WorkShift 的工作日設定來決定日期類型和工作時間
            string weekdayString = workday.WeekDay.ToString();
            if (workShift.Workdays.Contains(weekdayString))
            {
                // 該星期幾是工作日，設定所有時間資訊
                // 時間資訊已在 DealWorkday 方法中設定，這裡只需要計算工作時數和設定日期類型
                workday.WorkHours = Utility.CalcWorkHours(workday.ArrivalTime, workday.DepartureTime, workday.MiddayBreakStart, workday.MiddayBreakEnd);
                workday.DayType = 1; // 週間工作日
            }
            if (workShift.FixedDayOff == workday.WeekDay) // (週日) 例假日
            {
                workday.WorkHours = 0; // 預設不工作
                workday.DayType = 3;   // 例假日
            }

            if (workShift.RestDay == workday.WeekDay) // (週六) 休息日
            {
                workday.WorkHours = 0; // (週六) 預設不工作
                workday.DayType = 2;   // (週六) 休息日
            }
        }

        /// <summary>
        /// 根據星期幾設定日期類型
        /// 設定基本的工作日類型，並根據班別設定計算工作時間
        /// 日期類型定義：
        /// 1-週間工作日 2-週六休息日 3-週日例假日 4-週間國定假日
        /// 5-週六國定假日 6-週日國定假日 7-補班日 8-補假日
        /// 9-彈性放假日 20-週間休息日
        /// </summary>
        /// <param name="dt">班別週間設定資料表</param>
        /// <param name="workday">要設定的工作日物件</param>
        private static void GetDayType(DataTable dt, Workday workday)
        {
            switch (workday.WeekDay)
            {
                case 0: // 週日
                    workday.WorkHours = 0; // 週日不工作
                    workday.DayType = 3;   // 週日例假日
                    break;

                case 1: // 週一
                case 2: // 週二
                case 3: // 週三
                case 4: // 週四
                case 5: // 週五
                    // 預設為週間休息日，如果該星期幾有班別設定則改為工作日
                    workday.DayType = 20; // 目前不可能用到的週間休息日

                    // 查找該星期幾是否有對應的班別設定
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        byte dataWeekday = (byte)dt.Rows[i]["WeekDay"];
                        if (dataWeekday == workday.WeekDay)
                        {
                            // 找到對應的班別設定，更新所有時間資訊
                            workday.ArrivalTime = workday.WorkDate + (TimeSpan)dt.Rows[i]["ArrivalTime"];
                            workday.DepartureTime = workday.WorkDate + (TimeSpan)dt.Rows[i]["DepartureTime"];
                            workday.FlexibleArrivalBefore = workday.WorkDate + (TimeSpan)dt.Rows[i]["FlexibleArrivalBefore"];
                            workday.FlexibleArrivalAfter = workday.WorkDate + (TimeSpan)dt.Rows[i]["FlexibleArrivalAfter"];
                            workday.FlexibleDepartureBefore = workday.WorkDate + (TimeSpan)dt.Rows[i]["FlexibleDepartureBefore"];
                            workday.FlexibleDepartureAfter = workday.WorkDate + (TimeSpan)dt.Rows[i]["FlexibleDepartureAfter"];
                            workday.MiddayBreakStart = workday.WorkDate + (TimeSpan)dt.Rows[i]["MiddayBreakStart"];
                            workday.MiddayBreakEnd = workday.WorkDate + (TimeSpan)dt.Rows[i]["MiddayBreakEnd"];
                            workday.MorningRestStart = workday.WorkDate + (TimeSpan)dt.Rows[i]["MorningRestStart"];
                            workday.MorningRestEnd = workday.WorkDate + (TimeSpan)dt.Rows[i]["MorningRestEnd"];
                            workday.AfternoonRestStart = workday.WorkDate + (TimeSpan)dt.Rows[i]["AfternoonRestStart"];
                            workday.AfternoonRestEnd = workday.WorkDate + (TimeSpan)dt.Rows[i]["AfternoonRestEnd"];

                            // 計算實際工作時數（扣除午休時間）
                            workday.WorkHours = Utility.CalcWorkHours(workday.ArrivalTime, workday.DepartureTime, workday.MiddayBreakStart, workday.MiddayBreakEnd);
                            workday.DayType = 1; // 週間工作日
                            break;
                        }
                    }
                    break;

                case 6: // 週六
                    workday.WorkHours = 0; // 週六預設不工作
                    workday.DayType = 2;   // 週六休息日
                    break;

                default:
                    break;
            }
        }

        /// <summary>
        /// 檢查工作日的日期類型是否正確
        /// 驗證星期幾與日期類型的組合是否符合邏輯
        /// </summary>
        /// <param name="workday">要檢查的工作日物件</param>
        /// <returns>true表示日期類型正確，false表示有邏輯錯誤</returns>
        private bool CheckDayType(Workday workday)
        {
            bool ret = false;

            switch (workday.WeekDay)
            {
                case 0: // 週日
                    // 週日只能是例假日(3)或週日國定假日(6)
                    if (workday.DayType == 3 || workday.DayType == 6)
                    {
                        ret = true;
                    }
                    break;

                case 1: // 週一到週五
                case 2:
                case 3:
                case 4:
                case 5:
                    // 週間可以是：工作日(1)、國定假日(4)、補假日(8)、彈性放假日(9)、
                    // 其他特殊假日(10)、週間休息日(20)
                    if (workday.DayType == 1 || workday.DayType == 4 || workday.DayType == 8 ||
                        workday.DayType == 9 || workday.DayType == 10 || workday.DayType == 20)
                    {
                        ret = true;
                    }
                    break;

                case 6: // 週六
                    // 週六可以是：休息日(2)、週六國定假日(5)、補班日(7)、其他特殊工作日(11)
                    if (workday.DayType == 2 || workday.DayType == 5 || workday.DayType == 7 || workday.DayType == 11)
                    {
                        ret = true;
                    }
                    break;

                default:
                    ret = false;
                    break;
            }
            return ret;
        }

        /// <summary>
        /// 修改工作日資料
        /// 只有管理員才能執行此操作，會驗證日期類型的正確性，並記錄操作日誌
        /// 修改成功後會執行等待任務
        /// </summary>
        /// <param name="userName">使用者名稱</param>
        /// <param name="userId">使用者ID</param>
        /// <param name="ip">使用者IP位址</param>
        /// <param name="workday">要修改的工作日物件</param>
        /// <returns>true表示修改成功，false表示修改失敗</returns>
        public async Task<bool> Update(string userName, string userId, string ip, Workday workday)
        {
            bool ret = false;
            await Task.CompletedTask;
            // 檢查使用者是否為管理員
            if (Utility.IsAdmin(userId, ConnectionString))
            {
                // 驗證日期類型是否正確（星期幾與日期類型的組合必須合理）
                if (!CheckDayType(workday))
                {
                    Utility.EventLog(32, userId, $"修改年度工作日失敗，日期類型錯誤: {JsonConvert.SerializeObject(workday)}", ip, ConnectionString);
                    return ret;
                }

                // SQL UPDATE 語句，更新所有工作日相關欄位
                string sqlstr = "UPDATE dbo.Workday SET DayType=@DayType,ArrivalTime=@ArrivalTime,DepartureTime=@DepartureTime,FlexibleArrivalBefore = @FlexibleArrivalBefore,FlexibleArrivalAfter=@FlexibleArrivalAfter,FlexibleDepartureBefore=@FlexibleDepartureBefore,FlexibleDepartureAfter=@FlexibleDepartureAfter,MiddayBreakStart = @MiddayBreakStart,MiddayBreakEnd=@MiddayBreakEnd,MorningRestStart=@MorningRestStart,MorningRestEnd = @MorningRestEnd,AfternoonRestStart = @AfternoonRestStart,AfternoonRestEnd = @AfternoonRestEnd,WorkHours=@WorkHours,WeekDay=@WeekDay,Comment=@Comment WHERE ShiftId=@ShiftId AND WorkDate=@WorkDate;";
                int iRet = ExecuteNonQuery(sqlstr, workday);

                if (iRet == 1) // 影響一筆資料表示修改成功
                {
                    ret = true;
                    // 記錄成功日誌
                    Utility.EventLog(32, userId, $"修改年度工作日成功: {JsonConvert.SerializeObject(workday)}", ip, ConnectionString);
                }
                else
                {
                    // 記錄失敗日誌
                    Utility.EventLog(32, userId, $"修改年度工作日失敗: {JsonConvert.SerializeObject(workday)}", ip, ConnectionString);
                }
            }
            else
            {
                // 非管理員嘗試修改，記錄權限不足日誌
                Utility.EventLog(32, userId, $"修改年度工作日失敗: 使用者 {userId} {userName} 並非管理員  {JsonConvert.SerializeObject(workday)} ", ip, ConnectionString);
            }
            return ret;
        }

        /// <summary>
        /// 執行 SQL 非查詢命令
        /// 將工作日物件轉換為 SQL 參數並執行 INSERT 或 UPDATE 命令
        /// </summary>
        /// <param name="sqlstr">要執行的 SQL 命令字串</param>
        /// <param name="workday">工作日物件，用於產生 SQL 參數</param>
        /// <returns>受影響的資料列數</returns>
        private int ExecuteNonQuery(string sqlstr, Workday workday)
        {
            // 將工作日物件轉換為 SQL 參數陣列
            SqlParameter[] parameters = SetParameters(workday);
            // 執行 SQL 命令並回傳受影響的資料列數
            int iRet = SqlUtil.ExecuteSqlNonQuery(ConnectionString, sqlstr, parameters);
            return iRet;
        }

        /// <summary>
        /// 設定 SQL 參數
        /// 將工作日物件的所有屬性轉換為 SQL 參數陣列，用於資料庫操作
        /// 包含班別ID、工作日期、日期類型、各種時間設定、工作時數、星期幾、備註等
        /// </summary>
        /// <param name="workday">工作日物件</param>
        /// <returns>包含所有工作日資料的 SQL 參數陣列</returns>
        private SqlParameter[] SetParameters(Workday workday)
        {
            SqlParameter[] parameters = new SqlParameter[18];

            // 班別ID
            parameters[0] = new SqlParameter("@ShiftId", SqlDbType.SmallInt);
            parameters[0].Value = workday.ShiftId;

            // 工作日期
            parameters[1] = new SqlParameter("@WorkDate", SqlDbType.Date);
            parameters[1].Value = workday.WorkDate.ToLocalTime();

            // 日期類型
            parameters[2] = new SqlParameter("@DayType", SqlDbType.TinyInt);
            parameters[2].Value = workday.DayType;

            // 上班時間
            parameters[3] = new SqlParameter("@ArrivalTime", SqlDbType.SmallDateTime);
            parameters[3].Value = workday.ArrivalTime.ToLocalTime();

            // 下班時間
            parameters[4] = new SqlParameter("@DepartureTime", SqlDbType.SmallDateTime);
            parameters[4].Value = workday.DepartureTime.ToLocalTime();

            // 彈性上班時間範圍 - 最早到達時間
            parameters[5] = new SqlParameter("@FlexibleArrivalBefore", SqlDbType.SmallDateTime);
            parameters[5].Value = workday.FlexibleArrivalBefore.ToLocalTime();

            // 彈性上班時間範圍 - 最晚到達時間
            parameters[6] = new SqlParameter("@FlexibleArrivalAfter", SqlDbType.SmallDateTime);
            parameters[6].Value = workday.FlexibleArrivalAfter.ToLocalTime();

            // 彈性下班時間範圍 - 最早離開時間
            parameters[7] = new SqlParameter("@FlexibleDepartureBefore", SqlDbType.SmallDateTime);
            parameters[7].Value = workday.FlexibleDepartureBefore.ToLocalTime();

            // 彈性下班時間範圍 - 最晚離開時間
            parameters[8] = new SqlParameter("@FlexibleDepartureAfter", SqlDbType.SmallDateTime);
            parameters[8].Value = workday.FlexibleDepartureAfter.ToLocalTime();

            // 午休開始時間
            parameters[9] = new SqlParameter("@MiddayBreakStart", SqlDbType.SmallDateTime);
            parameters[9].Value = workday.MiddayBreakStart.ToLocalTime();

            // 午休結束時間
            parameters[10] = new SqlParameter("@MiddayBreakEnd", SqlDbType.SmallDateTime);
            parameters[10].Value = workday.MiddayBreakEnd.ToLocalTime();

            // 上午休息開始時間
            parameters[11] = new SqlParameter("@MorningRestStart", SqlDbType.SmallDateTime);
            parameters[11].Value = workday.MorningRestStart.ToLocalTime();

            // 上午休息結束時間
            parameters[12] = new SqlParameter("@MorningRestEnd", SqlDbType.SmallDateTime);
            parameters[12].Value = workday.MorningRestEnd.ToLocalTime();

            // 下午休息開始時間
            parameters[13] = new SqlParameter("@AfternoonRestStart", SqlDbType.SmallDateTime);
            parameters[13].Value = workday.AfternoonRestStart.ToLocalTime();

            // 下午休息結束時間
            parameters[14] = new SqlParameter("@AfternoonRestEnd", SqlDbType.SmallDateTime);
            parameters[14].Value = workday.AfternoonRestEnd.ToLocalTime();

            // 工作時數
            parameters[15] = new SqlParameter("@WorkHours", SqlDbType.Float);
            parameters[15].Value = workday.WorkHours;

            // 星期幾 (0=週日, 1=週一, ..., 6=週六)
            parameters[16] = new SqlParameter("@WeekDay", SqlDbType.TinyInt);
            parameters[16].Value = workday.WeekDay;

            // 備註 (國定假日名稱或其他說明)
            parameters[17] = new SqlParameter("@Comment", SqlDbType.NVarChar, 512);
            if (string.IsNullOrWhiteSpace(workday.Comment))
            {
                parameters[17].Value = DBNull.Value; // 空白備註設為 NULL
            }
            else
            {
                parameters[17].Value = workday.Comment;
            }

            return parameters;
        }
    }
}
