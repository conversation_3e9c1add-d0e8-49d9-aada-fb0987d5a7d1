
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using Microsoft.Data.SqlClient;

using Newtonsoft.Json;

using WorkdayCalendar.Models;

namespace WorkdayCalendar.DTO
{
 /// <summary>
 /// 員工工作日資料傳輸物件 (Data Transfer Object)
 /// 負責處理員工個人工作日曆相關的資料庫操作，包含查詢、新增、修改、刪除等。
 /// </summary>
 public class EmpWorkdayDto
 {
 /// <summary>
 /// 資料庫連線字串
 /// </summary>
 private string ConnectionString { get; set; }

 /// <summary>
 /// 建構函式，初始化員工工作日資料傳輸物件
 /// </summary>
 /// <param name="connectionString">資料庫連線字串</param>
 public EmpWorkdayDto(string connectionString)
 {
 ConnectionString = connectionString;
 }

 /// <summary>
 /// 取得指定員工在特定年份的工作日資料
 /// </summary>
 /// <param name="empNo">員工編號</param>
 /// <param name="year">查詢的年度</param>
 /// <returns>包含該員工年度工作日資訊的資料表</returns>
 public DataTable GetEmpWorkdays(string empNo, int year)
 {
 // 查詢指定員工在特定年份的所有工作日資料
 string sqlstr = """               
 SELECT EmpWorkday.EmpNo, EmpWorkday.ShiftId, EmpWorkday.WorkDate, EmpWorkday.DayType, 
 EmpWorkday.ArrivalTime, EmpWorkday.DepartureTime, EmpWorkday.FlexibleArrivalBefore, 
 EmpWorkday.FlexibleArrivalAfter, EmpWorkday.FlexibleDepartureBefore, EmpWorkday.FlexibleDepartureAfter, 
 EmpWorkday.MiddayBreakStart, EmpWorkday.MiddayBreakEnd, EmpWorkday.MorningRestStart, 
 EmpWorkday.MorningRestEnd, EmpWorkday.AfternoonRestStart, EmpWorkday.AfternoonRestEnd, 
 EmpWorkday.WorkHours, EmpWorkday.WeekDay, EmpWorkday.Comment, WorkShift.ShiftName
 FROM EmpWorkday INNER JOIN WorkShift ON EmpWorkday.ShiftId = WorkShift.ShiftId
WHERE EmpWorkday.EmpNo = @EmpNo AND YEAR(EmpWorkday.WorkDate) = @Year ORDER BY WorkDate;
""";
 SqlParameter[] parameters = new SqlParameter[2];
 parameters[0] = new SqlParameter("@EmpNo", SqlDbType.NVarChar, 20);
 parameters[0].Value = empNo;
 parameters[1] = new SqlParameter("@Year", SqlDbType.Int);
 parameters[1].Value = year;

 return SqlUtil.GetDataTable(ConnectionString, sqlstr, parameters);
 }


 /// <summary>
 /// 取得指定員工在特定年份的工作日資料
 /// </summary>
 /// <param name="empNo">員工編號</param>
 /// <param name="year">查詢的年度</param>
 /// <param name="shiftId">班別ID</param>
 /// <returns>包含該員工年度工作日資訊的資料表</returns>
 public DataTable GetEmpWorkdays(string empNo, int year, int shiftId)
 {
 // 查詢指定員工在特定年份的所有工作日資料
 string sqlstr = """
 SELECT EmpWorkday.EmpNo, EmpWorkday.ShiftId, EmpWorkday.WorkDate, EmpWorkday.DayType, 
 EmpWorkday.ArrivalTime, EmpWorkday.DepartureTime, EmpWorkday.FlexibleArrivalBefore, 
 EmpWorkday.FlexibleArrivalAfter, EmpWorkday.FlexibleDepartureBefore, EmpWorkday.FlexibleDepartureAfter, 
 EmpWorkday.MiddayBreakStart, EmpWorkday.MiddayBreakEnd, EmpWorkday.MorningRestStart, 
 EmpWorkday.MorningRestEnd, EmpWorkday.AfternoonRestStart, EmpWorkday.AfternoonRestEnd, 
 EmpWorkday.WorkHours, EmpWorkday.WeekDay, EmpWorkday.Comment, WorkShift.ShiftName
 FROM EmpWorkday INNER JOIN WorkShift ON EmpWorkday.ShiftId = WorkShift.ShiftId
WHERE EmpWorkday.EmpNo = @EmpNo AND YEAR(EmpWorkday.WorkDate) = @Year AND EmpWorkday.ShiftId=@ShiftId ORDER BY WorkDate;
""";
                
 SqlParameter[] parameters = new SqlParameter[3];
 parameters[0] = new SqlParameter("@EmpNo", SqlDbType.NVarChar, 20);
 parameters[0].Value = empNo;
 parameters[1] = new SqlParameter("@Year", SqlDbType.Int);
 parameters[1].Value = year;
 parameters[2] = new SqlParameter("@ShiftId", SqlDbType.SmallInt);
 parameters[2].Value = shiftId;

 return SqlUtil.GetDataTable(ConnectionString, sqlstr, parameters);
 }

 /// <summary>
 /// 檢查是否可以為指定員工新增年度工作日資料
 /// </summary>
 /// <param name="empNo">員工編號</param>
 /// <param name="year">要檢查的年份</param>
 /// <returns>true 表示可以新增，false 表示不可新增 (資料已存在)</returns>
 public bool CanAdd(string empNo, int year)
 {
 // 檢查該員工在該年度是否已存在工作日資料
 string sqlstr = $"SELECT COUNT(*) FROM dbo.EmpWorkday WHERE EmpNo = @EmpNo AND YEAR(WorkDate) = @Year;";
 SqlParameter[] parameters = new SqlParameter[2];
 parameters[0] = new SqlParameter("@EmpNo", SqlDbType.NVarChar, 20);
 parameters[0].Value = empNo;
 parameters[1] = new SqlParameter("@Year", SqlDbType.Int);
 parameters[1].Value = year;

 int count = (int)SqlUtil.ExecuteSqlScalar(ConnectionString, sqlstr, parameters);
 return count == 0;
 }


 /// <summary>
 /// 批次建立指定員工的工作日資料
 /// </summary>
 /// <param name="userId">執行操作的使用者ID</param>
 /// <param name="ip">使用者IP位址</param>
 /// <param name="empNo">員工編號</param>
 /// <param name="shiftId">班別ID</param>
 /// <param name="startDate">開始日期</param>
 /// <param name="endDate">結束日期</param>
 /// <returns>true 表示新增成功，false 表示新增失敗</returns>
 public bool AddOrUpdateEmpWorkdays(string userId, string ip, string empNo, int shiftId, DateTime startDate, DateTime endDate)
 {
 // 1. 取得全域的年度行事曆範本
 string workdaySql = "SELECT * FROM dbo.WorkShift WHERE ShiftId = @ShiftId;";
 SqlParameter[] workdayParams = new[]
 {
 new SqlParameter("@ShiftId", SqlDbType.SmallInt) { Value = shiftId }
 };
 DataTable dtWorkShift = SqlUtil.GetDataTable(ConnectionString, workdaySql, workdayParams);
 DataRow shiftInfo = dtWorkShift.Rows[0];
 if (dtWorkShift.Rows.Count == 0)
 {
 Utility.EventLog(41, userId, $"建立員工工作日失敗: 找不到對應的公用行事曆範本 (ShiftId: {shiftId}, Year: {startDate.Year})", ip, ConnectionString);
 return false; // 找不到對應的行事曆範本
 }
 WorkShiftDto workShiftDto = new WorkShiftDto(ConnectionString);
 WorkShift workShift = workShiftDto.Get(shiftId);
 // 查詢該班別的週間工作設定（週一到週日的上下班時間等）
 ShiftWeekdayDto shiftWeekdayDto = new ShiftWeekdayDto(ConnectionString);
 DataTable dtShiftWeekday = shiftWeekdayDto.GetDataTable(shiftId);
 List<ShiftWeekday> shiftWeekdays = ShiftWeekdayDto.DataTableToShiftWeekday(dtShiftWeekday);
 List<Holiday> holidaysList = shiftWeekdayDto.GetHolidays(startDate.Year);
 Holiday[] holidaysArray = holidaysList.ToArray();
 // 解析國定假日清單，將字串轉換為DateTime陣列
 DateTime[] holidays = new DateTime[holidaysList.Count];
 for (int i = 0; i < holidaysArray.Length; i++)
 {
 if (holidaysArray[i].Date != null && holidaysArray[i].Date.Length >= 6)
 {
 holidays[i] = DateTime.Parse(holidaysArray[i].Date);
 }
 }

 // 建立假日查找字典以提升效率 (O(1) 查找時間)
 var holidayLookup = new Dictionary<DateTime, Holiday>();
 for (int i = 0; i < holidays.Length; i++)
 {
 if (holidays[i] != default(DateTime))
 {
 holidayLookup[holidays[i].Date] = holidaysArray[i];
 }
 }

 // 2. 組合並批次寫入資料
 List<EmpWorkday> workdaysToInsert = new List<EmpWorkday>();
 for (DateTime date = startDate; date <= endDate; date = date.AddDays(1))
 {
 // 根據日期、班別設定、國定假日等資訊建立該日的工作日物件
 EmpWorkday workday = DealEmpWorkday(empNo, workShift, shiftWeekdays, holidayLookup, date);
 workdaysToInsert.Add(workday);
 }

 // 使用 Bulk Copy 提高寫入效率
 try
 {
 // 這裡假設 SqlUtil 有一個 BulkInsert 方法，如果沒有需要實作
 // SqlUtil.BulkInsert(ConnectionString, "EmpWorkday", workdaysToInsert);

 // 如果沒有 BulkInsert, 退回逐筆新增
 foreach (var day in workdaysToInsert)
 {
 AddOrUpdate(userId, ip, day);
 }
 Utility.EventLog(41, userId, $"成功建立員工 {empNo} 從 {startDate.ToString("yyyy/MM/dd")} 至 {endDate.ToString("yyyy/MM/dd")} 的工作日", ip, ConnectionString);
 return true;
 }
 catch (Exception ex)
 {
 Utility.EventLog(41, userId, $"批次寫入員工年度工作日時發生錯誤: {ex.Message}", ip, ConnectionString);
 }
 return false;
 }

 /// <summary>
 /// 處理單一日期的員工工作日資料 (使用 EmpWorkShift 物件)
 /// 根據班別設定、國定假日清單等資訊，建立該日期的完整工作日物件
 /// </summary>
 /// <param name="empNo">員工編號</param>
 /// <param name="workShift">班別設定物件</param>
 /// <param name="shiftWeekdays">週間班別List</param>
 /// <param name="holidayLookup">國定假日查找字典</param>
 /// <param name="date">要處理的日期</param>
 /// <returns>該日期的工作日物件</returns>
 private static EmpWorkday DealEmpWorkday(string empNo, WorkShift workShift, List<ShiftWeekday> shiftWeekdays, Dictionary<DateTime, Holiday> holidayLookup, DateTime date)
 {
 EmpWorkday workday = new EmpWorkday();
 workday.EmpNo = empNo;

 // 取得該日期是星期幾（0=週日, 1=週一, ..., 6=週六）
 byte weekday = (byte)date.DayOfWeek;
 workday.WeekDay = weekday;
 workday.ShiftId = Convert.ToInt16(workShift.ShiftId);
 workday.WorkDate = date;

 // 先檢查 shiftWeekdays 中是否有對應星期幾的設定
 ShiftWeekday matchingShiftWeekday = shiftWeekdays?.FirstOrDefault(sw => sw.WeekDay == weekday);

 if (matchingShiftWeekday != null)
 {
 // 使用 shiftWeekday 的設定
 workday.ArrivalTime = date + TimeSpan.Parse(matchingShiftWeekday.ArrivalTime);
 workday.DepartureTime = date + TimeSpan.Parse(matchingShiftWeekday.DepartureTime);
 workday.FlexibleArrivalBefore = date + TimeSpan.Parse(matchingShiftWeekday.FlexibleArrivalBefore);
 workday.FlexibleArrivalAfter = date + TimeSpan.Parse(matchingShiftWeekday.FlexibleArrivalAfter);
 workday.FlexibleDepartureBefore = date + TimeSpan.Parse(matchingShiftWeekday.FlexibleDepartureBefore);
 workday.FlexibleDepartureAfter = date + TimeSpan.Parse(matchingShiftWeekday.FlexibleDepartureAfter);
 workday.MiddayBreakStart = date + TimeSpan.Parse(matchingShiftWeekday.MiddayBreakStart);
 workday.MiddayBreakEnd = date + TimeSpan.Parse(matchingShiftWeekday.MiddayBreakEnd);
 workday.MorningRestStart = date + TimeSpan.Parse(matchingShiftWeekday.MorningRestStart);
 workday.MorningRestEnd = date + TimeSpan.Parse(matchingShiftWeekday.MorningRestEnd);
 workday.AfternoonRestStart = date + TimeSpan.Parse(matchingShiftWeekday.AfternoonRestStart);
 workday.AfternoonRestEnd = date + TimeSpan.Parse(matchingShiftWeekday.AfternoonRestEnd);
 }
 else
 {
 // 找不到對應的 shiftWeekday 設定，使用 workShift 的預設值
 workday.ArrivalTime = date + TimeSpan.Parse(workShift.ArrivalTime);
 workday.DepartureTime = date + TimeSpan.Parse(workShift.DepartureTime);
 workday.FlexibleArrivalBefore = date + TimeSpan.Parse(workShift.FlexibleArrivalBefore);
 workday.FlexibleArrivalAfter = date + TimeSpan.Parse(workShift.FlexibleArrivalAfter);
 workday.FlexibleDepartureBefore = date + TimeSpan.Parse(workShift.FlexibleDepartureBefore);
 workday.FlexibleDepartureAfter = date + TimeSpan.Parse(workShift.FlexibleDepartureAfter);
 workday.MiddayBreakStart = date + TimeSpan.Parse(workShift.MiddayBreakStart);
 workday.MiddayBreakEnd = date + TimeSpan.Parse(workShift.MiddayBreakEnd);
 workday.MorningRestStart = date + TimeSpan.Parse(workShift.MorningRestStart);
 workday.MorningRestEnd = date + TimeSpan.Parse(workShift.MorningRestEnd);
 workday.AfternoonRestStart = date + TimeSpan.Parse(workShift.AfternoonRestStart);
 workday.AfternoonRestEnd = date + TimeSpan.Parse(workShift.AfternoonRestEnd);
 }

 // 根據星期幾設定基本的日期類型和工作時間
 GetDayType(workShift, workday);

 // 檢查是否為國定假日並進行相應標記
 MarkHolidays(holidayLookup, workday);

 return workday;
 }

 /// <summary>
 /// 標記國定假日 (高效率版本)
 /// 使用字典查找指定日期是否為國定假日，如果是則更新工作日的類型和備註
 /// 時間複雜度: O(1)
 /// </summary>
 /// <param name="param">工作日參數</param>
 /// <param name="holidayLookup">國定假日查找字典</param>
 /// <param name="workday">要更新的工作日物件</param>
 private static void MarkHolidays(Dictionary<DateTime, Holiday> holidayLookup, EmpWorkday workday)
 {
 // 使用字典進行 O(1) 時間複雜度的查找
 if (holidayLookup.TryGetValue(workday.WorkDate.Date, out Holiday holiday))
 {
 // 設定假日名稱作為備註
 workday.Comment = holiday.Name;
 // 國定假日不計工作時數
 workday.WorkHours = 0;

 // 根據原本是星期幾來設定國定假日的類型
 SetHolidayDayType(workday);
 }
 }

 /// <summary>
 /// 根據星期幾設定國定假日的日期類型
 /// </summary>
 /// <param name="workday">要更新的工作日物件</param>
 private static void SetHolidayDayType(EmpWorkday workday)
 {
 switch (workday.WeekDay)
 {
 case 0: // 週日國定假日
 workday.DayType = 6;
 break;
 case 1: // 週一到週五的國定假日
 case 2:
 case 3:
 case 4:
 case 5:
 workday.DayType = 4; // 週間國定假日
 break;
 case 6: // 週六國定假日
 workday.DayType = 5;
 break;
 default:
 break;
 }
 }

 /// <summary>
 /// 根據設定的WorkShift設定日期類型
 /// 設定基本的工作日類型，並根據班別設定計算工作時間
 /// </summary>
 /// <param name="workShift">班別設定物件</param>
 /// <param name="workday">要設定的員工工作日物件</param>
 private static void GetDayType(WorkShift workShift, EmpWorkday workday)
 {
 // 根據 WorkShift 的工作日設定來決定日期類型和工作時間
 string weekdayString = workday.WeekDay.ToString();
 if (workShift.Workdays.Contains(weekdayString))
 {
 // 該星期幾是工作日，設定所有時間資訊
 // 時間資訊已在 DealWorkday 方法中設定，這裡只需要計算工作時數和設定日期類型
 workday.WorkHours = Utility.CalcWorkHours(workday.ArrivalTime, workday.DepartureTime, workday.MiddayBreakStart, workday.MiddayBreakEnd);
 workday.DayType = 1; // 週間工作日
 }
 if (workShift.FixedDayOff == workday.WeekDay) // (週日) 例假日
 {
 workday.WorkHours = 0; // 預設不工作
 workday.DayType = 3; // 例假日
 }

 if (workShift.RestDay == workday.WeekDay) // (週六) 休息日
 {
 workday.WorkHours = 0; // (週六) 預設不工作
 workday.DayType = 2; // (週六) 休息日
 }
 }

 /// <summary>
 /// 批次建立指定員工的整年度工作日資料
 /// </summary>
 /// <param name="userId">執行操作的使用者ID</param>
 /// <param name="ip">使用者IP位址</param>
 /// <param name="empNo">員工編號</param>
 /// <param name="year">要建立的年份</param>
 /// <param name="shiftId">要套用的班別ID</param>
 /// <returns>true 表示建立成功，false 表示建立失敗</returns>
 public bool AddEmpWorkdays(string userId, string ip, string empNo, int year, int shiftId)
 {
 // 1. 檢查權限 (此處簡化，假設呼叫者已有權限)

 // 2. 取得全域的年度行事曆範本
 string workdaySql = "SELECT * FROM dbo.WorkShift WHERE ShiftId = @ShiftId;";
 SqlParameter[] workdayParams = new[]
 {
 new SqlParameter("@ShiftId", SqlDbType.SmallInt) { Value = shiftId }
 };
 DataTable dtWorkShift = SqlUtil.GetDataTable(ConnectionString, workdaySql, workdayParams);
 DataRow shiftInfo = dtWorkShift.Rows[0];
 if (dtWorkShift.Rows.Count == 0)
 {
 Utility.EventLog(41, userId, $"建立員工年度工作日失敗: 找不到對應的公用行事曆範本 (ShiftId: {shiftId}, Year: {year})", ip, ConnectionString);
 return false; // 找不到對應的行事曆範本
 }

 // 5. 組合並批次寫入資料
 List<EmpWorkday> workdaysToInsert = new List<EmpWorkday>();
 foreach (DataRow row in dtWorkShift.Rows)
 {
 EmpWorkday newDay = new EmpWorkday
 {
 EmpNo = empNo,
 ShiftId = Convert.ToInt16(row["ShiftId"]),
 WorkDate = Convert.ToDateTime(row["WorkDate"]),
 DayType = Convert.ToByte(row["DayType"]),
 WeekDay = Convert.ToByte(row["WeekDay"]),
 WorkHours = Convert.ToDouble(row["WorkHours"]),
 Comment = row["Comment"]?.ToString(),

 // 套用員工個人化的時間設定
 ArrivalTime = Convert.ToDateTime(row["WorkDate"]).Date + ((TimeSpan)shiftInfo["ArrivalTime"]),
 DepartureTime = Convert.ToDateTime(row["WorkDate"]).Date + ((TimeSpan)shiftInfo["DepartureTime"]),
 FlexibleArrivalBefore = Convert.ToDateTime(row["WorkDate"]).Date + ((TimeSpan)shiftInfo["FlexibleArrivalBefore"]),
 FlexibleArrivalAfter = Convert.ToDateTime(row["WorkDate"]).Date + ((TimeSpan)shiftInfo["FlexibleArrivalAfter"]),
 FlexibleDepartureBefore = Convert.ToDateTime(row["WorkDate"]).Date + ((TimeSpan)shiftInfo["FlexibleDepartureBefore"]),
 FlexibleDepartureAfter = Convert.ToDateTime(row["WorkDate"]).Date + ((TimeSpan)shiftInfo["FlexibleDepartureAfter"]),
 MiddayBreakStart = Convert.ToDateTime(row["WorkDate"]).Date + ((TimeSpan)shiftInfo["MiddayBreakStart"]),
 MiddayBreakEnd = Convert.ToDateTime(row["WorkDate"]).Date + ((TimeSpan)shiftInfo["MiddayBreakEnd"]),
 MorningRestStart = Convert.ToDateTime(row["WorkDate"]).Date + ((TimeSpan)shiftInfo["MorningRestStart"]),
 MorningRestEnd = Convert.ToDateTime(row["WorkDate"]).Date + ((TimeSpan)shiftInfo["MorningRestEnd"]),
 AfternoonRestStart = Convert.ToDateTime(row["WorkDate"]).Date + ((TimeSpan)shiftInfo["AfternoonRestStart"]),
 AfternoonRestEnd = Convert.ToDateTime(row["WorkDate"]).Date + ((TimeSpan)shiftInfo["AfternoonRestEnd"])
 };
 workdaysToInsert.Add(newDay);
 }

 // 使用 Bulk Copy 提高寫入效率
 try
 {
 // 這裡假設 SqlUtil 有一個 BulkInsert 方法，如果沒有需要實作
 // SqlUtil.BulkInsert(ConnectionString, "EmpWorkday", workdaysToInsert);

 // 如果沒有 BulkInsert, 退回逐筆新增
 foreach (var day in workdaysToInsert)
 {
 Add(userId, ip, day);
 }

 Utility.EventLog(41, userId, $"成功建立員工 {empNo} 在 {year} 年的年度工作日", ip, ConnectionString);
 return true;
 }
 catch (Exception ex)
 {
 Utility.EventLog(41, userId, $"批次寫入員工年度工作日時發生錯誤: {ex.Message}", ip, ConnectionString);
 return false;
 }
 }

 /// <summary>
 /// 新增或更新單一員工工作日資料
 /// </summary>
 /// <param name="userId">執行操作的使用者ID</param>
 /// <param name="ip">使用者IP位址</param>
 /// <param name="empWorkday">要新增的員工工作日物件</param>
 /// <returns>true 表示新增成功，false 表示新增失敗</returns>
 public bool AddOrUpdate(string userId, string ip, EmpWorkday empWorkday)
 {
 string sqlstr = "SELECT COUNT(*) FROM dbo.EmpWorkday WHERE EmpNo = @EmpNo AND WorkDate = @WorkDate;";
 SqlParameter[] parameters = new SqlParameter[2];
 parameters[0] = new SqlParameter("@EmpNo", SqlDbType.NVarChar, 20);
 parameters[0].Value = empWorkday.EmpNo;
 parameters[1] = new SqlParameter("@WorkDate", SqlDbType.Date);
 parameters[1].Value = empWorkday.WorkDate.Date;

 int count = (int)SqlUtil.ExecuteSqlScalar(ConnectionString, sqlstr, parameters);

 if (count == 0)
 {
 return Add(userId, ip, empWorkday);
 }
 else
 {
 return Update(userId, ip, empWorkday);
 }
 }

 /// <summary>
 /// 新增單一員工工作日資料
 /// </summary>
 /// <param name="userId">執行操作的使用者ID</param>
 /// <param name="ip">使用者IP位址</param>
 /// <param name="empWorkday">要新增的員工工作日物件</param>
 /// <returns>true 表示新增成功，false 表示新增失敗</returns>
 public bool Add(string userId, string ip, EmpWorkday empWorkday)
 {
 string sqlstr = "INSERT INTO dbo.EmpWorkday (EmpNo, ShiftId, WorkDate, DayType, ArrivalTime, DepartureTime, FlexibleArrivalBefore, FlexibleArrivalAfter, FlexibleDepartureBefore, FlexibleDepartureAfter, MiddayBreakStart, MiddayBreakEnd, MorningRestStart, MorningRestEnd, AfternoonRestStart, AfternoonRestEnd, WorkHours, WeekDay, Comment) VALUES (@EmpNo, @ShiftId, @WorkDate, @DayType, @ArrivalTime, @DepartureTime, @FlexibleArrivalBefore, @FlexibleArrivalAfter, @FlexibleDepartureBefore, @FlexibleDepartureAfter, @MiddayBreakStart, @MiddayBreakEnd, @MorningRestStart, @MorningRestEnd, @AfternoonRestStart, @AfternoonRestEnd, @WorkHours, @WeekDay, @Comment);";

 int iRet = ExecuteNonQuery(sqlstr, empWorkday);

 if (iRet == 1)
 {
 Utility.EventLog(41, userId, $"新增員工工作日成功: {JsonConvert.SerializeObject(empWorkday)}", ip, ConnectionString);
 return true;
 }
 else
 {
 Utility.EventLog(41, userId, $"新增員工工作日失敗: {JsonConvert.SerializeObject(empWorkday)}", ip, ConnectionString);
 return false;
 }
 }

 /// <summary>
 /// 修改員工工作日資料
 /// </summary>
 /// <param name="userId">執行操作的使用者ID</param>
 /// <param name="ip">使用者IP位址</param>
 /// <param name="empWorkday">要修改的員工工作日物件</param>
 /// <returns>true 表示修改成功，false 表示修改失敗</returns>
 public bool Update(string userId, string ip, EmpWorkday empWorkday)
 {
 // 驗證使用者權限 (此處簡化)
 if (!Utility.IsAdmin(userId, ConnectionString))
 {
 Utility.EventLog(42, userId, $"修改員工工作日失敗: 使用者 {userId} 並非管理員", ip, ConnectionString);
 return false;
 }

 string sqlstr = "UPDATE dbo.EmpWorkday SET ShiftId=@ShiftId, DayType=@DayType, ArrivalTime=@ArrivalTime, DepartureTime=@DepartureTime, FlexibleArrivalBefore=@FlexibleArrivalBefore, FlexibleArrivalAfter=@FlexibleArrivalAfter, FlexibleDepartureBefore=@FlexibleDepartureBefore, FlexibleDepartureAfter=@FlexibleDepartureAfter, MiddayBreakStart=@MiddayBreakStart, MiddayBreakEnd=@MiddayBreakEnd, MorningRestStart=@MorningRestStart, MorningRestEnd=@MorningRestEnd, AfternoonRestStart=@AfternoonRestStart, AfternoonRestEnd=@AfternoonRestEnd, WorkHours=@WorkHours, WeekDay=@WeekDay, Comment=@Comment WHERE EmpNo=@EmpNo AND WorkDate=@WorkDate;";

 int iRet = ExecuteNonQuery(sqlstr, empWorkday);

 if (iRet == 1)
 {
 Utility.EventLog(42, userId, $"修改員工工作日成功: {JsonConvert.SerializeObject(empWorkday)}", ip, ConnectionString);
 return true;
 }
 else
 {
 Utility.EventLog(42, userId, $"修改員工工作日失敗: {JsonConvert.SerializeObject(empWorkday)}", ip, ConnectionString);
 return false;
 }
 }

 /// <summary>
 /// 刪除指定員工在特定日期的工作日資料
 /// </summary>
 /// <param name="userId">執行操作的使用者ID</param>
 /// <param name="ip">使用者IP位址</param>
 /// <param name="empNo">員工編號</param>
 /// <param name="workDate">工作日期</param>
 /// <returns>true 表示刪除成功，false 表示刪除失敗</returns>
 public bool Delete(string userId, string ip, string empNo, DateTime workDate)
 {
 // 驗證使用者權限 (此處簡化)
 if (!Utility.IsAdmin(userId, ConnectionString))
 {
 Utility.EventLog(43, userId, $"刪除員工工作日失敗: 使用者 {userId} 並非管理員", ip, ConnectionString);
 return false;
 }

 string sqlstr = "DELETE FROM dbo.EmpWorkday WHERE EmpNo=@EmpNo AND WorkDate=@WorkDate;";
 SqlParameter[] parameters = new SqlParameter[2];
 parameters[0] = new SqlParameter("@EmpNo", SqlDbType.NVarChar, 20);
 parameters[0].Value = empNo;
 parameters[1] = new SqlParameter("@WorkDate", SqlDbType.Date);
 parameters[1].Value = workDate.Date;

 int iRet = SqlUtil.ExecuteSqlNonQuery(ConnectionString, sqlstr, parameters);

 if (iRet == 1)
 {
 Utility.EventLog(43, userId, $"刪除員工工作日成功: EmpNo={empNo}, WorkDate={workDate.ToShortDateString()}", ip, ConnectionString);
 return true;
 }
 else
 {
 Utility.EventLog(43, userId, $"刪除員工工作日失敗: EmpNo={empNo}, WorkDate={workDate.ToShortDateString()}", ip, ConnectionString);
 return false;
 }
 }

 /// <summary>
 /// 執行 SQL 非查詢命令 (INSERT, UPDATE, DELETE)
 /// </summary>
 private int ExecuteNonQuery(string sqlstr, EmpWorkday empWorkday)
 {
 SqlParameter[] parameters = SetParameters(empWorkday);
 return SqlUtil.ExecuteSqlNonQuery(ConnectionString, sqlstr, parameters);
 }

 /// <summary>
 /// 將 EmpWorkday 物件轉換為 SQL 參數陣列
 /// </summary>
 private SqlParameter[] SetParameters(EmpWorkday empWorkday)
 {
 return new SqlParameter[]
 {
 new SqlParameter("@EmpNo", SqlDbType.NVarChar, 20) { Value = empWorkday.EmpNo },
 new SqlParameter("@ShiftId", SqlDbType.SmallInt) { Value = empWorkday.ShiftId },
 new SqlParameter("@WorkDate", SqlDbType.Date) { Value = empWorkday.WorkDate.Date },
 new SqlParameter("@DayType", SqlDbType.TinyInt) { Value = empWorkday.DayType },
 new SqlParameter("@ArrivalTime", SqlDbType.SmallDateTime) { Value = empWorkday.ArrivalTime },
 new SqlParameter("@DepartureTime", SqlDbType.SmallDateTime) { Value = empWorkday.DepartureTime },
 new SqlParameter("@FlexibleArrivalBefore", SqlDbType.SmallDateTime) { Value = empWorkday.FlexibleArrivalBefore },
 new SqlParameter("@FlexibleArrivalAfter", SqlDbType.SmallDateTime) { Value = empWorkday.FlexibleArrivalAfter },
 new SqlParameter("@FlexibleDepartureBefore", SqlDbType.SmallDateTime) { Value = empWorkday.FlexibleDepartureBefore },
 new SqlParameter("@FlexibleDepartureAfter", SqlDbType.SmallDateTime) { Value = empWorkday.FlexibleDepartureAfter },
 new SqlParameter("@MiddayBreakStart", SqlDbType.SmallDateTime) { Value = empWorkday.MiddayBreakStart },
 new SqlParameter("@MiddayBreakEnd", SqlDbType.SmallDateTime) { Value = empWorkday.MiddayBreakEnd },
 new SqlParameter("@MorningRestStart", SqlDbType.SmallDateTime) { Value = empWorkday.MorningRestStart },
 new SqlParameter("@MorningRestEnd", SqlDbType.SmallDateTime) { Value = empWorkday.MorningRestEnd },
 new SqlParameter("@AfternoonRestStart", SqlDbType.SmallDateTime) { Value = empWorkday.AfternoonRestStart },
 new SqlParameter("@AfternoonRestEnd", SqlDbType.SmallDateTime) { Value = empWorkday.AfternoonRestEnd },
 new SqlParameter("@WorkHours", SqlDbType.Float) { Value = empWorkday.WorkHours },
 new SqlParameter("@WeekDay", SqlDbType.TinyInt) { Value = empWorkday.WeekDay },
 new SqlParameter("@Comment", SqlDbType.NVarChar, 512) { Value = (object)empWorkday.Comment ?? DBNull.Value }
 };
 }
 }
}
