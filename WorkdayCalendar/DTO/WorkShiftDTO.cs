// 引入必要的命名空間
using System;                   // 基礎系統功能
using System.Data;              // ADO.NET 資料存取相關類別

using Microsoft.Data.SqlClient;  // SQL Server 資料庫操作相關功能

using Newtonsoft.Json;          // JSON 序列化和反序列化功能

using WorkdayCalendar.Models;   // 專案中的模型類別

namespace WorkdayCalendar.DTO
{
    /// <summary>
    /// 班別資料傳輸物件 (Data Transfer Object)
    /// 負責處理與 WorkShift 資料表相關的資料庫操作
    /// </summary>
    public class WorkShiftDto
    {
        // 資料庫連接字串
        private string ConnectionString { get; set; }

        /// <summary>
        /// 建構函式，初始化 WorkShiftDto 實例
        /// </summary>
        /// <param name="connectionString">資料庫連接字串</param>
        public WorkShiftDto(string connectionString)
        {
            ConnectionString = connectionString;
        }

        /// <summary>
        /// 取得所有班別資料
        /// </summary>
        /// <returns>包含所有班別資料的 DataTable</returns>
        public DataTable Get()
        {
            string sqlstr = "SELECT * FROM WorkShift;";
            return SqlUtil.GetDataTable(ConnectionString, sqlstr);
        }

        /// <summary>
        /// 取得班別資料
        /// </summary>
        /// <param name="shiftId">班別Id</param>
        /// <returns>包含班別資料的 DataTable</returns>
        public DataTable GetDataTable(int shiftId)
        {
            string sqlstr = "SELECT * FROM WorkShift WHERE ShiftId=@ShiftId;";
            SqlParameter parameter = new SqlParameter("@ShiftId", SqlDbType.SmallInt);
            parameter.Value = shiftId;
            return SqlUtil.GetDataTable(ConnectionString, sqlstr, parameter);
        }

        /// <summary>
        /// 取得班別資料
        /// </summary>
        /// <param name="shiftId">班別Id</param>
        /// <returns>班別 WorkShift</returns>
        public WorkShift Get(int shiftId)
        {
            string sqlstr = "SELECT * FROM WorkShift WHERE ShiftId=@ShiftId;";
            SqlParameter parameter = new SqlParameter("@ShiftId", SqlDbType.SmallInt);
            parameter.Value = shiftId;
            DataTable dt = SqlUtil.GetDataTable(ConnectionString, sqlstr, parameter);
            WorkShift workShift = DataTableToWorkShift(dt);
            return workShift;
        }


        /// <summary>
        /// 將DataTable 轉換為 WorkShift 物件
        /// </summary>
        /// <param name="dt">data table</param>
        /// <returns></returns>
        public static WorkShift DataTableToWorkShift(DataTable dt)
        {
            WorkShift workShift = new WorkShift();
            if (dt.Rows.Count > 0)
            {
                DataRow row = dt.Rows[0];
                workShift.ShiftId = Convert.ToInt16(row["ShiftId"]);
                workShift.ShiftName = row["ShiftName"].ToString();
                workShift.Workdays = row["Workdays"] as string;
                workShift.RestDay = Convert.ToInt32(row["RestDay"]);
                workShift.FixedDayOff = Convert.ToInt32(row["FixedDayOff"]);
                workShift.ArrivalTime = row.Field<TimeSpan?>("ArrivalTime").ToString();
                workShift.DepartureTime = row.Field<TimeSpan?>("DepartureTime").ToString();
                workShift.FlexibleArrivalBefore = row.Field<TimeSpan?>("FlexibleArrivalBefore").ToString();
                workShift.FlexibleArrivalAfter = row.Field<TimeSpan?>("FlexibleArrivalAfter").ToString();
                workShift.FlexibleDepartureBefore = row.Field<TimeSpan?>("FlexibleDepartureBefore").ToString();
                workShift.FlexibleDepartureAfter = row.Field<TimeSpan?>("FlexibleDepartureAfter").ToString();
                workShift.MiddayBreakStart = row.Field<TimeSpan?>("MiddayBreakStart").ToString();
                workShift.MiddayBreakEnd = row.Field<TimeSpan?>("MiddayBreakEnd").ToString();
                workShift.MorningRestStart = row.Field<TimeSpan?>("MorningRestStart").ToString();
                workShift.MorningRestEnd = row.Field<TimeSpan?>("MorningRestEnd").ToString();
                workShift.AfternoonRestStart = row.Field<TimeSpan?>("AfternoonRestStart").ToString();
                workShift.AfternoonRestEnd = row.Field<TimeSpan?>("AfternoonRestEnd").ToString();
                workShift.Comment = row["Comment"] as string;
            }

            return workShift;
        }

        /// <summary>
        /// 新增班別資料
        /// </summary>
        /// <param name="userId">執行操作的使用者 ID</param>
        /// <param name="ip">執行操作的 IP 位址</param>
        /// <param name="workShift">要新增的班別資料</param>
        /// <returns>新增成功回傳班別 ID，失敗回傳 -1</returns>
        /// <remarks>只有管理員可以執行此操作</remarks>
        public int Add(string userId, string ip, WorkShift workShift)
        {
            int ret = -1;
            if (Utility.IsAdmin(userId, ConnectionString))
            {
                string sqlstr = "INSERT INTO dbo.WorkShift (ShiftName,Workdays,RestDay,FixedDayOff,ArrivalTime,DepartureTime,FlexibleArrivalBefore,FlexibleArrivalAfter,FlexibleDepartureBefore,FlexibleDepartureAfter,MiddayBreakStart,MiddayBreakEnd,MorningRestStart,MorningRestEnd,AfternoonRestStart,AfternoonRestEnd,Comment) VALUES (@ShiftName, @Workdays,@RestDay,@FixedDayOff, @ArrivalTime,@DepartureTime,@FlexibleArrivalBefore,@FlexibleArrivalAfter,@FlexibleDepartureBefore,@FlexibleDepartureAfter,@MiddayBreakStart,@MiddayBreakEnd,@MorningRestStart,@MorningRestEnd,@AfternoonRestStart,@AfternoonRestEnd,@Comment);";
                int iRet = ExecuteNonQuery(sqlstr, workShift);
                if (iRet == 1) //成功
                {
                    sqlstr = "SELECT ShiftId FROM dbo.WorkShift WHERE ShiftName=@ShiftName;";
                    SqlParameter parameter = new SqlParameter("@ShiftName", SqlDbType.NVarChar, 50);
                    parameter.Value = workShift.ShiftName;
                    ret = int.Parse(SqlUtil.ExecuteSqlScalar(ConnectionString, sqlstr, parameter).ToString());
                    Utility.EventLog(11, userId, $"建立班別成功: {JsonConvert.SerializeObject(workShift)}", ip, ConnectionString);
                    workShift.ShiftId = (short)iRet;
                }
                else
                {
                    Utility.EventLog(11, userId, $"建立班別失敗: {JsonConvert.SerializeObject(workShift)}", ip, ConnectionString);
                }
            }
            else
            {
                Utility.EventLog(11, userId, $"建立班別失敗: 使用者 {userId} 並非管理員", ip, ConnectionString);
            }
            return ret;
        }

        /// <summary>
        /// 更新班別資料
        /// </summary>
        /// <param name="userId">執行操作的使用者 ID</param>
        /// <param name="ip">執行操作的 IP 位址</param>
        /// <param name="workShift">要更新的班別資料</param>
        /// <returns>更新成功回傳 true，否則回傳 false</returns>
        /// <remarks>只有管理員可以執行此操作</remarks>
        public bool Update(string userId, string ip, WorkShift workShift)
        {
            bool ret = false;
            if (Utility.IsAdmin(userId, ConnectionString))
            {
                string sqlstr = "UPDATE dbo.WorkShift SET ShiftName=@ShiftName,ArrivalTime=@ArrivalTime,Workdays=@Workdays,RestDay=@RestDay,FixedDayOff=@FixedDayOff,DepartureTime=@DepartureTime,FlexibleArrivalBefore=@FlexibleArrivalBefore,FlexibleArrivalAfter=@FlexibleArrivalAfter,FlexibleDepartureBefore=@FlexibleDepartureBefore,FlexibleDepartureAfter=@FlexibleDepartureAfter,MiddayBreakStart=@MiddayBreakStart,MiddayBreakEnd=@MiddayBreakEnd,MorningRestStart=@MorningRestStart,MorningRestEnd=@MorningRestEnd,AfternoonRestStart=@AfternoonRestStart,AfternoonRestEnd=@AfternoonRestEnd,Comment=@Comment WHERE ShiftId = @ShiftId";
                int iRet = ExecuteNonQuery(sqlstr, workShift);
                if (iRet == 1) //成功
                {
                    ret = true;
                    Utility.EventLog(12, userId, $"修改班別成功: {JsonConvert.SerializeObject(workShift)}", ip, ConnectionString);
                }
                else
                {
                    Utility.EventLog(12, userId, $"修改班別失敗: {JsonConvert.SerializeObject(workShift)}", ip, ConnectionString);
                }
            }
            else
            {
                Utility.EventLog(12, userId, $"修改班別失敗: 使用者 {userId} 並非管理員", ip, ConnectionString);
            }
            return ret;
        }

        /// <summary>
        /// 刪除指定 ID 的班別
        /// </summary>
        /// <param name="userId">執行操作的使用者 ID</param>
        /// <param name="ip">執行操作的 IP 位址</param>
        /// <param name="shiftId">要刪除的班別 ID</param>
        /// <returns>刪除成功回傳 true，否則回傳 false</returns>
        /// <remarks>只有管理員可以執行此操作</remarks>
        public bool Delete(string userId, string ip, int shiftId)
        {
            bool ret = false;
            if (Utility.IsAdmin(userId, ConnectionString))
            {
                string sqlstr = "DELETE FROM dbo.WorkShift WHERE ShiftId=@ShiftId;";
                SqlParameter parameter = new SqlParameter("@ShiftId", SqlDbType.SmallInt);
                parameter.Value = shiftId;
                int iRet = SqlUtil.ExecuteSqlNonQuery(ConnectionString, sqlstr, parameter);
                if (iRet == 1)
                {
                    //"刪除成功";
                    ret = true;
                    Utility.EventLog(13, userId, $"刪除班別成功: ShiftId = {shiftId}", ip, ConnectionString);
                }
                else
                {
                    //"刪除失敗";
                    Utility.EventLog(13, userId, $"刪除班別失敗: ShiftId = {shiftId}", ip, ConnectionString);
                }
            }
            else
            {
                Utility.EventLog(13, userId, $"刪除班別失敗: 使用者 {userId} 並非管理員", ip, ConnectionString);
            }
            return ret;
        }

        /// <summary>
        /// 執行非查詢 SQL 語句
        /// </summary>
        /// <param name="sqlstr">要執行的 SQL 語句</param>
        /// <param name="workShift">包含參數值的班別物件</param>
        /// <returns>受影響的資料列數</returns>
        private int ExecuteNonQuery(string sqlstr, WorkShift workShift)
        {
            SqlParameter[] parameters = SetParameters(workShift);
            int iRet = SqlUtil.ExecuteSqlNonQuery(ConnectionString, sqlstr, parameters);
            return iRet;
        }

        /// <summary>
        /// 設定 SQL 參數
        /// </summary>
        /// <param name="workShift">包含參數值的班別物件</param>
        /// <returns>設定好的 SqlParameter 陣列</returns>
        private SqlParameter[] SetParameters(WorkShift workShift)
        {
            SqlParameter[] parameters = new SqlParameter[18];
            parameters[0] = new SqlParameter("@ShiftName", SqlDbType.NVarChar, 50);
            parameters[0].Value = workShift.ShiftName;
            parameters[1] = new SqlParameter("@RestDay", SqlDbType.Int);
            parameters[1].Value = workShift.RestDay;
            parameters[2] = new SqlParameter("@Workdays", SqlDbType.VarChar, 14);
            if (string.IsNullOrWhiteSpace(workShift.Workdays))
            {
                parameters[2].Value = DBNull.Value;
            }
            else
            {
                parameters[2].Value = workShift.Workdays;
            }
            parameters[3] = new SqlParameter("@FixedDayOff", SqlDbType.Int);
            parameters[3].Value = workShift.FixedDayOff;
            parameters[4] = new SqlParameter("@ArrivalTime", SqlDbType.Time, 0);
            parameters[4].Value = workShift.ArrivalTime;
            parameters[5] = new SqlParameter("@DepartureTime", SqlDbType.Time, 0);
            parameters[5].Value = workShift.DepartureTime;
            parameters[6] = new SqlParameter("@FlexibleArrivalBefore", SqlDbType.Time, 0);
            parameters[6].Value = workShift.FlexibleArrivalBefore;
            parameters[7] = new SqlParameter("@FlexibleArrivalAfter", SqlDbType.Time, 0);
            parameters[7].Value = workShift.FlexibleArrivalAfter;
            parameters[8] = new SqlParameter("@FlexibleDepartureBefore", SqlDbType.Time, 0);
            parameters[8].Value = workShift.FlexibleDepartureBefore;
            parameters[9] = new SqlParameter("@FlexibleDepartureAfter", SqlDbType.Time, 0);
            parameters[9].Value = workShift.FlexibleDepartureAfter;
            parameters[10] = new SqlParameter("@MiddayBreakStart", SqlDbType.Time, 0);
            parameters[10].Value = workShift.MiddayBreakStart;
            parameters[11] = new SqlParameter("@MiddayBreakEnd", SqlDbType.Time, 0);
            parameters[11].Value = workShift.MiddayBreakEnd;
            parameters[12] = new SqlParameter("@MorningRestStart", SqlDbType.Time, 0);
            parameters[12].Value = workShift.MorningRestStart;
            parameters[13] = new SqlParameter("@MorningRestEnd", SqlDbType.Time, 0);
            parameters[13].Value = workShift.MorningRestEnd;
            parameters[14] = new SqlParameter("@AfternoonRestStart", SqlDbType.Time, 0);
            parameters[14].Value = workShift.AfternoonRestStart;
            parameters[15] = new SqlParameter("@AfternoonRestEnd", SqlDbType.Time, 0);
            parameters[15].Value = workShift.AfternoonRestEnd;
            parameters[16] = new SqlParameter("@Comment", SqlDbType.NVarChar, 512);
            if (string.IsNullOrWhiteSpace(workShift.Comment))
            {
                parameters[16].Value = DBNull.Value;
            }
            else
            {
                parameters[16].Value = workShift.Comment;
            }
            parameters[17] = new SqlParameter("@ShiftId", SqlDbType.SmallInt);
            parameters[17].Value = workShift.ShiftId;
            return parameters;
        }
    }
}
