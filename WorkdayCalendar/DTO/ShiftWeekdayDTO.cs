using Microsoft.Data.SqlClient;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using WorkdayCalendar.Models;

namespace WorkdayCalendar.DTO
{
    /// <summary>
    /// 班別週間日資料傳輸物件
    /// </summary>
    public class ShiftWeekdayDto
    {
        /// <summary>
        /// 資料庫連接字串
        /// </summary>
        private string ConnectionString { get; set; }

        /// <summary>
        /// 班別週間日建構子
        /// </summary>
        /// <param name="connectionString">資料庫連接字串</param>
        public ShiftWeekdayDto(string connectionString)
        {
            ConnectionString = connectionString;
        }

        /// <summary>
        /// 取得所有班別各週間日預設時間
        /// </summary>
        /// <returns>包含所有班別週間日設定的DataTable</returns>
        /// <remarks>
        /// 查詢結果包含以下欄位:
        /// ShiftId(班別ID), WeekDay(星期幾), ArrivalTime(上班時間), DepartureTime(下班時間),
        /// FlexibleArrivalBefore(彈性上班前時間), FlexibleArrivalAfter(彈性上班後時間),
        /// FlexibleDepartureBefore(彈性下班前時間), FlexibleDepartureAfter(彈性下班後時間),
        /// MiddayBreakStart(午休開始時間), MiddayBreakEnd(午休結束時間),
        /// MorningRestStart(上午休息開始), MorningRestEnd(上午休息結束),
        /// AfternoonRestStart(下午休息開始), AfternoonRestEnd(下午休息結束),
        /// WorkHours(工作時數), Comment(備註), ShiftName(班別名稱)
        /// </remarks>
        public DataTable GetAll()
        {
            string sqlstr = "SELECT ShiftWeekday.ShiftId, ShiftWeekday.WeekDay, ShiftWeekday.ArrivalTime, ShiftWeekday.DepartureTime,  ShiftWeekday.FlexibleArrivalBefore, ShiftWeekday.FlexibleArrivalAfter,ShiftWeekday.FlexibleDepartureBefore,ShiftWeekday.FlexibleDepartureAfter,ShiftWeekday.MiddayBreakStart, ShiftWeekday.MiddayBreakEnd,  ShiftWeekday.MorningRestStart, ShiftWeekday.MorningRestEnd, ShiftWeekday.AfternoonRestStart, ShiftWeekday.AfternoonRestEnd,  ShiftWeekday.WorkHours, ShiftWeekday.Comment, WorkShift.ShiftName FROM ShiftWeekday INNER JOIN WorkShift ON ShiftWeekday.ShiftId = WorkShift.ShiftId;";
            return SqlUtil.GetDataTable(ConnectionString, sqlstr);
        }

        /// <summary>
        ///  取得指定班別各週間日預設時間
        /// </summary>
        /// <param name="shiftId">班別ID</param>
        /// <returns>包含指定班別週間日設定的DataTable</returns>
        /// <remarks>
        /// 查詢結果包含以下欄位:
        /// ShiftId(班別ID), WeekDay(星期幾), ArrivalTime(上班時間), DepartureTime(下班時間),
        /// FlexibleArrivalBefore(彈性上班前時間), FlexibleArrivalAfter(彈性上班後時間),
        /// FlexibleDepartureBefore(彈性下班前時間), FlexibleDepartureAfter(彈性下班後時間),
        /// MiddayBreakStart(午休開始時間), MiddayBreakEnd(午休結束時間),
        /// MorningRestStart(上午休息開始), MorningRestEnd(上午休息結束),
        /// AfternoonRestStart(下午休息開始), AfternoonRestEnd(下午休息結束),
        /// WorkHours(工作時數), Comment(備註), ShiftName(班別名稱)
        /// </remarks>
        public DataTable GetDataTable(int shiftId)
        {
            string sqlstr = "SELECT ShiftWeekday.ShiftId, ShiftWeekday.WeekDay, ShiftWeekday.ArrivalTime, ShiftWeekday.DepartureTime,  ShiftWeekday.FlexibleArrivalBefore, ShiftWeekday.FlexibleArrivalAfter,ShiftWeekday.FlexibleDepartureBefore,ShiftWeekday.FlexibleDepartureAfter,ShiftWeekday.MiddayBreakStart, ShiftWeekday.MiddayBreakEnd,  ShiftWeekday.MorningRestStart, ShiftWeekday.MorningRestEnd, ShiftWeekday.AfternoonRestStart, ShiftWeekday.AfternoonRestEnd,  ShiftWeekday.WorkHours, ShiftWeekday.Comment, WorkShift.ShiftName FROM ShiftWeekday INNER JOIN WorkShift ON ShiftWeekday.ShiftId = WorkShift.ShiftId WHERE ShiftWeekday.ShiftId=@ShiftId;";
            SqlParameter[] parameters = [new SqlParameter("@ShiftId", SqlDbType.SmallInt)];
            parameters[0].Value = shiftId;
            DataTable dt = SqlUtil.GetDataTable(ConnectionString, sqlstr);
            return dt;
        }

        /// <summary>
        /// 取得所有國定假日預設屬性
        /// </summary>
        /// <returns>包含所有國定假日設定的DataTable</returns>
        /// <remarks>
        /// 查詢結果包含以下欄位:
        /// Date(日期,格式為MMDD), IsLunar(是否為農曆日期),
        /// Name(假日名稱), DayOff(是否為休假日)
        /// </remarks>
        public DataTable GetAllHolidaysDataTable()
        {
            string sqlstr = "SELECT Date, IsLunar, Name, DayOff FROM dbo.Holiday ORDER BY Date;";
            return SqlUtil.GetDataTable(ConnectionString, sqlstr);
        }


        public List<Holiday> GetHolidays(int year)
        {
            List<Holiday> holidays = new List<Holiday>();
            DataTable dt = Holidays(year);
            //foreach (DataRow dr in dt.Rows)
            //{
            //    Holiday holiday = new Holiday
            //    {
            //        Date = dr["Date"].ToString(),
            //        IsLunar = (bool)dr["IsLunar"],
            //        Name = dr["Name"].ToString(),
            //        DayOff = (bool)dr["DayOff"]
            //    };
            //    holidays.Add(holiday);
            //}
            return holidays;
        }

        /// <summary>
        /// 取得某年內所有休假的國定假日
        /// </summary>
        /// <param name="year">要查詢的年份</param>
        /// <returns>包含指定年份所有休假日期的DataTable</returns>
        /// <remarks>
        /// 此方法會將農曆日期轉換為西曆日期
        /// 特別處理大年夜(1230)和小年夜(1229)的日期轉換
        /// 返回的DataTable包含轉換後的DateTime格式日期
        /// </remarks>
        public DataTable Holidays(int year)
        {
            string sqlstr = "SELECT Date, IsLunar, Name FROM dbo.Holiday WHERE DayOff=1;";
            DataTable dt = SqlUtil.GetDataTable(ConnectionString, sqlstr);
            dt.Columns["Date"].ColumnName = "OrigDate";
            dt.Columns.Add(new DataColumn("Date", Type.GetType("System.DateTime")));

            foreach (DataRow dr in dt.Rows)
            {
                string dateString = (string)dr["OrigDate"];
                bool isLunar = (bool)dr["IsLunar"];
                int month = int.Parse(dateString.Substring(0, 2));
                int day = int.Parse(dateString.Substring(2, 2));
                DateTime date = new DateTime(year, month, day);
                if (isLunar)
                {
                    if (dateString == "1230") // 大年夜有時候是 12/30，有時候是12/29，所以...
                    {
                        date = Utility.LunarToSolar(year, 1, 1).AddDays(-1);
                    }
                    else if (dateString == "1229") // 小年夜有時候是 12/29，有時候是12/28，所以...
                    {
                        date = Utility.LunarToSolar(year, 1, 1).AddDays(-2);
                    }
                    else
                    {
                        date = Utility.LunarToSolar(year, month, day);
                    }
                }
                dr["Date"] = date;
            }

            dt.Columns.Remove("OrigDate");
            dt.DefaultView.Sort = "Date ASC";
            dt = dt.DefaultView.ToTable();
            return dt;
        }

        /// <summary>
        /// 將 DataTable 轉換為 ShiftWeekday 物件清單
        /// </summary>
        /// <param name="dataTable">包含班別週間日資料的 DataTable</param>
        /// <returns>轉換後的 ShiftWeekday 物件清單</returns>
        /// <remarks>
        /// 此方法會將 DataTable 中的每一列資料轉換為對應的 ShiftWeekday 物件
        /// 支援的欄位包含：ShiftId, ShiftName, WeekDay, ArrivalTime, DepartureTime,
        /// FlexibleArrivalBefore, FlexibleArrivalAfter, FlexibleDepartureBefore, FlexibleDepartureAfter,
        /// MiddayBreakStart, MiddayBreakEnd, MorningRestStart, MorningRestEnd,
        /// AfternoonRestStart, AfternoonRestEnd, Comment
        /// 如果某個欄位在 DataTable 中不存在或為 null，則會設定為預設值或空字串
        /// </remarks>
        public static List<ShiftWeekday> DataTableToShiftWeekday(DataTable dataTable)
        {
            List<ShiftWeekday> shiftWeekdays = new List<ShiftWeekday>();

            if (dataTable == null || dataTable.Rows.Count == 0)
            {
                return shiftWeekdays;
            }

            foreach (DataRow row in dataTable.Rows)
            {
                ShiftWeekday shiftWeekday = new ShiftWeekday
                {
                    ShiftId = row["ShiftId"] != DBNull.Value ? Convert.ToInt32(row["ShiftId"]) : 0,
                    ShiftName = row.Table.Columns.Contains("ShiftName") && row["ShiftName"] != DBNull.Value
                        ? row["ShiftName"].ToString() : string.Empty,
                    WeekDay = row["WeekDay"] != DBNull.Value ? Convert.ToInt32(row["WeekDay"]) : 0,
                    ArrivalTime = row["ArrivalTime"] != DBNull.Value ? row["ArrivalTime"].ToString() : string.Empty,
                    DepartureTime = row["DepartureTime"] != DBNull.Value ? row["DepartureTime"].ToString() : string.Empty,
                    FlexibleArrivalBefore = row.Table.Columns.Contains("FlexibleArrivalBefore") && row["FlexibleArrivalBefore"] != DBNull.Value
                        ? row["FlexibleArrivalBefore"].ToString() : string.Empty,
                    FlexibleArrivalAfter = row.Table.Columns.Contains("FlexibleArrivalAfter") && row["FlexibleArrivalAfter"] != DBNull.Value
                        ? row["FlexibleArrivalAfter"].ToString() : string.Empty,
                    FlexibleDepartureBefore = row.Table.Columns.Contains("FlexibleDepartureBefore") && row["FlexibleDepartureBefore"] != DBNull.Value
                        ? row["FlexibleDepartureBefore"].ToString() : string.Empty,
                    FlexibleDepartureAfter = row.Table.Columns.Contains("FlexibleDepartureAfter") && row["FlexibleDepartureAfter"] != DBNull.Value
                        ? row["FlexibleDepartureAfter"].ToString() : string.Empty,
                    MiddayBreakStart = row.Table.Columns.Contains("MiddayBreakStart") && row["MiddayBreakStart"] != DBNull.Value
                        ? row["MiddayBreakStart"].ToString() : string.Empty,
                    MiddayBreakEnd = row.Table.Columns.Contains("MiddayBreakEnd") && row["MiddayBreakEnd"] != DBNull.Value
                        ? row["MiddayBreakEnd"].ToString() : string.Empty,
                    MorningRestStart = row.Table.Columns.Contains("MorningRestStart") && row["MorningRestStart"] != DBNull.Value
                        ? row["MorningRestStart"].ToString() : string.Empty,
                    MorningRestEnd = row.Table.Columns.Contains("MorningRestEnd") && row["MorningRestEnd"] != DBNull.Value
                        ? row["MorningRestEnd"].ToString() : string.Empty,
                    AfternoonRestStart = row.Table.Columns.Contains("AfternoonRestStart") && row["AfternoonRestStart"] != DBNull.Value
                        ? row["AfternoonRestStart"].ToString() : string.Empty,
                    AfternoonRestEnd = row.Table.Columns.Contains("AfternoonRestEnd") && row["AfternoonRestEnd"] != DBNull.Value
                        ? row["AfternoonRestEnd"].ToString() : string.Empty,
                    Comment = row.Table.Columns.Contains("Comment") && row["Comment"] != DBNull.Value
                        ? row["Comment"].ToString() : string.Empty
                };

                shiftWeekdays.Add(shiftWeekday);
            }

            return shiftWeekdays;
        }

        /// <summary>
        /// 新增一筆班別週間日設定
        /// </summary>
        /// <param name="userId">執行操作的使用者ID</param>
        /// <param name="ip">使用者IP位址，用於記錄日誌</param>
        /// <param name="yearCalendar">要新增的班別週間日設定物件</param>
        /// <returns>新增成功傳回true，否則傳回false</returns>
        /// <remarks>
        /// 此方法會檢查使用者是否為管理員，只有管理員可以新增班別週間日設定
        /// 操作結果會記錄在事件日誌中，包含成功或失敗的詳細資訊
        /// </remarks>
        public bool Add(string userId, string ip, ShiftWeekday yearCalendar)
        {
            bool ret = false;
            if (Utility.IsAdmin(userId, ConnectionString))
            {
                string sqlstr = "INSERT INTO dbo.ShiftWeekday (ShiftId,WeekDay,ArrivalTime,DepartureTime,FlexibleArrivalBefore,FlexibleArrivalAfter,FlexibleDepartureBefore,FlexibleDepartureAfter,MiddayBreakStart,MiddayBreakEnd,MorningRestStart,MorningRestEnd,AfternoonRestStart,AfternoonRestEnd,WorkHours,Comment) VALUES (@ShiftId,@WeekDay,@ArrivalTime,@DepartureTime,@FlexibleArrivalBefore,@FlexibleArrivalAfter,@FlexibleDepartureBefore,@FlexibleDepartureAfter,@MiddayBreakStart,@MiddayBreakEnd ,@MorningRestStart,@MorningRestEnd,@AfternoonRestStart,@AfternoonRestEnd,@WorkHours,@Comment);";
                int iRet = ExecuteNonQuery(sqlstr, yearCalendar);
                if (iRet == 1) //成功
                {
                    ret = true;
                    Utility.EventLog(21, userId, $"建立週間工時成功: {JsonConvert.SerializeObject(yearCalendar)}", ip, ConnectionString);
                }
                else
                {
                    Utility.EventLog(21, userId, $"建立週間工時失敗: {JsonConvert.SerializeObject(yearCalendar)}", ip, ConnectionString);
                }
            }
            else
            {
                Utility.EventLog(21, userId, $"建立週間工時失敗: 使用者 {userId} 並非管理員", ip, ConnectionString);
            }
            return ret;
        }

        /// <summary>
        /// 更新指定的班別週間日設定
        /// </summary>
        /// <param name="userId">執行操作的使用者ID</param>
        /// <param name="ip">使用者IP位址，用於記錄日誌</param>
        /// <param name="yearCalendar">要更新的班別週間日設定物件</param>
        /// <returns>更新成功傳回true，否則傳回false</returns>
        /// <remarks>
        /// 此方法會根據ShiftId和WeekDay來更新對應的班別週間日設定
        /// 只有管理員可以執行此操作，所有操作都會記錄在事件日誌中
        /// 更新失敗可能是因為指定的記錄不存在或資料庫錯誤
        /// </remarks>
        public bool Update(string userId, string ip, ShiftWeekday yearCalendar)
        {
            bool ret = false;
            if (Utility.IsAdmin(userId, ConnectionString))
            {
                string sqlstr = "UPDATE dbo.ShiftWeekday SET ArrivalTime=@ArrivalTime,DepartureTime=@DepartureTime,FlexibleArrivalBefore=@FlexibleArrivalBefore,FlexibleArrivalAfter=@FlexibleArrivalAfter,FlexibleDepartureBefore=@FlexibleDepartureBefore,FlexibleDepartureAfter=@FlexibleDepartureAfter,MiddayBreakStart=@MiddayBreakStart,MiddayBreakEnd=@MiddayBreakEnd,MorningRestStart=@MorningRestStart,MorningRestEnd=@MorningRestEnd,AfternoonRestStart=@AfternoonRestStart,AfternoonRestEnd=@AfternoonRestEnd,WorkHours=@WorkHours,Comment=@Comment WHERE ShiftId=@ShiftId AND WeekDay=@WeekDay;";
                int iRet = ExecuteNonQuery(sqlstr, yearCalendar);
                if (iRet == 1) //成功
                {
                    ret = true;
                    Utility.EventLog(22, userId, $"修改週間工時成功: {JsonConvert.SerializeObject(yearCalendar)}", ip, ConnectionString);
                }
                else
                {
                    Utility.EventLog(22, userId, $"修改週間工時失敗: {JsonConvert.SerializeObject(yearCalendar)}", ip, ConnectionString);
                }
            }
            else
            {
                Utility.EventLog(22, userId, $"修改週間工時失敗: 使用者 {userId} 並非管理員", ip, ConnectionString);
            }
            return ret;
        }

        /// <summary>
        /// 刪除指定的班別週間日設定
        /// </summary>
        /// <param name="userId">執行操作的使用者ID</param>
        /// <param name="ip">使用者IP位址，用於記錄日誌</param>
        /// <param name="yearCalendar">要刪除的班別週間日設定物件</param>
        /// <returns>刪除成功傳回true，否則傳回false</returns>
        /// <remarks>
        /// 此方法會根據ShiftId和WeekDay來刪除對應的班別週間日設定
        /// 只有管理員可以執行此操作，所有操作都會記錄在事件日誌中
        /// 刪除失敗可能是因為指定的記錄不存在或資料庫錯誤
        /// </remarks>
        public bool Delete(string userId, string ip, ShiftWeekday yearCalendar)
        {
            bool ret = false;
            if (Utility.IsAdmin(userId, ConnectionString))
            {
                string sqlstr = "DELETE FROM dbo.ShiftWeekday WHERE ShiftId=@ShiftId AND WeekDay=@WeekDay;";
                SqlParameter[] parameters = new SqlParameter[2];
                parameters[0] = new SqlParameter("@ShiftId", SqlDbType.SmallInt);
                parameters[0].Value = yearCalendar.ShiftId;
                parameters[1] = new SqlParameter("@WeekDay", SqlDbType.TinyInt);
                parameters[1].Value = yearCalendar.WeekDay;

                int iRet = SqlUtil.ExecuteSqlNonQuery(ConnectionString, sqlstr, parameters);
                if (iRet == 1)
                {
                    //"刪除成功";
                    ret = true;
                    Utility.EventLog(23, userId, $"刪除週間工時成功: ShiftId = {yearCalendar.ShiftId}, WeekDay={yearCalendar.WeekDay}", ip, ConnectionString);
                }
                else
                {
                    //"刪除失敗";
                    Utility.EventLog(23, userId, $"刪除週間工時失敗: ShiftId = {yearCalendar.ShiftId}, WeekDay={yearCalendar.WeekDay}", ip, ConnectionString);
                }
            }
            else
            {
                Utility.EventLog(23, userId, $"刪除週間工時失敗: 使用者 {userId} 並非管理員。 ShiftId = {yearCalendar.ShiftId}, WeekDay={yearCalendar.WeekDay}", ip, ConnectionString);
            }
            return ret;
        }

        /// <summary>
        /// 執行不返回結果集的SQL命令
        /// </summary>
        /// <param name="sqlstr">要執行的SQL語句</param>
        /// <param name="yearCalendar">包含參數值的班別週間日設定物件</param>
        /// <returns>受影響的資料列數</returns>
        /// <remarks>
        /// 此方法會先將ShiftWeekday物件的屬性轉換為SQL參數
        /// 然後使用SqlUtil.ExecuteSqlNonQuery執行SQL命令
        /// 主要用於處理新增、更新和刪除操作
        /// </remarks>
        private int ExecuteNonQuery(string sqlstr, ShiftWeekday yearCalendar)
        {
            SqlParameter[] parameters = SetParameters(yearCalendar);
            int iRet = SqlUtil.ExecuteSqlNonQuery(ConnectionString, sqlstr, parameters);
            return iRet;
        }

        private SqlParameter[] SetParameters(ShiftWeekday shiftWeekday)
        {
            SqlParameter[] parameters = new SqlParameter[16];
            parameters[0] = new SqlParameter("@ShiftId", SqlDbType.SmallInt);
            parameters[0].Value = shiftWeekday.ShiftId;
            parameters[1] = new SqlParameter("@WeekDay", SqlDbType.TinyInt);
            parameters[1].Value = shiftWeekday.WeekDay;
            parameters[2] = new SqlParameter("@ArrivalTime", SqlDbType.Time, 0);
            parameters[2].Value = shiftWeekday.ArrivalTime;
            parameters[3] = new SqlParameter("@DepartureTime", SqlDbType.Time, 0);
            parameters[3].Value = shiftWeekday.DepartureTime;
            parameters[4] = new SqlParameter("@FlexibleArrivalBefore", SqlDbType.Time, 0);
            parameters[4].Value = shiftWeekday.FlexibleArrivalBefore;
            parameters[5] = new SqlParameter("@FlexibleArrivalAfter", SqlDbType.Time, 0);
            parameters[5].Value = shiftWeekday.FlexibleArrivalAfter;
            parameters[6] = new SqlParameter("@FlexibleDepartureBefore", SqlDbType.Time, 0);
            parameters[6].Value = shiftWeekday.FlexibleDepartureBefore;
            parameters[7] = new SqlParameter("@FlexibleDepartureAfter", SqlDbType.Time, 0);
            parameters[7].Value = shiftWeekday.FlexibleDepartureAfter;
            parameters[8] = new SqlParameter("@MiddayBreakStart", SqlDbType.Time, 0);
            parameters[8].Value = shiftWeekday.MiddayBreakStart;
            parameters[9] = new SqlParameter("@MiddayBreakEnd", SqlDbType.Time, 0);
            parameters[9].Value = shiftWeekday.MiddayBreakEnd;
            parameters[10] = new SqlParameter("@MorningRestStart", SqlDbType.Time, 0);
            parameters[10].Value = shiftWeekday.MorningRestStart;
            parameters[11] = new SqlParameter("@MorningRestEnd", SqlDbType.Time, 0);
            parameters[11].Value = shiftWeekday.MorningRestEnd;
            parameters[12] = new SqlParameter("@AfternoonRestStart", SqlDbType.Time, 0);
            parameters[12].Value = shiftWeekday.AfternoonRestStart;
            parameters[13] = new SqlParameter("@AfternoonRestEnd", SqlDbType.Time, 0);
            parameters[13].Value = shiftWeekday.AfternoonRestEnd;

            double workHours = Utility.CalcWorkHours(TimeSpan.Parse(shiftWeekday.ArrivalTime), TimeSpan.Parse(shiftWeekday.DepartureTime), TimeSpan.Parse(shiftWeekday.MiddayBreakStart), TimeSpan.Parse(shiftWeekday.MiddayBreakEnd));
            parameters[14] = new SqlParameter("@WorkHours", SqlDbType.Float);
            parameters[14].Value = workHours;

            parameters[15] = new SqlParameter("@Comment", SqlDbType.NVarChar, 512);
            if (string.IsNullOrWhiteSpace(shiftWeekday.Comment))
            {
                parameters[15].Value = DBNull.Value;
            }
            else
            {
                parameters[15].Value = shiftWeekday.Comment;
            }
            return parameters;
        }

    }
}
