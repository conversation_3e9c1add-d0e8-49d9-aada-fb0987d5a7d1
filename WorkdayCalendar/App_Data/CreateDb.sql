﻿USE [master]
GO
/****** Object:  Database [Workday]    Script Date: 2021/11/29 下午 03:48:56 ******/
CREATE DATABASE [Workday];
GO
USE [Workday]
GO

CREATE TABLE [dbo].[EventLog](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[EmpNo] [varchar](5) NOT NULL,
	[EventTime] [datetime] NOT NULL,
	[EventTypeId] [tinyint] NOT NULL,
	[Description] [nvarchar](1024) NOT NULL,
	[IP] [nvarchar](50) NOT NULL,
 CONSTRAINT [PK_EventLog] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[EventType]    Script Date: 2021/11/29 下午 03:48:56 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[EventType](
	[EventTypeId] [tinyint] NOT NULL,
	[EventType] [nvarchar](50) NOT NULL,
 CONSTRAINT [PK_EventType] PRIMARY KEY CLUSTERED 
(
	[EventTypeId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Holiday]    Script Date: 2021/11/29 下午 03:48:56 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Holiday](
	[Date] [varchar](4) NOT NULL,
	[IsLunar] [bit] NOT NULL,
	[Name] [nvarchar](50) NOT NULL,
	[DayOff] [bit] NOT NULL,
 CONSTRAINT [PK_Holiday_1] PRIMARY KEY CLUSTERED 
(
	[Date] ASC,
	[IsLunar] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ShiftWeekday]    Script Date: 2021/11/29 下午 03:48:56 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ShiftWeekday](
	[ShiftId] [smallint] NOT NULL,
	[WeekDay] [tinyint] NOT NULL,
	[ArrivalTime] [time](0) NOT NULL,
	[DepartureTime] [time](0) NOT NULL,
	[FlexibleArrivalBefore] [time](0) NOT NULL,
	[FlexibleArrivalAfter] [time](0) NOT NULL,
	[FlexibleDepartureBefore] [time](0) NOT NULL,
	[FlexibleDepartureAfter] [time](0) NOT NULL,
	[MiddayBreakStart] [time](0) NOT NULL,
	[MiddayBreakEnd] [time](0) NOT NULL,
	[MorningRestStart] [time](0) NOT NULL,
	[MorningRestEnd] [time](0) NOT NULL,
	[AfternoonRestStart] [time](0) NOT NULL,
	[AfternoonRestEnd] [time](0) NOT NULL,
	[WorkHours] [float] NOT NULL,
	[Comment] [nvarchar](512) NULL,
 CONSTRAINT [PK_YearCalendar] PRIMARY KEY CLUSTERED 
(
	[ShiftId] ASC,
	[WeekDay] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Users]    Script Date: 2021/11/29 下午 03:48:56 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Users](
	[EmpNo] [varchar](5) NOT NULL,
	[IsAdmin] [bit] NOT NULL,
 CONSTRAINT [PK_Users] PRIMARY KEY CLUSTERED 
(
	[EmpNo] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Workday]    Script Date: 2021/11/29 下午 03:48:56 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Workday](
	[ShiftId] [smallint] NOT NULL,
	[WorkDate] [date] NOT NULL,
	[DayType] [tinyint] NOT NULL,
	[ArrivalTime] [smalldatetime] NOT NULL,
	[DepartureTime] [smalldatetime] NOT NULL,
	[FlexibleArrivalBefore] [smalldatetime] NOT NULL,
	[FlexibleArrivalAfter] [smalldatetime] NOT NULL,
	[FlexibleDepartureBefore] [smalldatetime] NOT NULL,
	[FlexibleDepartureAfter] [smalldatetime] NOT NULL,
	[MiddayBreakStart] [smalldatetime] NOT NULL,
	[MiddayBreakEnd] [smalldatetime] NOT NULL,
	[MorningRestStart] [smalldatetime] NOT NULL,
	[MorningRestEnd] [smalldatetime] NOT NULL,
	[AfternoonRestStart] [smalldatetime] NOT NULL,
	[AfternoonRestEnd] [smalldatetime] NOT NULL,
	[WorkHours] [float] NOT NULL,
	[WeekDay] [tinyint] NOT NULL,
	[Comment] [nvarchar](512) NULL,
 CONSTRAINT [PK_Workday] PRIMARY KEY CLUSTERED 
(
	[ShiftId] ASC,
	[WorkDate] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[WorkdayType]    Script Date: 2021/11/29 下午 03:48:56 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[WorkdayType](
	[TypeId] [tinyint] NOT NULL,
	[TypeName] [nvarchar](20) NOT NULL,
	[DayOff] [bit] NULL,
	[DisplayOrder] [tinyint] NULL,
 CONSTRAINT [PK_WorkdayType] PRIMARY KEY CLUSTERED 
(
	[TypeId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[WorkShift]    Script Date: 2021/11/29 下午 03:48:56 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[WorkShift](
	[ShiftId] [smallint] IDENTITY(1,1) NOT NULL,
	[ShiftName] [nvarchar](50) NOT NULL,
	[Workdays] [varchar](14) NOT NULL,
	[ArrivalTime] [time](0) NOT NULL,
	[DepartureTime] [time](0) NOT NULL,
	[FlexibleArrivalBefore] [time](0) NOT NULL,
	[FlexibleArrivalAfter] [time](0) NOT NULL,
	[FlexibleDepartureBefore] [time](0) NOT NULL,
	[FlexibleDepartureAfter] [time](0) NOT NULL,
	[MiddayBreakStart] [time](0) NOT NULL,
	[MiddayBreakEnd] [time](0) NOT NULL,
	[MorningRestStart] [time](0) NOT NULL,
	[MorningRestEnd] [time](0) NOT NULL,
	[AfternoonRestStart] [time](0) NOT NULL,
	[AfternoonRestEnd] [time](0) NOT NULL,
	[Comment] [nvarchar](512) NULL,
 CONSTRAINT [PK_WorkCalendar] PRIMARY KEY CLUSTERED 
(
	[ShiftId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
INSERT [dbo].[EventType] ([EventTypeId], [EventType]) VALUES (11, N'建立班別')
INSERT [dbo].[EventType] ([EventTypeId], [EventType]) VALUES (12, N'修改班別')
INSERT [dbo].[EventType] ([EventTypeId], [EventType]) VALUES (13, N'刪除班別')
INSERT [dbo].[EventType] ([EventTypeId], [EventType]) VALUES (21, N'建立年度行事曆')
INSERT [dbo].[EventType] ([EventTypeId], [EventType]) VALUES (22, N'修改年度行事曆')
INSERT [dbo].[EventType] ([EventTypeId], [EventType]) VALUES (23, N'刪除年度行事曆')
INSERT [dbo].[EventType] ([EventTypeId], [EventType]) VALUES (31, N'建立工作日')
INSERT [dbo].[EventType] ([EventTypeId], [EventType]) VALUES (32, N'修改工作日')
INSERT [dbo].[EventType] ([EventTypeId], [EventType]) VALUES (33, N'刪除工作日')
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'0101', 0, N'元旦', 1)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'0101', 1, N'初一', 1)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'0102', 1, N'初二', 1)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'0103', 1, N'初三', 1)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'0104', 1, N'初四', 1)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'0105', 1, N'初五', 0)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'0228', 0, N'和平紀念日', 1)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'0312', 0, N'國父逝世紀念日', 0)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'0329', 1, N'青年節', 0)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'0404', 0, N'兒童節', 1)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'0405', 0, N'清明節', 1)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'0501', 0, N'勞動節', 1)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'0505', 1, N'端午節', 1)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'0815', 1, N'中秋節', 1)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'0903', 0, N'軍人節', 0)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'0928', 0, N'教師節', 0)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'1010', 0, N'國慶日', 1)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'1024', 0, N'台灣光復節', 0)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'1031', 0, N'蔣公誕辰紀念日', 0)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'1112', 0, N'國父誕辰', 0)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'1225', 0, N'行憲紀念日', 0)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'1229', 1, N'小年夜', 1)
INSERT [dbo].[Holiday] ([Date], [IsLunar], [Name], [DayOff]) VALUES (N'1230', 1, N'除夕', 1)
INSERT [dbo].[ShiftWeekday] ([ShiftId], [WeekDay], [ArrivalTime], [DepartureTime], [FlexibleArrivalBefore], [FlexibleArrivalAfter], [FlexibleDepartureBefore], [FlexibleDepartureAfter], [MiddayBreakStart], [MiddayBreakEnd], [MorningRestStart], [MorningRestEnd], [AfternoonRestStart], [AfternoonRestEnd], [WorkHours], [Comment]) VALUES (1, 1, CAST(N'08:00:00' AS Time), CAST(N'17:00:00' AS Time), CAST(N'07:30:00' AS Time), CAST(N'09:00:00' AS Time), CAST(N'16:30:00' AS Time), CAST(N'18:00:00' AS Time), CAST(N'12:00:00' AS Time), CAST(N'13:00:00' AS Time), CAST(N'10:00:00' AS Time), CAST(N'10:10:00' AS Time), CAST(N'15:00:00' AS Time), CAST(N'15:10:00' AS Time), 8, NULL)
INSERT [dbo].[ShiftWeekday] ([ShiftId], [WeekDay], [ArrivalTime], [DepartureTime], [FlexibleArrivalBefore], [FlexibleArrivalAfter], [FlexibleDepartureBefore], [FlexibleDepartureAfter], [MiddayBreakStart], [MiddayBreakEnd], [MorningRestStart], [MorningRestEnd], [AfternoonRestStart], [AfternoonRestEnd], [WorkHours], [Comment]) VALUES (1, 2, CAST(N'08:00:00' AS Time), CAST(N'17:00:00' AS Time), CAST(N'07:30:00' AS Time), CAST(N'09:00:00' AS Time), CAST(N'16:30:00' AS Time), CAST(N'18:00:00' AS Time), CAST(N'12:00:00' AS Time), CAST(N'13:00:00' AS Time), CAST(N'10:00:00' AS Time), CAST(N'10:10:00' AS Time), CAST(N'15:00:00' AS Time), CAST(N'15:10:00' AS Time), 8, NULL)
INSERT [dbo].[ShiftWeekday] ([ShiftId], [WeekDay], [ArrivalTime], [DepartureTime], [FlexibleArrivalBefore], [FlexibleArrivalAfter], [FlexibleDepartureBefore], [FlexibleDepartureAfter], [MiddayBreakStart], [MiddayBreakEnd], [MorningRestStart], [MorningRestEnd], [AfternoonRestStart], [AfternoonRestEnd], [WorkHours], [Comment]) VALUES (1, 3, CAST(N'08:00:00' AS Time), CAST(N'17:00:00' AS Time), CAST(N'07:30:00' AS Time), CAST(N'09:00:00' AS Time), CAST(N'16:30:00' AS Time), CAST(N'18:00:00' AS Time), CAST(N'12:00:00' AS Time), CAST(N'13:00:00' AS Time), CAST(N'10:00:00' AS Time), CAST(N'10:10:00' AS Time), CAST(N'15:00:00' AS Time), CAST(N'15:10:00' AS Time), 8, NULL)
INSERT [dbo].[ShiftWeekday] ([ShiftId], [WeekDay], [ArrivalTime], [DepartureTime], [FlexibleArrivalBefore], [FlexibleArrivalAfter], [FlexibleDepartureBefore], [FlexibleDepartureAfter], [MiddayBreakStart], [MiddayBreakEnd], [MorningRestStart], [MorningRestEnd], [AfternoonRestStart], [AfternoonRestEnd], [WorkHours], [Comment]) VALUES (1, 4, CAST(N'08:00:00' AS Time), CAST(N'17:00:00' AS Time), CAST(N'07:30:00' AS Time), CAST(N'09:00:00' AS Time), CAST(N'16:30:00' AS Time), CAST(N'18:00:00' AS Time), CAST(N'12:00:00' AS Time), CAST(N'13:00:00' AS Time), CAST(N'10:00:00' AS Time), CAST(N'10:10:00' AS Time), CAST(N'15:00:00' AS Time), CAST(N'15:10:00' AS Time), 8, NULL)
INSERT [dbo].[ShiftWeekday] ([ShiftId], [WeekDay], [ArrivalTime], [DepartureTime], [FlexibleArrivalBefore], [FlexibleArrivalAfter], [FlexibleDepartureBefore], [FlexibleDepartureAfter], [MiddayBreakStart], [MiddayBreakEnd], [MorningRestStart], [MorningRestEnd], [AfternoonRestStart], [AfternoonRestEnd], [WorkHours], [Comment]) VALUES (1, 5, CAST(N'08:00:00' AS Time), CAST(N'17:00:00' AS Time), CAST(N'07:30:00' AS Time), CAST(N'09:00:00' AS Time), CAST(N'16:30:00' AS Time), CAST(N'18:00:00' AS Time), CAST(N'12:00:00' AS Time), CAST(N'13:00:00' AS Time), CAST(N'10:00:00' AS Time), CAST(N'10:10:00' AS Time), CAST(N'15:00:00' AS Time), CAST(N'15:10:00' AS Time), 8, NULL)
INSERT [dbo].[Users] ([EmpNo], [IsAdmin]) VALUES (N'0178', 1)
INSERT [dbo].[Users] ([EmpNo], [IsAdmin]) VALUES (N'0349', 1)
INSERT [dbo].[Users] ([EmpNo], [IsAdmin]) VALUES (N'0395', 1)
INSERT [dbo].[Users] ([EmpNo], [IsAdmin]) VALUES (N'0467', 1)
INSERT [dbo].[WorkdayType] ([TypeId], [TypeName], [DayOff], [DisplayOrder]) VALUES (1, N'週間工作日', 0, 1)
INSERT [dbo].[WorkdayType] ([TypeId], [TypeName], [DayOff], [DisplayOrder]) VALUES (2, N'週六休息日', 1, 2)
INSERT [dbo].[WorkdayType] ([TypeId], [TypeName], [DayOff], [DisplayOrder]) VALUES (3, N'週日例假日', 1, 3)
INSERT [dbo].[WorkdayType] ([TypeId], [TypeName], [DayOff], [DisplayOrder]) VALUES (4, N'週間國定假日', 1, 4)
INSERT [dbo].[WorkdayType] ([TypeId], [TypeName], [DayOff], [DisplayOrder]) VALUES (5, N'週六國定假日', 1, 5)
INSERT [dbo].[WorkdayType] ([TypeId], [TypeName], [DayOff], [DisplayOrder]) VALUES (6, N'週日國定假日', 1, 6)
INSERT [dbo].[WorkdayType] ([TypeId], [TypeName], [DayOff], [DisplayOrder]) VALUES (7, N'補班日', 0, 7)
INSERT [dbo].[WorkdayType] ([TypeId], [TypeName], [DayOff], [DisplayOrder]) VALUES (8, N'補假日', 1, 8)
INSERT [dbo].[WorkdayType] ([TypeId], [TypeName], [DayOff], [DisplayOrder]) VALUES (9, N'彈性放假日', 1, 9)
INSERT [dbo].[WorkdayType] ([TypeId], [TypeName], [DayOff], [DisplayOrder]) VALUES (10, N'天災放假日', 1, 10)
INSERT [dbo].[WorkdayType] ([TypeId], [TypeName], [DayOff], [DisplayOrder]) VALUES (20, N'週間休息日', 1, 99)
SET IDENTITY_INSERT [dbo].[WorkShift] ON 

INSERT [dbo].[WorkShift] ([ShiftId], [ShiftName], [Workdays], [ArrivalTime], [DepartureTime], [FlexibleArrivalBefore], [FlexibleArrivalAfter], [FlexibleDepartureBefore], [FlexibleDepartureAfter], [MiddayBreakStart], [MiddayBreakEnd], [MorningRestStart], [MorningRestEnd], [AfternoonRestStart], [AfternoonRestEnd], [Comment]) VALUES (1, N'正常班', N'1,2,3,4,5', CAST(N'08:00:00' AS Time), CAST(N'17:00:00' AS Time), CAST(N'07:30:00' AS Time), CAST(N'09:00:00' AS Time), CAST(N'16:30:00' AS Time), CAST(N'18:00:00' AS Time), CAST(N'12:00:00' AS Time), CAST(N'13:00:00' AS Time), CAST(N'10:00:00' AS Time), CAST(N'10:10:00' AS Time), CAST(N'15:00:00' AS Time), CAST(N'15:10:00' AS Time), NULL)
SET IDENTITY_INSERT [dbo].[WorkShift] OFF
ALTER TABLE [dbo].[EventLog] ADD  CONSTRAINT [DF_EventLog_EventTime]  DEFAULT (getdate()) FOR [EventTime]
GO
ALTER TABLE [dbo].[Holiday] ADD  CONSTRAINT [DF_Holiday_IsLunar]  DEFAULT ((0)) FOR [IsLunar]
GO
ALTER TABLE [dbo].[Holiday] ADD  CONSTRAINT [DF_Holiday_Off]  DEFAULT ((1)) FOR [DayOff]
GO
ALTER TABLE [dbo].[Users] ADD  CONSTRAINT [DF_Users_IsAdmin]  DEFAULT ((1)) FOR [IsAdmin]
GO
ALTER TABLE [dbo].[WorkShift] ADD  CONSTRAINT [DF_WorkShift_Workdays]  DEFAULT ('1,2,3,4,5') FOR [Workdays]
GO
ALTER TABLE [dbo].[EventLog]  WITH CHECK ADD  CONSTRAINT [FK_EventLog_EventType] FOREIGN KEY([EventTypeId])
REFERENCES [dbo].[EventType] ([EventTypeId])
GO
ALTER TABLE [dbo].[EventLog] CHECK CONSTRAINT [FK_EventLog_EventType]
GO
ALTER TABLE [dbo].[ShiftWeekday]  WITH CHECK ADD  CONSTRAINT [FK_ShiftWeekday_WorkShift] FOREIGN KEY([ShiftId])
REFERENCES [dbo].[WorkShift] ([ShiftId])
GO
ALTER TABLE [dbo].[ShiftWeekday] CHECK CONSTRAINT [FK_ShiftWeekday_WorkShift]
GO
ALTER TABLE [dbo].[Workday]  WITH CHECK ADD  CONSTRAINT [FK_Workday_WorkdayType] FOREIGN KEY([DayType])
REFERENCES [dbo].[WorkdayType] ([TypeId])
GO
ALTER TABLE [dbo].[Workday] CHECK CONSTRAINT [FK_Workday_WorkdayType]
GO
ALTER TABLE [dbo].[Workday]  WITH CHECK ADD  CONSTRAINT [FK_Workday_WorkShift] FOREIGN KEY([ShiftId])
REFERENCES [dbo].[WorkShift] ([ShiftId])
GO
ALTER TABLE [dbo].[Workday] CHECK CONSTRAINT [FK_Workday_WorkShift]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'序號，自動產生流水號' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EventLog', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'員工編號' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EventLog', @level2type=N'COLUMN',@level2name=N'EmpNo'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'事件發生時間，自動產生' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EventLog', @level2type=N'COLUMN',@level2name=N'EventTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'修改類型： 1. 班別 2. 年度行事曆 3. 工作日 搭配 1.建立 2.修改 3.刪除，參考EventType表' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EventLog', @level2type=N'COLUMN',@level2name=N'EventTypeId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'動作記錄' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EventLog', @level2type=N'COLUMN',@level2name=N'Description'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'IP Address' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EventLog', @level2type=N'COLUMN',@level2name=N'IP'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'事件種類編號' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EventType', @level2type=N'COLUMN',@level2name=N'EventTypeId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'事件類型' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EventType', @level2type=N'COLUMN',@level2name=N'EventType'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'格式: MMdd' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Holiday', @level2type=N'COLUMN',@level2name=N'Date'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'為農曆' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Holiday', @level2type=N'COLUMN',@level2name=N'IsLunar'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'註解' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Holiday', @level2type=N'COLUMN',@level2name=N'Name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'是否放假，預設值﹔是' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Holiday', @level2type=N'COLUMN',@level2name=N'DayOff'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'年度' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ShiftWeekday', @level2type=N'COLUMN',@level2name=N'ShiftId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'星期數 0-星期日 1-星期一 2-星期二 3-星期三 4-星期四 5-星期五 6-星期六' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ShiftWeekday', @level2type=N'COLUMN',@level2name=N'WeekDay'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日正常上班起始時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ShiftWeekday', @level2type=N'COLUMN',@level2name=N'ArrivalTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日正常下班結束時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ShiftWeekday', @level2type=N'COLUMN',@level2name=N'DepartureTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日彈性上班起始時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ShiftWeekday', @level2type=N'COLUMN',@level2name=N'FlexibleArrivalBefore'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日彈性上班結束時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ShiftWeekday', @level2type=N'COLUMN',@level2name=N'FlexibleArrivalAfter'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日向前彈性上班時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ShiftWeekday', @level2type=N'COLUMN',@level2name=N'FlexibleDepartureBefore'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日向後彈性下班時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ShiftWeekday', @level2type=N'COLUMN',@level2name=N'FlexibleDepartureAfter'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日午休起始時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ShiftWeekday', @level2type=N'COLUMN',@level2name=N'MiddayBreakStart'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日午休結束時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ShiftWeekday', @level2type=N'COLUMN',@level2name=N'MiddayBreakEnd'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日上午休息開始時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ShiftWeekday', @level2type=N'COLUMN',@level2name=N'MorningRestStart'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日上午休息結束時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ShiftWeekday', @level2type=N'COLUMN',@level2name=N'MorningRestEnd'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日下午休息開始時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ShiftWeekday', @level2type=N'COLUMN',@level2name=N'AfternoonRestStart'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日下午休息結束時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ShiftWeekday', @level2type=N'COLUMN',@level2name=N'AfternoonRestEnd'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日正常上班時數(小時)(系統自動計算更新欄位，已扣除午休時間。)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ShiftWeekday', @level2type=N'COLUMN',@level2name=N'WorkHours'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'備註' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ShiftWeekday', @level2type=N'COLUMN',@level2name=N'Comment'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'員工編號' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Users', @level2type=N'COLUMN',@level2name=N'EmpNo'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'為管理員' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Users', @level2type=N'COLUMN',@level2name=N'IsAdmin'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'班別編號' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Workday', @level2type=N'COLUMN',@level2name=N'ShiftId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'行事曆日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Workday', @level2type=N'COLUMN',@level2name=N'WorkDate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'當日類型 1-週間工作日 2-週六休息日 3-週日例假日 4-週間國定假日 5-週六國定假日 6-週日國定假日 7-補班日 8-補假日 9-彈性放假日 20-週間休息日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Workday', @level2type=N'COLUMN',@level2name=N'DayType'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日正常上班起始時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Workday', @level2type=N'COLUMN',@level2name=N'ArrivalTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日正常下班結束時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Workday', @level2type=N'COLUMN',@level2name=N'DepartureTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日彈性上班起始時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Workday', @level2type=N'COLUMN',@level2name=N'FlexibleArrivalBefore'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日彈性上班結束時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Workday', @level2type=N'COLUMN',@level2name=N'FlexibleArrivalAfter'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日向前彈性下班時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Workday', @level2type=N'COLUMN',@level2name=N'FlexibleDepartureBefore'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日向後彈性下班時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Workday', @level2type=N'COLUMN',@level2name=N'FlexibleDepartureAfter'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日午休起始時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Workday', @level2type=N'COLUMN',@level2name=N'MiddayBreakStart'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日午休結束時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Workday', @level2type=N'COLUMN',@level2name=N'MiddayBreakEnd'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日上午休息開始時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Workday', @level2type=N'COLUMN',@level2name=N'MorningRestStart'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日上午休息結束時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Workday', @level2type=N'COLUMN',@level2name=N'MorningRestEnd'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日下午休息開始時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Workday', @level2type=N'COLUMN',@level2name=N'AfternoonRestStart'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日下午休息結束時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Workday', @level2type=N'COLUMN',@level2name=N'AfternoonRestEnd'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日正常上班時數(小時)	(系統自動計算更新欄位，已扣除午休時間。)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Workday', @level2type=N'COLUMN',@level2name=N'WorkHours'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'星期數 0-星期日 1-星期一 2-星期二 3-星期三 4-星期四 5-星期五 6-星期六' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Workday', @level2type=N'COLUMN',@level2name=N'WeekDay'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'備註' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Workday', @level2type=N'COLUMN',@level2name=N'Comment'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日類型編號' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WorkdayType', @level2type=N'COLUMN',@level2name=N'TypeId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日類型' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WorkdayType', @level2type=N'COLUMN',@level2name=N'TypeName'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'班別編號' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WorkShift', @level2type=N'COLUMN',@level2name=N'ShiftId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'班別名稱' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WorkShift', @level2type=N'COLUMN',@level2name=N'ShiftName'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'上班日陣列, 0-星期日 1-星期一 2-星期二 3-星期三 4-星期四 5-星期五 6-星期六' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WorkShift', @level2type=N'COLUMN',@level2name=N'Workdays'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日正常上班時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WorkShift', @level2type=N'COLUMN',@level2name=N'ArrivalTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日正常下班時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WorkShift', @level2type=N'COLUMN',@level2name=N'DepartureTime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日向前彈性上班時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WorkShift', @level2type=N'COLUMN',@level2name=N'FlexibleArrivalBefore'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日向後彈性上班時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WorkShift', @level2type=N'COLUMN',@level2name=N'FlexibleArrivalAfter'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日向前彈性下班時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WorkShift', @level2type=N'COLUMN',@level2name=N'FlexibleDepartureBefore'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日向後彈性下班時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WorkShift', @level2type=N'COLUMN',@level2name=N'FlexibleDepartureAfter'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日午休開始時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WorkShift', @level2type=N'COLUMN',@level2name=N'MiddayBreakStart'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日午休結束時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WorkShift', @level2type=N'COLUMN',@level2name=N'MiddayBreakEnd'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日上午休息開始時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WorkShift', @level2type=N'COLUMN',@level2name=N'MorningRestStart'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日上午休息結束時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WorkShift', @level2type=N'COLUMN',@level2name=N'MorningRestEnd'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日下午休息開始時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WorkShift', @level2type=N'COLUMN',@level2name=N'AfternoonRestStart'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日下午休息結束時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WorkShift', @level2type=N'COLUMN',@level2name=N'AfternoonRestEnd'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'備註' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'WorkShift', @level2type=N'COLUMN',@level2name=N'Comment'
GO



/****** Object:  Table [dbo].[EmpWorkday]    Script Date: 2023/9/20 上午 10:48:19 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[EmpWorkday](
	[EmpNo] [char](4) NOT NULL,
	[WorkDate] [date] NOT NULL,
	[ShiftId] [smallint] NOT NULL,
	[DayType] [tinyint] NOT NULL,
	[ArrivalTime] [smalldatetime] NOT NULL,
	[DepartureTime] [smalldatetime] NOT NULL,
	[FlexibleArrivalBefore] [smalldatetime] NOT NULL,
	[FlexibleArrivalAfter] [smalldatetime] NOT NULL,
	[FlexibleDepartureBefore] [smalldatetime] NOT NULL,
	[FlexibleDepartureAfter] [smalldatetime] NOT NULL,
	[MiddayBreakStart] [smalldatetime] NOT NULL,
	[MiddayBreakEnd] [smalldatetime] NOT NULL,
	[MorningRestStart] [smalldatetime] NOT NULL,
	[MorningRestEnd] [smalldatetime] NOT NULL,
	[AfternoonRestStart] [smalldatetime] NOT NULL,
	[AfternoonRestEnd] [smalldatetime] NOT NULL,
	[WorkHours] [float] NOT NULL,
	[WeekDay] [tinyint] NOT NULL,
	[Comment] [nvarchar](512) NULL,
 CONSTRAINT [PK_EmpWorkday_1] PRIMARY KEY CLUSTERED 
(
	[EmpNo] ASC,
	[WorkDate] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[EmpWorkday]  WITH CHECK ADD  CONSTRAINT [FK_EmpWorkday_WorkdayType] FOREIGN KEY([DayType])
REFERENCES [dbo].[WorkdayType] ([TypeId])
GO

ALTER TABLE [dbo].[EmpWorkday] CHECK CONSTRAINT [FK_EmpWorkday_WorkdayType]
GO

ALTER TABLE [dbo].[EmpWorkday]  WITH CHECK ADD  CONSTRAINT [FK_EmpWorkday_WorkShift] FOREIGN KEY([ShiftId])
REFERENCES [dbo].[WorkShift] ([ShiftId])
GO

ALTER TABLE [dbo].[EmpWorkday] CHECK CONSTRAINT [FK_EmpWorkday_WorkShift]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'行事曆日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkday', @level2type=N'COLUMN',@level2name=N'WorkDate'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'班別編號' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkday', @level2type=N'COLUMN',@level2name=N'ShiftId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'當日類型 1-週間工作日 2-週六休息日 3-週日例假日 4-週間國定假日 5-週六國定假日 6-週日國定假日 7-補班日 8-補假日 9-彈性放假日 20-週間休息日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkday', @level2type=N'COLUMN',@level2name=N'DayType'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日正常上班起始時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkday', @level2type=N'COLUMN',@level2name=N'ArrivalTime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日正常下班結束時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkday', @level2type=N'COLUMN',@level2name=N'DepartureTime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日彈性上班起始時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkday', @level2type=N'COLUMN',@level2name=N'FlexibleArrivalBefore'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日彈性上班結束時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkday', @level2type=N'COLUMN',@level2name=N'FlexibleArrivalAfter'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日向前彈性下班時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkday', @level2type=N'COLUMN',@level2name=N'FlexibleDepartureBefore'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日向後彈性下班時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkday', @level2type=N'COLUMN',@level2name=N'FlexibleDepartureAfter'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日午休起始時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkday', @level2type=N'COLUMN',@level2name=N'MiddayBreakStart'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日午休結束時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkday', @level2type=N'COLUMN',@level2name=N'MiddayBreakEnd'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日上午休息開始時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkday', @level2type=N'COLUMN',@level2name=N'MorningRestStart'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日上午休息結束時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkday', @level2type=N'COLUMN',@level2name=N'MorningRestEnd'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日下午休息開始時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkday', @level2type=N'COLUMN',@level2name=N'AfternoonRestStart'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日下午休息結束時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkday', @level2type=N'COLUMN',@level2name=N'AfternoonRestEnd'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'工作日正常上班時數(小時)	(系統自動計算更新欄位，已扣除午休時間。)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkday', @level2type=N'COLUMN',@level2name=N'WorkHours'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'星期數 0-星期日 1-星期一 2-星期二 3-星期三 4-星期四 5-星期五 6-星期六' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkday', @level2type=N'COLUMN',@level2name=N'WeekDay'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'備註' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkday', @level2type=N'COLUMN',@level2name=N'Comment'
GO


CREATE TABLE [dbo].[EmpWorkShift](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[EmpNo] [char](4) NOT NULL,
	[ShiftId] [smallint] NOT NULL,
	[WorkDate] [date] NOT NULL,
 CONSTRAINT [PK_EmpWorkShift] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [IX_EmpWorkShift] UNIQUE NONCLUSTERED 
(
	[EmpNo] ASC,
	[WorkDate] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[EmpWorkShift]  WITH CHECK ADD  CONSTRAINT [FK_EmpWorkShift_EmpWorkShift] FOREIGN KEY([ShiftId])
REFERENCES [dbo].[WorkShift] ([ShiftId])
GO

ALTER TABLE [dbo].[EmpWorkShift] CHECK CONSTRAINT [FK_EmpWorkShift_EmpWorkShift]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'流水號' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkShift', @level2type=N'COLUMN',@level2name=N'ID'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'員工編號' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkShift', @level2type=N'COLUMN',@level2name=N'EmpNo'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'班別編號' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkShift', @level2type=N'COLUMN',@level2name=N'ShiftId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'日期' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'EmpWorkShift', @level2type=N'COLUMN',@level2name=N'WorkDate'
GO

USE [master]
GO
ALTER DATABASE [Workday] SET  READ_WRITE 
GO

