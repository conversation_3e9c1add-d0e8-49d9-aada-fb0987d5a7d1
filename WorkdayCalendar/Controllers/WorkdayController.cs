using System.Data;                         // 資料表操作
using System.Threading.Tasks;              // 非同步程式設計支援

using Microsoft.AspNetCore.Authorization;  // 提供授權相關功能
using Microsoft.AspNetCore.Mvc;           // ASP.NET Core MVC 核心功能
using Microsoft.Extensions.Configuration;  // 組態設定存取

using Newtonsoft.Json;                     // JSON 序列化/反序列化

using WorkdayCalendar.DTO;                 // 資料傳輸物件
using WorkdayCalendar.Models;              // 資料模型

namespace WorkdayCalendar.Controllers
{
    /// <summary>
    /// 工作日曆管理控制器<br/>
    /// 負責處理與工作日曆相關的所有 Web API 請求，包括新增、查詢、更新和刪除工作日曆資料。
    /// 所有操作都需要經過身分驗證。
    /// </summary>
    [Authorize]
    [ApiController]
    [Route("/api/[controller]/[action]")]
    public class WorkdayController : ControllerBase
    {
        /// <summary>
        /// 組態設定物件，用於存取應用程式設定
        /// </summary>
        private readonly IConfiguration Configuration;

        /// <summary>
        /// 工作日曆資料存取層物件
        /// </summary>
        private static WorkdayDto workdayDTO { get; set; }

        /// <summary>
        /// 建構函式，初始化 WorkdayController 的新實例
        /// </summary>
        /// <param name="configuration">應用程式組態設定</param>
        public WorkdayController(IConfiguration configuration)
        {
            Configuration = configuration;
            workdayDTO = new WorkdayDto(Configuration.GetSecuredConnectionString("Workday"));
        }

        /// <summary>
        /// 新增單筆工作日曆資料
        /// </summary>
        /// <param name="workday">要新增的工作日曆資料</param>
        /// <returns>新增成功返回 true，否則返回 false</returns>
        /// <remarks>
        /// POST: /api/Workday/Add
        /// 需要傳入完整的工作日曆資料模型，系統會自動記錄操作者資訊。
        /// </remarks>
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public bool Add(Workday workday)
        {
            string userName = HttpContext.User.Identity.Name;
            string userId = Utility.GetValidUserID(userName);
            string ip = HttpContext.Connection.RemoteIpAddress.ToString();
            return workdayDTO.Add(userId, ip, workday);
        }

        /// <summary>
        /// 批次新增多筆工作日曆資料
        /// </summary>
        /// <param name="param">包含新增參數的物件，包含年份、班別ID、開始日期和假日設定等</param>
        /// <returns>新增成功返回 true，否則返回 false</returns>
        /// <remarks>
        /// POST: /api/Workday/AddWorkdays
        /// 用於批次新增多個工作日的排班資料。
        /// 系統會根據參數自動計算並新增多筆工作日曆記錄。
        /// </remarks>
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public bool AddWorkdays(WorkdayParam param)
        {
            string userName = HttpContext.User.Identity.Name;
            string userId = Utility.GetValidUserID(userName);
            param.StartDate = param.StartDate.ToLocalTime().Date; // 確保起始日期為日期格式，不包含時間部分
            string ip = HttpContext.Connection.RemoteIpAddress.ToString();
            return workdayDTO.AddWorkdays(userId, ip, param);
        }


        /// <summary>
        /// 檢查是否可以新增工作日曆資料
        /// </summary>
        /// <param name="param">包含檢查參數的物件</param>
        /// <returns>可以新增返回 true，否則返回 false</returns>
        /// <remarks>
        /// POST: /api/Workday/CanAdd
        /// 在新增工作日曆前，先檢查是否符合新增條件。
        /// 用於前端驗證，避免重複或衝突的資料。
        /// </remarks>
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public bool CanAdd(WorkdayParam param)
        {
            string userName = HttpContext.User.Identity.Name;
            string userId = Utility.GetValidUserID(userName);
            string ip = HttpContext.Connection.RemoteIpAddress.ToString();
            return workdayDTO.CanAdd(userId, ip, param);
        }

        /// <summary>
        /// 刪除指定的工作日曆資料
        /// </summary>
        /// <param name="workday">要刪除的工作日曆資料</param>
        /// <returns>刪除成功返回 true，否則返回 false</returns>
        /// <remarks>
        /// DELETE: /api/Workday/Delete
        /// 此操作會從資料庫中刪除指定的工作日曆記錄，並記錄操作者資訊。
        /// 請注意，此操作無法復原。
        /// </remarks>
        [HttpDelete]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public bool Delete(Workday workday)
        {
            string userName = HttpContext.User.Identity.Name;
            string userId = Utility.GetValidUserID(userName);
            string ip = HttpContext.Connection.RemoteIpAddress.ToString();
            return workdayDTO.Delete(userId, ip, workday);
        }

        /// <summary>
        /// 取得所有班別資料
        /// </summary>
        /// <returns>JSON 格式的班別資料清單，若無資料則返回空陣列 []</returns>
        /// <remarks>
        /// GET: /api/Workday/GetShifts
        /// 取得系統中所有可用的班別設定。
        /// 快取設為不儲存，以確保取得最新資料。
        /// </remarks>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public string GetShifts()
        {
            string ret = "[]";
            DataTable dt = workdayDTO.GetShifts();
            //將DataTable轉成JSON字串
            if (dt != null && dt.Rows.Count > 0)
            {
                ret = JsonConvert.SerializeObject(dt);
            }
            return ret;
        }

        /// <summary>
        /// 取得指定年份和班別的工作日曆資料
        /// </summary>
        /// <param name="year">年份，例如：2023</param>
        /// <param name="shiftId">班別ID</param>
        /// <returns>JSON 格式的工作日曆資料清單，若無資料則返回空陣列 []</returns>
        /// <remarks>
        /// GET: /api/Workday/GetWorkdays?Year=2023&shiftId=1
        /// 範例網址：/api/Workday/GetWorkdays?Year=2023&shiftId=1
        /// 取得指定年份和班別的所有工作日曆資料。
        /// 快取設為不儲存，以確保取得最新資料。
        /// </remarks>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        // GET: Workday
        public string GetWorkdays(int year, int shiftId)
        {
            string ret = "[]";
            DataTable dt = workdayDTO.GetWorkdays(year, shiftId);
            //將DataTable轉成JSON字串
            if (dt != null && dt.Rows.Count > 0)
            {
                ret = JsonConvert.SerializeObject(dt);
            }
            return ret;
        }

        /// <summary>
        /// 取得工作日曆類型清單
        /// </summary>
        /// <returns>JSON 格式的工作日曆類型清單，若無資料則返回空陣列 []</returns>
        /// <remarks>
        /// GET: /api/Workday/GetWorkdayType
        /// 取得系統中定義的所有工作日曆類型，例如：工作日、休息日、國定假日等。
        /// 快取設為不儲存，以確保取得最新資料。
        /// </remarks>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public string GetWorkdayType()
        {
            string ret = "[]";
            DataTable dt = workdayDTO.GetWorkdayType();
            //將DataTable轉成JSON字串
            if (dt != null && dt.Rows.Count > 0)
            {
                ret = JsonConvert.SerializeObject(dt);
            }
            return ret;
        }

        /// <summary>
        /// 更新工作日曆資料
        /// </summary>
        /// <param name="workday">要更新的工作日曆資料</param>
        /// <returns>更新成功返回 true，否則返回 false</returns>
        /// <remarks>
        /// PUT: /api/Workday/Update
        /// 更新現有的工作日曆記錄。
        /// 系統會自動記錄最後修改者資訊和修改時間。
        /// </remarks>
        [HttpPut]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public async Task<bool> Update(Workday workday)
        {
            string userName = HttpContext.User.Identity.Name;
            string userId = Utility.GetValidUserID(userName);
            string ip = HttpContext.Connection.RemoteIpAddress.ToString();
            bool ret = await workdayDTO.Update(userName, userId, ip, workday);
            return ret;
        }

    }
}
