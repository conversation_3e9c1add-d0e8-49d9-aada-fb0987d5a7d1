using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;  // 提供授權相關功能
using Microsoft.AspNetCore.Mvc;           // ASP.NET Core MVC 核心功能
using Microsoft.EntityFrameworkCore;      // Entity Framework Core 功能
using Newtonsoft.Json;                     // JSON 序列化/反序列化
using WorkdayCalendar.Data;               // 資料存取層
using WorkdayCalendar.Models;              // 資料模型

namespace WorkdayCalendar.Controllers
{
    /// <summary>
    /// 員工班別管理控制器<br />
    /// 負責處理與員工班別分配相關的所有 Web API 請求，包括新增、查詢、更新和刪除員工班別資訊。
    /// 使用 Entity Framework Core 進行資料庫存取操作。
    /// 所有操作都需要經過身分驗證。
    /// </summary>
    [Authorize]
    [ApiController]
    [Route("/api/[controller]/[action]")]
    public class EmpWorkShiftController : ControllerBase
    {
        /// <summary>
        /// 資料庫上下文物件，用於 Entity Framework Core 資料存取
        /// </summary>
        private readonly WorkdayDbContext _context;

        /// <summary>
        /// 建構函式，初始化 EmpWorkShiftController 的新實例
        /// </summary>
        /// <param name="context">資料庫上下文，透過依賴注入提供</param>
        /// <remarks>
        /// 使用依賴注入方式取得 WorkdayDbContext 實例，
        /// 確保資料庫連線的生命週期由 DI 容器管理
        /// </remarks>
        public EmpWorkShiftController(WorkdayDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 取得所有員工班別資料
        /// </summary>
        /// <returns>JSON 格式的員工班別資料清單</returns>
        /// <remarks>
        /// GET: /api/EmpWorkShift/GetDataTable
        /// 此 API 會回傳所有員工班別資料，並快取設為不儲存。
        /// 回傳的 JSON 中時間格式會移除 ":00" 結尾以保持與原有系統的一致性。
        /// 使用 AsNoTracking() 提升查詢效能，因為這是唯讀操作。
        /// </remarks>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public async Task<string> GetAll()
        {
            try
            {
                // 使用 Entity Framework Core 查詢所有員工班別資料
                // AsNoTracking() 提升查詢效能，因為不需要追蹤實體變更
                var empWorkShifts = await _context.EmpWorkShifts
                    .AsNoTracking()
                    .OrderBy(e => e.EmpNo)  // 按員工編號排序
                    .ThenBy(e => e.WorkDate) // 按工作日期排序
                    .ToListAsync();

                // 將查詢結果序列化為 JSON 字串
                string jsonResult = JsonConvert.SerializeObject(empWorkShifts);

                // 移除時間格式中的 ":00" 結尾，保持與原有系統的一致性
                return jsonResult.Replace(":00\"", "\"");
            }
            catch (Exception ex)
            {
                // 記錄錯誤並回傳空陣列
                // 在實際應用中，應該使用適當的日誌記錄框架
                Console.WriteLine($"取得員工班別資料時發生錯誤: {ex.Message}");
                return "[]";
            }
        }



        /// <summary>
        /// 根據員工編號取得特定員工的班別資料
        /// </summary>
        /// <param name="EmpNo">員工編號 (4位字元)</param>
        /// <param name="year">西元年度</param>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public async Task<string> Get(string empNo, int year)
        {
            try
            {
                // 使用 Entity Framework Core 查詢所有員工班別資料
                // AsNoTracking() 提升查詢效能，因為不需要追蹤實體變更
                var empWorkShifts = await _context.EmpWorkShifts
                    .AsNoTracking()
                    .Where(e => e.EmpNo == empNo && e.WorkDate.Year == year)
                    .OrderBy(e => e.WorkDate) // 只需按工作日期排序
                    .ToListAsync();

                // 將查詢結果序列化為 JSON 字串
                string jsonResult = JsonConvert.SerializeObject(empWorkShifts);

                // 移除時間格式中的 ":00" 結尾，保持與原有系統的一致性
                return jsonResult.Replace(":00\"", "\"");
            }
            catch (Exception ex)
            {
                // 記錄錯誤並回傳空陣列
                // 在實際應用中，應該使用適當的日誌記錄框架
                Console.WriteLine($"取得員工班別資料時發生錯誤: {ex.Message}");
                return "[]";
            }
        }

        /// <summary>
        /// 根據員工編號取得特定員工的班別資料
        /// </summary>
        /// <param name="EmpNo">員工編號 (4位字元)</param>
        /// <returns>JSON 格式的員工班別資料，若找不到則回傳 null</returns>
        /// <remarks>
        /// GET: /api/EmpWorkShift/GetByEmpNo/{EmpNo}
        /// 根據員工編號查詢特定員工的班別資訊。
        /// 員工編號為 4 位字元的主鍵。
        /// </remarks>
        // [HttpGet]
        // [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        // [Route("/api/[controller]/[action]/{EmpNo}")]
        // public async Task<string> GetByEmpNo(string EmpNo)
        // {
        //     try
        //     {
        //         // 驗證員工編號格式
        //         if (string.IsNullOrWhiteSpace(EmpNo) || EmpNo.Length != 4)
        //         {
        //             return "null";
        //         }

        //         // 根據員工編號查詢班別資料
        //         var param = await _context.EmpWorkShifts
        //             .AsNoTracking()
        //             .FirstOrDefaultAsync(e => e.EmpNo == EmpNo);

        //         if (param == null)
        //         {
        //             return "null";
        //         }

        //         // 序列化為 JSON 並移除時間格式中的 ":00" 結尾
        //         string jsonResult = JsonConvert.SerializeObject(param);
        //         return jsonResult.Replace(":00\"", "\"");
        //     }
        //     catch (Exception ex)
        //     {
        //         // 記錄錯誤
        //         Console.WriteLine($"取得員工 {EmpNo} 班別資料時發生錯誤: {ex.Message}");
        //         return "null";
        //     }
        // }

        /// <summary>
        /// 根據班別編號取得使用該班別的所有員工資料
        /// </summary>
        /// <param name="shiftId">班別編號</param>
        /// <returns>JSON 格式的員工班別資料清單</returns>
        /// <remarks>
        /// GET: /api/EmpWorkShift/GetByShiftId/{ShiftId}
        /// 查詢使用指定班別的所有員工資訊。
        /// </remarks>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/{shiftId}")]
        public async Task<string> GetByShiftId(int shiftId)
        {
            try
            {
                // 根據班別編號查詢員工資料
                var empWorkShifts = await _context.EmpWorkShifts
                    .AsNoTracking()
                    .Where(e => e.ShiftId == shiftId)
                    .OrderBy(e => e.EmpNo)
                    .ToListAsync();

                // 序列化為 JSON 並移除時間格式中的 ":00" 結尾
                string jsonResult = JsonConvert.SerializeObject(empWorkShifts);
                return jsonResult.Replace(":00\"", "\"");
            }
            catch (Exception ex)
            {
                // 記錄錯誤
                Console.WriteLine($"取得班別 {shiftId} 的員工資料時發生錯誤: {ex.Message}");
                return "[]";
            }
        }

        /// <summary>
        /// 新增員工班別資料
        /// </summary>
        /// <param name="param">要新增的員工班別資料</param>
        /// <returns>新增成功返回員工編號，失敗返回空字串</returns>
        /// <remarks>
        /// POST: /api/EmpWorkShift/Add
        /// 需要傳入完整的員工班別資料模型，系統會自動記錄操作者資訊。
        /// </remarks>
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public async Task<IActionResult> AddOrUpdate(EmpWorkShiftParam param)
        {
            try
            {
                // 驗證輸入資料
                if (param == null || string.IsNullOrWhiteSpace(param.EmpNo))
                {
                    return BadRequest("Invalid employee work shift data.");
                }
                param.StartDate = param.StartDate.ToLocalTime().Date; // 確保開始日期為日期格式
                param.EndDate = param.EndDate.ToLocalTime().Date; // 確保結束日期為日期格式

                for (DateTime WorkDate = param.StartDate; WorkDate <= param.EndDate; WorkDate = WorkDate.AddDays(1))
                {
                    // 新增或更新每一天的班別資料
                    var empWorkShift = new EmpWorkShift
                    {
                        EmpNo = param.EmpNo,
                        ShiftId = param.ShiftId,
                        WorkDate = WorkDate
                    };
                    // 檢查員工編號是否已存在
                    var existingEmp = await _context.EmpWorkShifts
                        .AsNoTracking()
                        .FirstOrDefaultAsync(e =>
                            e.EmpNo == empWorkShift.EmpNo
                            && e.WorkDate == empWorkShift.WorkDate);
                    // 如果員工編號已存在，則更新現有記錄
                    if (existingEmp != null)
                    {
                        // 員工編號已存在，無法新增, 改用更新
                        await Update(empWorkShift);
                    }
                    else
                    {
                        // 新增新的員工班別資料
                        _context.EmpWorkShifts.Add(empWorkShift);
                    }

                    // 新增員工班別資料到資料庫
                    _context.EmpWorkShifts.Add(empWorkShift);
                    await _context.SaveChangesAsync();

                }

                // 取得操作者資訊用於日誌記錄
                string userName = HttpContext.User.Identity?.Name ?? "";
                string userId = Utility.GetValidUserID(userName);
                string ip = HttpContext.Connection?.RemoteIpAddress?.ToString() ?? "";

                // 記錄操作日誌 (事件類型可根據實際需求調整)
                if (!string.IsNullOrEmpty(userId))
                {
                    var connectionString = _context.Database.GetConnectionString();
                    Utility.EventLog(1, userId, $"新增員工班別: {param.EmpNo}", ip, connectionString);
                }

                return Ok("新增成功");
            }
            catch (Exception ex)
            {
                // 記錄錯誤
                Console.WriteLine($"新增員工班別時發生錯誤: {ex.Message} {ex.StackTrace}");
                return BadRequest($"新增員工班別時發生錯誤: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新員工班別資料
        /// </summary>
        /// <param name="empWorkShift">要更新的員工班別資料</param>
        /// <returns>更新成功返回 true，否則返回 false</returns>
        /// <remarks>
        /// PUT: /api/EmpWorkShift/Update
        /// 需要傳入完整的員工班別資料模型，系統會自動記錄最後修改者資訊。
        /// 根據員工編號 (EmpNo) 更新對應的記錄。
        /// </remarks>
        [HttpPut]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public async Task<IActionResult> Update(EmpWorkShift empWorkShift)
        {
            try
            {
                // 驗證輸入資料
                if (empWorkShift == null || string.IsNullOrWhiteSpace(empWorkShift.EmpNo))
                {
                    return BadRequest("無員工資料");
                }

                // 檢查該筆資料是否存在
                var existingRecord = await _context.EmpWorkShifts
                     .AsNoTracking()
                    .FirstOrDefaultAsync(e => e.EmpNo == empWorkShift.EmpNo &&
                    e.WorkDate == empWorkShift.WorkDate);

                if (existingRecord == null)
                {
                    // 該筆紀錄不存在，無法更新
                    return BadRequest("該筆紀錄不存在，無法更新"); ;
                }

                // 取得操作者資訊
                string userName = HttpContext.User.Identity?.Name ?? "";
                string userId = Utility.GetValidUserID(userName);
                string ip = HttpContext.Connection?.RemoteIpAddress?.ToString() ?? "";

                // 更新員工班別資料
                existingRecord.ShiftId = empWorkShift.ShiftId;

                // 儲存變更到資料庫
                await _context.SaveChangesAsync();

                // 記錄操作日誌
                if (!string.IsNullOrEmpty(userId))
                {
                    var connectionString = _context.Database.GetConnectionString();
                    Utility.EventLog(2, userId, $"更新員工班別: {empWorkShift.EmpNo}", ip, connectionString);
                }

                return Ok("新增成功");
            }
            catch (Exception ex)
            {
                // 記錄錯誤
                Console.WriteLine($"新增員工班別時發生錯誤: {ex.Message} {ex.StackTrace}");
                return BadRequest($"新增員工班別時發生錯誤: {ex.Message}");
            }
        }

        /// <summary>
        /// 刪除指定員工的班別資料
        /// </summary>
        /// <param name="workShift">要刪除的員工班別資料</param>
        /// <returns>刪除成功返回 true，否則返回 false</returns>
        /// <remarks>
        /// DELETE: /api/EmpWorkShift/Delete
        /// 此操作會從資料庫中刪除指定員工的班別記錄，並記錄操作者資訊。
        /// 請注意，此操作無法復原。
        /// </remarks>
        [HttpDelete]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public async Task<IActionResult> Delete(EmpWorkShift workShift)
        {
            try
            {
                // 驗證員工編號
                if (string.IsNullOrWhiteSpace(workShift.EmpNo))
                {
                    return BadRequest("無員工編號");
                }

                // 查詢要刪除的員工班別記錄
                var empWorkShift = await _context.EmpWorkShifts
                     .AsNoTracking()
                    .FirstOrDefaultAsync(e => e.EmpNo == workShift.EmpNo
                    && e.WorkDate == workShift.WorkDate);

                if (empWorkShift == null)
                {
                    // 資料不存在，無法刪除
                    return BadRequest("無此資料");
                }

                // 取得操作者資訊
                string userName = HttpContext.User.Identity?.Name ?? "";
                string userId = Utility.GetValidUserID(userName);
                string ip = HttpContext.Connection?.RemoteIpAddress?.ToString() ?? "";

                // 從資料庫中移除員工班別記錄
                _context.EmpWorkShifts.Remove(empWorkShift);
                await _context.SaveChangesAsync();

                // 記錄操作日誌
                if (!string.IsNullOrEmpty(userId))
                {
                    var connectionString = _context.Database.GetConnectionString();
                    Utility.EventLog(3, userId, $"刪除員工班別: {workShift.EmpNo}", ip, connectionString);
                }

                return Ok("刪除成功");
            }
            catch (Exception ex)
            {
                // 記錄錯誤
                Console.WriteLine($"刪除員工班別時發生錯誤: {ex.Message} {ex.StackTrace}");
                return BadRequest($"刪除員工班別時發生錯誤: {ex.Message}");
            }
        }

        /// <summary>
        /// 批次更新員工班別 - 將多個員工分配到指定班別
        /// </summary>
        /// <param name="request">批次更新請求，包含員工編號清單和目標班別編號</param>
        /// <returns>更新成功的員工數量</returns>
        /// <remarks>
        /// POST: /api/EmpWorkShift/BatchUpdateShift
        /// 此 API 允許一次將多個員工分配到同一個班別。
        /// 會先查詢目標班別的詳細資訊，然後更新所有指定員工的班別設定。
        /// </remarks>
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public async Task<int> BatchUpdateShift(BatchUpdateShiftRequest request)
        {
            try
            {
                // 驗證輸入資料
                if (request == null || request.EmpNos == null || !request.EmpNos.Any() || request.ShiftId <= 0)
                {
                    return 0;
                }
                request.WorkDate = request.WorkDate.ToLocalTime().Date; // 確保工作日期為日期格式
                // 查詢目標班別資訊
                var targetShift = await _context.WorkShifts
                    .AsNoTracking()
                    .FirstOrDefaultAsync(s => s.ShiftId == request.ShiftId);

                if (targetShift == null)
                {
                    // 目標班別不存在
                    return 0;
                }

                // 取得操作者資訊
                string userName = HttpContext.User.Identity?.Name ?? "";
                string userId = Utility.GetValidUserID(userName);
                string ip = HttpContext.Connection?.RemoteIpAddress?.ToString() ?? "";

                int updatedCount = 0;

                // 逐一處理每個員工
                foreach (string EmpNo in request.EmpNos)
                {
                    if (string.IsNullOrWhiteSpace(EmpNo) || EmpNo.Length != 4)
                    {
                        continue; // 跳過無效的員工編號
                    }

                    // 查詢員工是否已有班別記錄
                    var existingEmp = await _context.EmpWorkShifts.AsNoTracking()
                        .FirstOrDefaultAsync(e => e.EmpNo == EmpNo && e.WorkDate == request.WorkDate);

                    if (existingEmp != null)
                    {
                        // 更新現有記錄
                        existingEmp.ShiftId = targetShift.ShiftId;

                    }
                    else
                    {
                        // 建立新記錄
                        var newEmpWorkShift = new EmpWorkShift
                        {
                            EmpNo = EmpNo,
                            ShiftId = request.ShiftId,
                            WorkDate = request.WorkDate
                        };

                        _context.EmpWorkShifts.Add(newEmpWorkShift);
                    }

                    updatedCount++;
                }

                // 儲存所有變更
                await _context.SaveChangesAsync();

                // 記錄操作日誌
                if (!string.IsNullOrEmpty(userId) && updatedCount > 0)
                {
                    var connectionString = _context.Database.GetConnectionString();
                    Utility.EventLog(4, userId, $"批次更新員工班別: {updatedCount} 位員工分配到班別 {request.ShiftId}", ip, connectionString);
                }

                return updatedCount;
            }
            catch (Exception ex)
            {
                // 記錄錯誤
                Console.WriteLine($"批次更新員工班別時發生錯誤: {ex.Message}");
                return 0;
            }
        }
    }

    /// <summary>
    /// 批次更新班別請求模型
    /// </summary>
    public class BatchUpdateShiftRequest
    {
        /// <summary>
        /// 要更新的員工編號清單
        /// </summary>
        public List<string> EmpNos { get; set; } = new List<string>();

        /// <summary>
        /// 目標班別編號
        /// </summary>
        public short ShiftId { get; set; }


        /// <summary>
        /// 工作日期
        /// </summary>
        public DateTime WorkDate { get; set; }
    }
}