using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;

namespace WorkdayCalendar.Controllers
{
    /// <summary>
    /// 登入使用者控制器
    /// 處理與登入使用者相關的 API 請求，包含身分驗證與權限管理
    /// </summary>
    [Authorize] // 要求使用者必須通過身分驗證才能存取此控制器
    [ApiController] // 標示為 API 控制器，提供 Web API 功能
    [Route("/api/[controller]")] // 設定路由模式為 /api/LogonUser
    public class LogonUserController : ControllerBase
    {
        /// <summary>
        /// 設定檔服務，用於讀取應用程式設定
        /// </summary>
        private readonly IConfiguration Configuration;

        /// <summary>
        /// 初始化登入使用者控制器的新執行個體
        /// </summary>
        /// <param name="configuration">設定檔服務，用於存取應用程式設定值</param>
        public LogonUserController(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        /// <summary>
        /// 判斷目前登入的使用者是否為系統管理員
        /// </summary>
        /// <returns>
        /// 如果目前使用者具有管理員權限，則傳回 <c>true</c>；否則傳回 <c>false</c>
        /// </returns>
        [HttpGet] // 指定此方法接受 HTTP GET 請求
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)] // 設定回應不進行快取
        [Route("/api/[controller]/[action]")] // 設定完整路由為 /api/LogonUser/IsAdmin
        // GET api/LogonUser/IsAdmin
        public bool IsAdmin()
        {
            // 從設定檔中取得工作日資料庫連線字串
            string connectionString = Configuration.GetSecuredConnectionString("Workday");

            // 取得目前登入使用者的身分名稱（通常為網域\使用者名稱格式）
            string userName = HttpContext.User.Identity.Name;

            // 透過工具類別驗證並取得有效的使用者 ID
            string userId = Utility.GetValidUserID(userName);

            // 呼叫工具類別方法，檢查該使用者是否具有管理員權限
            return Utility.IsAdmin(userId, connectionString);
        }
    }
}
