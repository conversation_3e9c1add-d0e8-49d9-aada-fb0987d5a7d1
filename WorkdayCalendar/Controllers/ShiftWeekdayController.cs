using System.Data;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;

using Newtonsoft.Json;

using WorkdayCalendar.DTO;
using WorkdayCalendar.Models;

namespace WorkdayCalendar.Controllers
{
    /// <summary>班表平日控制器 - 用於管理班表和平日相關的API操作</summary>
    [Authorize] // 需要授權才能存取此控制器
    [ApiController] // 標記為API控制器，啟用自動模型驗證等功能
    [Route("/api/[controller]")] // 設定路由模式，控制器名稱會自動替換到[controller]位置
    public class ShiftWeekdayController : ControllerBase
    {
        /// <summary>應用程式組態設定介面，用於讀取設定檔參數</summary>
        private readonly IConfiguration Configuration;

        /// <summary>班表平日資料傳輸物件，處理與資料庫相關的操作</summary>
        private static ShiftWeekdayDto shiftWeekdayDTO { get; set; }

        /// <summary>
        /// 班表平日控制器建構子
        /// 初始化控制器並設定資料庫連線設定
        /// </summary>
        /// <param name="configuration">應用程式組態設定，包含資料庫連線字串等設定值</param>
        public ShiftWeekdayController(IConfiguration configuration)
        {
            Configuration = configuration;
            // 從組態設定中取得Workday連線字串，初始化資料傳輸物件
            shiftWeekdayDTO = new ShiftWeekdayDto(Configuration.GetSecuredConnectionString("Workday"));
        }

        /// <summary>
        /// 取得所有班表平日資料
        /// 從資料庫查詢所有班表和平日的相關資訊並轉換為JSON格式回傳
        /// </summary>
        /// <returns>JSON格式的班表平日資料字串</returns>
        [HttpGet] // 指定此方法回應HTTP GET請求
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)] // 禁用快取，確保每次都取得最新資料
        [Route("/api/[controller]/[action]")] // 設定完整的API路由路徑
        public string GetAll()
        {
            string ret = "";
            // 呼叫資料傳輸物件取得所有班表平日資料
            DataTable dt = shiftWeekdayDTO.GetAll();
            // 將DataTable資料結構轉換成JSON字串格式
            ret = JsonConvert.SerializeObject(dt);
            // 移除時間格式中的秒數部分，例如將"12:00:00"改為"12:00"
            return ret.Replace(":00\"", "\"");
        }

        /// <summary>
        /// 取得所有國定假日資料（包括不休假的工作日）
        /// 查詢資料庫中所有的國定假日，包含那些被調整為工作日的假日
        /// </summary>
        /// <returns>JSON格式的所有國定假日資料字串</returns>
        [HttpGet] // 指定此方法回應HTTP GET請求
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)] // 禁用快取，確保每次都取得最新資料
        [Route("/api/[controller]/[action]")] // 設定完整的API路由路徑
        public string AllHolidays()
        {
            string ret = "";
            // 原本的SQL查詢語句參考：SELECT Date, IsLunar, Name, DayOff FROM dbo.Holiday ORDER BY Date;
            // 呼叫資料傳輸物件取得所有國定假日資料
            DataTable dt = shiftWeekdayDTO.GetAllHolidaysDataTable();
            // 將DataTable資料結構轉換成JSON字串格式
            ret = JsonConvert.SerializeObject(dt);
            return ret;
        }

        /// <summary>
        /// 取得指定年份的預設國定假日資料
        /// 根據提供的年份參數，查詢該年度的所有國定假日資訊
        /// </summary>
        /// <param name="year">要查詢的年份</param>
        /// <returns>JSON格式的指定年份國定假日資料字串</returns>
        [HttpGet] // 指定此方法回應HTTP GET請求
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)] // 禁用快取，確保每次都取得最新資料
        [Route("/api/[controller]/[action]/{year}")] // 設定包含年份參數的API路由路徑
        public string Holidays(int year)
        {
            string ret = "";
            // 呼叫資料傳輸物件取得指定年份的國定假日資料
            DataTable dt = shiftWeekdayDTO.Holidays(year);
            // 將DataTable資料結構轉換成JSON字串格式
            ret = JsonConvert.SerializeObject(dt);
            // 移除時間格式中的秒數部分，例如將"12:00:00"改為"12:00"
            return ret.Replace(":00\"", "\"");
        }

        /// <summary>
        /// 新增年度行事曆資料
        /// 接收前端傳送的班表平日物件，進行新增操作並記錄操作者資訊
        /// </summary>
        /// <param name="yearCalendar">要新增的年度行事曆物件，包含班表和平日的相關資訊</param>
        /// <returns>布林值表示新增操作是否成功</returns>
        [HttpPost] // 指定此方法回應HTTP POST請求
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)] // 禁用快取
        [Route("/api/[controller]/[action]")] // 設定完整的API路由路徑
        public bool Add(ShiftWeekday yearCalendar)
        {
            // 從HTTP上下文中取得目前登入使用者的身分名稱
            string userName = HttpContext.User.Identity.Name;
            // 透過工具類別取得有效的使用者ID
            string userId = Utility.GetValidUserID(userName);
            // 取得發出請求的客戶端IP位址，用於記錄操作來源
            string ip = HttpContext.Connection.RemoteIpAddress.ToString();
            // 呼叫資料傳輸物件執行新增操作，並傳入使用者ID、IP位址和年度行事曆資料
            return shiftWeekdayDTO.Add(userId, ip, yearCalendar);
        }

        /// <summary>
        /// 更新年度行事曆資料
        /// 接收前端傳送的班表平日物件，進行更新操作並記錄操作者資訊
        /// </summary>
        /// <param name="yearCalendar">要更新的年度行事曆物件，包含修改後的班表和平日資訊</param>
        /// <returns>布林值表示更新操作是否成功</returns>
        [HttpPut] // 指定此方法回應HTTP PUT請求，通常用於更新現有資源
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)] // 禁用快取
        [Route("/api/[controller]/[action]")] // 設定完整的API路由路徑
        public bool Update(ShiftWeekday yearCalendar)
        {
            // 從HTTP上下文中取得目前登入使用者的身分名稱
            string userName = HttpContext.User.Identity.Name;
            // 透過工具類別取得有效的使用者ID
            string userId = Utility.GetValidUserID(userName);
            // 取得發出請求的客戶端IP位址，用於記錄操作來源
            string ip = HttpContext.Connection.RemoteIpAddress.ToString();
            // 呼叫資料傳輸物件執行更新操作，並傳入使用者ID、IP位址和年度行事曆資料
            return shiftWeekdayDTO.Update(userId, ip, yearCalendar);
        }

        /// <summary>
        /// 刪除年度行事曆資料
        /// 接收前端傳送的班表平日物件，進行刪除操作並記錄操作者資訊
        /// </summary>
        /// <param name="yearCalendar">要刪除的年度行事曆物件，通常只需要包含識別資訊</param>
        /// <returns>布林值表示刪除操作是否成功</returns>
        [HttpDelete] // 指定此方法回應HTTP DELETE請求，用於刪除資源
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)] // 禁用快取
        [Route("/api/[controller]/[action]")] // 設定完整的API路由路徑
        public bool Delete(ShiftWeekday yearCalendar)
        {
            // 從HTTP上下文中取得目前登入使用者的身分名稱
            string userName = HttpContext.User.Identity.Name;
            // 透過工具類別取得有效的使用者ID
            string userId = Utility.GetValidUserID(userName);
            // 取得發出請求的客戶端IP位址，用於記錄操作來源
            string ip = HttpContext.Connection.RemoteIpAddress.ToString();
            // 呼叫資料傳輸物件執行刪除操作，並傳入使用者ID、IP位址和年度行事曆資料
            return shiftWeekdayDTO.Delete(userId, ip, yearCalendar);
        }
    }
}
