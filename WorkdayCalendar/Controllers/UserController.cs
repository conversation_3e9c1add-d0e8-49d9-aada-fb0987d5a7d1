using System.Data;                         // 資料表操作

using Microsoft.AspNetCore.Authorization;  // 提供授權相關功能
using Microsoft.AspNetCore.Mvc;           // ASP.NET Core MVC 核心功能
using Microsoft.Extensions.Configuration;  // 組態設定存取

using Newtonsoft.Json;                     // JSON 序列化/反序列化

namespace WorkdayCalendar.Controllers
{
    /// <summary>
    /// 使用者控制器，提供使用者相關的 API
    /// </summary>
    [Authorize]
    [ApiController]
    [Route("/api/[controller]/[action]")]
    public class UserController : ControllerBase
    {
        /// <summary>
        /// 組態設定物件，用於存取應用程式設定
        /// </summary>
        private readonly IConfiguration Configuration;

        /// <summary>
        /// 員工資料存取物件，用於操作員工相關資料
        /// </summary>
        private readonly Org.Sinotech.EmployeeDao _EmployeeDao;
 
        /// <summary>
        /// 建構函式，初始化 UserController 的新實例
        /// </summary>
        /// <param name="employeeDao">員工資料存取物件</param>
        /// <param name="configuration">應用程式組態設定</param>
        public UserController(Org.Sinotech.EmployeeDao employeeDao, IConfiguration configuration)
        {
            _EmployeeDao = employeeDao;
            Configuration = configuration;
        }

        /// <summary>
        /// 檢查當前登入使用者是否為系統管理員
        /// </summary>
        /// <returns>如果是管理員返回 true，否則返回 false</returns>
        /// <remarks>
        /// GET: /api/User/IsAdmin
        /// 此 API 會檢查當前已驗證的使用者是否具有管理員權限。
        /// </remarks>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public bool IsAdmin()
        {
            string connectionString = Configuration.GetSecuredConnectionString("Workday");
            string userName = HttpContext.User.Identity.Name;
            string userId = Utility.GetValidUserID(userName);
            return Utility.IsAdmin(userId, connectionString);
        }

        /// <summary>
        /// 取得所有部門資料
        /// </summary>
        /// <returns>JSON 格式的部門資料清單</returns>
        /// <remarks>
        /// GET: /api/User/Departments
        /// 取得系統中所有有效的部門資料，包含部門代碼、部門簡稱和部門全稱。
        /// 快取設為不儲存，以確保取得最新資料。
        /// </remarks>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public string Departments()
        {
            string connectionString = Configuration.GetSecuredConnectionString("MIS");
            string sql = "SELECT DeptNo, DeptSName, DeptName FROM vwDeptData WHERE StNo=1;";
            DataTable dt = SqlUtil.GetDataTable(connectionString, sql);
            string ret = JsonConvert.SerializeObject(dt);
            return ret;
        }

        /// <summary>
        /// 取得當前登入使用者的員工編號
        /// </summary>
        /// <returns>當前登入使用者的員工編號</returns>
        /// <remarks>
        /// GET: /api/User/UserId
        /// 從已驗證的使用者身分中取得員工編號。
        /// 快取設為不儲存，以確保取得最新資料。
        /// </remarks>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public string UserId()
        {
            return Utility.GetValidUserID(HttpContext.User.Identity.Name);
        }

        /// <summary>
        /// 取得當前登入使用者的中文姓名
        /// </summary>
        /// <returns>當前登入使用者的中文姓名</returns>
        /// <remarks>
        /// GET: /api/User/UserName
        /// 此為無參數重載，會自動取得當前登入使用者的姓名。
        /// 快取設為不儲存，以確保取得最新資料。
        /// </remarks>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public string UserName()
        {
            return UserName(UserId());
        }

        /// <summary>
        /// 取得指定使用者的中文姓名
        /// </summary>
        /// <param name="userid">員工編號</param>
        /// <returns>指定使用者的中文姓名，若找不到則返回空字串</returns>
        /// <remarks>
        /// GET: /api/User/UserName/{userid}
        /// 根據員工編號查詢對應的中文姓名。
        /// 如果未提供 userid 或為空，則返回當前登入使用者的姓名。
        /// 快取設為不儲存，以確保取得最新資料。
        /// </remarks>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/{userid}")]
        public string UserName(string userid)
        {
            if (string.IsNullOrWhiteSpace(userid))
            {
                userid = UserId();
            }
            string ret = _EmployeeDao.GetName(userid);
            if (ret == null)
            {
                ret = "";
            }
            return ret;
        }

        /// <summary>
        /// 取得當前登入使用者的詳細資料
        /// </summary>
        /// <returns>JSON 格式的使用者詳細資料</returns>
        /// <remarks>
        /// GET: /api/User/UserDetail
        /// 此為無參數重載，會自動取得當前登入使用者的詳細資料。
        /// 快取設為不儲存，以確保取得最新資料。
        /// </remarks>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public string UserDetail()
        {
            return UserDetail(UserId());
        }

        /// <summary>
        /// 取得指定使用者的詳細資料
        /// </summary>
        /// <param name="userid">員工編號</param>
        /// <returns>JSON 格式的使用者詳細資料，若找不到則返回空字串</returns>
        /// <remarks>
        /// GET: /api/User/UserDetail/{userid}
        /// 根據員工編號查詢詳細資料，包含部門、職稱、電子郵件等資訊。
        /// 如果未提供 userid 或為空，則返回當前登入使用者的資料。
        /// 快取設為不儲存，以確保取得最新資料。
        /// </remarks>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/{userid}")]
        public string UserDetail(string userid)
        {
            string ret;
            if (string.IsNullOrWhiteSpace(userid))
            {
                userid = UserId();
            }
            string connectionString = Configuration.GetSecuredConnectionString("MIS");
            string sql = $"SELECT vwCard_Emp.EmpNo, vwCard_Emp.CName, vwCard_Emp.DeptNo, vwDeptData.DeptName, vwDeptData.DeptSName, vwCard_Emp.Email, vwCard_Emp.JobNo, vwCard_Emp.RankNo, vwCard_Emp.RankName, vwCard_Emp.JobName, vwDeptTeamEmp.TeamID, vwDeptTeam.TeamCName FROM vwDeptTeam INNER JOIN vwDeptTeamEmp ON vwDeptTeam.TeamID = vwDeptTeamEmp.TeamID RIGHT OUTER JOIN vwCard_Emp ON vwDeptTeamEmp.EmpNo = vwCard_Emp.EmpNo LEFT OUTER JOIN vwDeptData ON vwCard_Emp.DeptNo = vwDeptData.DeptNo WHERE (vwCard_Emp.EmpNo = '{userid}') ORDER BY vwCard_Emp.EmpNo";
            DataTable dt = SqlUtil.GetDataTable(connectionString, sql);
            ret = JsonConvert.SerializeObject(dt);

            if (ret == null)
            {
                ret = "";
            }
            return ret;
        }

        /// <summary>
        /// 取得當前登入使用者的部門代碼
        /// </summary>
        /// <returns>當前登入使用者的部門代碼</returns>
        /// <remarks>
        /// GET: /api/User/DeptNo
        /// 此為無參數重載，會自動取得當前登入使用者的部門代碼。
        /// 快取設為不儲存，以確保取得最新資料。
        /// </remarks>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public int DeptNo()
        {
            return DeptNo(UserId());
        }

        /// <summary>
        /// 取得指定使用者的部門代碼
        /// </summary>
        /// <param name="userid">員工編號</param>
        /// <returns>指定使用者的部門代碼</returns>
        /// <remarks>
        /// GET: /api/User/DeptNo/{userid}
        /// 根據員工編號查詢對應的部門代碼。
        /// 如果未提供 userid 或為空，則返回當前登入使用者的部門代碼。
        /// 快取設為不儲存，以確保取得最新資料。
        /// </remarks>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/{userid}")]
        public int DeptNo(string userid)
        {
            if (string.IsNullOrWhiteSpace(userid))
            {
                userid = UserId();
            }
            int ret = _EmployeeDao.GetDeptNo(userid);
            return ret;
        }

        /// <summary>
        /// 取得所有使用者的基本資料
        /// </summary>
        /// <returns>JSON 格式的所有使用者資料清單</returns>
        /// <remarks>
        /// GET: /api/User/Users
        /// 取得系統中所有有效使用者的基本資料，包含員工編號、姓名、部門、職稱等資訊。
        /// 快取設為不儲存，以確保取得最新資料。
        /// </remarks>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public string Users()
        {
            string connectionString = Configuration.GetSecuredConnectionString("MIS");
            string sql = "SELECT vwCard_Emp.EmpNo, vwCard_Emp.CName, vwCard_Emp.DeptNo, vwDeptData.DeptName, vwDeptData.DeptSName, vwCard_Emp.Email, vwCard_Emp.JobNo, vwCard_Emp.RankNo, vwCard_Emp.RankName, vwCard_Emp.JobName, vwDeptTeamEmp.TeamID, vwDeptTeam.TeamCName FROM vwDeptTeam INNER JOIN vwDeptTeamEmp ON vwDeptTeam.TeamID = vwDeptTeamEmp.TeamID RIGHT OUTER JOIN vwCard_Emp ON vwDeptTeamEmp.EmpNo = vwCard_Emp.EmpNo LEFT OUTER JOIN vwDeptData ON vwCard_Emp.DeptNo = vwDeptData.DeptNo WHERE Status=0 ORDER BY vwCard_Emp.EmpNo;";
            DataTable dt = SqlUtil.GetDataTable(connectionString, sql);
            return JsonConvert.SerializeObject(dt);
        }

        /// <summary>
        /// 取得當前登入使用者的部門簡稱
        /// </summary>
        /// <returns>當前登入使用者的部門簡稱</returns>
        /// <remarks>
        /// GET: /api/User/DeptSName
        /// 此為無參數重載，會自動取得當前登入使用者的部門簡稱。
        /// 快取設為不儲存，以確保取得最新資料。
        /// </remarks>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public string DeptSName()
        {
            return DeptSName(userid: UserId());
        }

        /// <summary>
        /// 取得指定使用者的部門簡稱
        /// </summary>
        /// <param name="userid">員工編號</param>
        /// <returns>指定使用者的部門簡稱，若找不到則返回空字串</returns>
        /// <remarks>
        /// GET: /api/User/DeptSName/{userid}
        /// 根據員工編號查詢對應的部門簡稱。
        /// 如果未提供 userid 或為空，則返回當前登入使用者的部門簡稱。
        /// 快取設為不儲存，以確保取得最新資料。
        /// </remarks>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/{userid}")]
        public string DeptSName(string userid)
        {
            if (string.IsNullOrWhiteSpace(userid))
            {
                userid = UserId();
            }
            string ret = _EmployeeDao.GetDeptSName(userid);
            if (ret == null)
            {
                ret = "";
            }
            return ret;
        }
    }
}