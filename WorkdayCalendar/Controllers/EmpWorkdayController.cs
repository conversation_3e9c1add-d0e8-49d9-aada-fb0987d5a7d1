using System;
using System.Data;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;

using Newtonsoft.Json;

using WorkdayCalendar.DTO;
using WorkdayCalendar.Models;


namespace WorkdayCalendar.Controllers
{
    /// <summary>
    /// 員工工作日曆管理控制器<br/>
    /// 負責處理與員工工作日曆相關的所有 Web API 請求，包括新增、查詢、更新和刪除員工工作日曆資料。
    /// 使用 ADO.NET 透過 DTO 進行資料庫操作。
    /// 所有操作都需要經過身分驗證。
    /// </summary>
    [Authorize]
    [ApiController]
    [Route("/api/[controller]/[action]")]
    public class EmpWorkdayController : ControllerBase
    {
        private readonly string _connectionString;
        private readonly EmpWorkdayDto _empWorkdayDto;

        /// <summary>
        /// 建構函式，初始化 EmpWorkdayController 的新實例
        /// </summary>
        /// <param name="config">應用程式組態，用於取得資料庫連線字串</param>
        public EmpWorkdayController(IConfiguration config)
        {
            _connectionString = config.GetSecuredConnectionString("Workday");
            _empWorkdayDto = new EmpWorkdayDto(_connectionString);
        }


        /// <summary>
        /// 批次增加員工特殊工作日曆
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Add([FromBody] AddEmpWorkdaysRequest request)
        {
            if (request == null
                || string.IsNullOrEmpty(request.EmpNo)
                || request.Year <= 1990
                || request.ShiftId <= 0
                || request.StartDate == default(DateTime)
                || request.EndDate == default(DateTime)
                || request.StartDate > request.EndDate
                )
            {
                return BadRequest("請設定正確資料再新增行事曆");
            }

            request.StartDate = request.StartDate.ToLocalTime().Date; // 確保起始日期為日期格式，不包含時間部分
            request.EndDate = request.EndDate.ToLocalTime().Date; // 確保結束日期為日期格式，不包含時間部分

            // 檢查開始和結束日期是否在同一年內
            if (request.StartDate.Year != request.Year || request.EndDate.Year != request.Year)
            {
                return BadRequest("開始和結束日期必須在同一年內");
            }
            // 檢查使用者是否為管理員
            string userName = HttpContext.User.Identity?.Name ?? "";
            string userId = Utility.GetValidUserID(userName);
            if (Utility.IsAdmin(userId, _connectionString) == false)
            {
                return Forbid("您沒有權限執行此操作");
            }

            try
            {
                string ip = HttpContext.Connection?.RemoteIpAddress?.ToString() ?? "";

                bool success = _empWorkdayDto.AddOrUpdateEmpWorkdays(userId, ip, request.EmpNo, request.ShiftId, request.StartDate, request.EndDate);

                if (success)
                {
                    return Ok(true);
                }
                else
                {
                    return Ok(false);
                }
            }
            catch (Exception ex)
            {
                // 在正式環境中應使用更完善的日誌記錄
                Console.WriteLine($"建立員工年度工作日曆時發生錯誤: {ex.Message}");
                return StatusCode(500, "發生錯誤，無法建立年度工作日曆");
            }
        }


        /// <summary>
        /// 批次新增指定員工的年度特殊工作日曆
        /// </summary>
        /// <param name="request">包含員工編號、年份和班別編號的請求物件</param>
        /// <returns>新增成功返回 true，否則返回 false</returns>
        /// <remarks>
        /// POST: /api/EmpWorkday/AddYearlyWorkdays
        /// 此 API 會根據指定的員工和班別，產生一整年的特殊工作日曆資料。
        /// 這解決了原先前端意圖與後端實作不符的問題。
        /// </remarks>
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult AddYearlyWorkdays([FromBody] AddYearlyWorkdaysRequest request)
        {
            if (request == null || string.IsNullOrEmpty(request.EmpNo) || request.Year <= 0 || request.ShiftId <= 0)
            {
                return BadRequest("請求參數無效。");
            }

            try
            {
                // 檢查使用者是否為管理員
                string userName = HttpContext.User.Identity?.Name ?? "";
                string userId = Utility.GetValidUserID(userName);
                if (Utility.IsAdmin(userId, _connectionString) == false)
                {
                    return Forbid("您沒有權限執行此操作");
                }
                string ip = HttpContext.Connection?.RemoteIpAddress?.ToString() ?? "";

                bool success = _empWorkdayDto.AddEmpWorkdays(userId, ip, request.EmpNo, request.Year, request.ShiftId);

                if (success)
                {
                    return Ok(true);
                }
                else
                {
                    return Ok(false);
                }
            }
            catch (Exception ex)
            {
                // 在正式環境中應使用更完善的日誌記錄
                Console.WriteLine($"建立員工年度工作日曆時發生錯誤: {ex.Message}");
                return StatusCode(500, "伺服器內部錯誤，無法建立年度工作日曆。");
            }
        }

        /// <summary>
        /// 檢查是否可以新增員工特殊工作日曆資料
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="year">年份</param>
        /// <returns>可以新增返回 true，否則返回 false</returns>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult CanAdd(string empNo, int year)
        {
            if (string.IsNullOrEmpty(empNo) || year <= 0)
            {
                return BadRequest("參數無效");
            }
            try
            {
                // 檢查使用者是否為管理員
                string userName = HttpContext.User.Identity?.Name ?? "";
                string userId = Utility.GetValidUserID(userName);
                if (Utility.IsAdmin(userId, _connectionString) == false)
                {
                    return Forbid("您沒有權限執行此操作");
                }
                return Ok(_empWorkdayDto.CanAdd(empNo, year));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"檢查是否可新增員工行事曆時發生錯誤: {ex.Message}");
                return StatusCode(500, "伺服器錯誤");
            }
        }

        /// <summary>
        /// 取得指定員工整年的特殊工作日曆資料
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="year">年份</param>
        /// <returns>JSON 格式的員工特殊工作日曆資料清單</returns>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult GetEmpWorkdaysYear(string empNo, int year)
        {
            if (string.IsNullOrEmpty(empNo) || year <= 0)
            {
                return BadRequest("參數無效");
            }
            try
            {
                DataTable dt = _empWorkdayDto.GetEmpWorkdays(empNo, year);
                string jsonResult = JsonConvert.SerializeObject(dt);
                return Content(jsonResult, "application/json");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得員工行事曆時發生錯誤: {ex.Message}");
                return StatusCode(500, "伺服器錯誤");
            }
        }

        /// <summary>
        /// 取得指定員工的工作日曆資料
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="year">年份</param>
        /// <param name="shiftId">班別編號</param>
        /// <returns>JSON 格式的員工工作日曆資料清單</returns>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult GetEmpWorkdays(string empNo, int year, int shiftId)
        {
            if (string.IsNullOrEmpty(empNo) || year <= 0)
            {
                return BadRequest("參數無效");
            }
            try
            {
                DataTable dt = _empWorkdayDto.GetEmpWorkdays(empNo, year, shiftId);
                string jsonResult = JsonConvert.SerializeObject(dt);
                return Content(jsonResult, "application/json");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"取得員工行事曆時發生錯誤: {ex.Message}");
                return StatusCode(500, "伺服器錯誤");
            }
        }

        /// <summary>
        /// 更新員工工作日曆資料
        /// </summary>
        /// <param name="empWorkday">要更新的員工工作日曆資料</param>
        /// <returns>更新成功返回 true，否則返回 false</returns>
        [HttpPut]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Update([FromBody] EmpWorkday empWorkday)
        {
            if (empWorkday == null)
            {
                return BadRequest("無效的請求資料");
            }

            try
            {
                string userName = HttpContext.User.Identity?.Name ?? "";
                string userId = Utility.GetValidUserID(userName);
                string ip = HttpContext.Connection?.RemoteIpAddress?.ToString() ?? "";
                if (Utility.IsAdmin(userId, _connectionString) == false)
                {
                    return Forbid("您沒有權限執行此操作");
                }
                bool success = _empWorkdayDto.Update(userId, ip, empWorkday);
                return Ok(success);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新員工工作日曆時發生錯誤: {ex.Message}");
                return StatusCode(500, "伺服器內部錯誤");
            }
        }

        /// <summary>
        /// 刪除指定的員工工作日曆資料
        /// </summary>
        /// <param name="empNo">員工編號</param>
        /// <param name="workDate">工作日期</param>
        /// <returns>刪除成功返回 true，否則返回 false</returns>
        [HttpDelete]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Delete(string empNo, DateTime workDate)
        {
            if (string.IsNullOrEmpty(empNo))
            {
                return BadRequest("員工編號不可為空");
            }

            try
            {
                string userName = HttpContext.User.Identity?.Name ?? "";
                string userId = Utility.GetValidUserID(userName);
                string ip = HttpContext.Connection?.RemoteIpAddress?.ToString() ?? "";

                bool success = _empWorkdayDto.Delete(userId, ip, empNo, workDate);
                return Ok(success);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"刪除員工工作日曆時發生錯誤: {ex.Message}");
                return StatusCode(500, "伺服器內部錯誤");
            }
        }
    }

    /// <summary>
    /// 新增年度工作日曆的請求模型
    /// </summary>
    public class AddYearlyWorkdaysRequest
    {
        public string EmpNo { get; set; }
        public int Year { get; set; }
        public int ShiftId { get; set; }
    }

    /// <summary>
    /// 新增年度工作日曆的請求模型
    /// </summary>
    public class AddEmpWorkdaysRequest
    {
        public string EmpNo { get; set; }
        public int Year { get; set; }
        public int ShiftId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }

}