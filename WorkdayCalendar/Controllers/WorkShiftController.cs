using System.Data;                         // 資料表操作

using Microsoft.AspNetCore.Authorization;  // 提供授權相關功能
using Microsoft.AspNetCore.Mvc;           // ASP.NET Core MVC 核心功能
using Microsoft.Extensions.Configuration;  // 組態設定存取

using Newtonsoft.Json;                     // JSON 序列化/反序列化

using WorkdayCalendar.DTO;                 // 資料傳輸物件
using WorkdayCalendar.Models;              // 資料模型

namespace WorkdayCalendar.Controllers
{

    /// <summary>
    /// 班別管理控制器<br />
    /// 負責處理與班別相關的所有 Web API 請求，包括新增、查詢、更新和刪除班別資訊。
    /// 所有操作都需要經過身分驗證。
    /// </summary>
    [Authorize]
    [ApiController]
    [Route("/api/[controller]/[action]")]
    public class WorkShiftController : ControllerBase
    {
        /// <summary>
        /// 組態設定物件，用於存取應用程式設定
        /// </summary>
        private readonly IConfiguration Configuration;

        /// <summary>
        /// 班別資料存取層物件
        /// </summary>
        private WorkShiftDto workShiftDTO;

        /// <summary>
        /// 建構函式，初始化 WorkShiftController 的新實例
        /// </summary>
        /// <param name="configuration">應用程式組態設定</param>
        public WorkShiftController(IConfiguration configuration)
        {
            Configuration = configuration;
            workShiftDTO = new WorkShiftDto(Configuration.GetSecuredConnectionString("Workday"));
        }

        /// <summary>
        /// 取得所有班別資料
        /// </summary>
        /// <returns>JSON 格式的班別資料清單</returns>
        /// <remarks>
        /// GET: /api/WorkShift/GetDataTable
        /// 此 API 會回傳所有班別資料，並快取設為不儲存。
        /// 回傳的 JSON 中時間格式會移除 ":00" 結尾。
        /// </remarks>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public string Get()
        {
            // 從資料存取層取得所有班別資料
            DataTable dt = workShiftDTO.Get();
            // 將DataTable轉成JSON字串格式
            string ret = JsonConvert.SerializeObject(dt);
            return ret.Replace(":00\"", "\"");
        }

        /// <summary>
        /// 新增班別資料
        /// </summary>
        /// <param name="workShift">要新增的班別資料</param>
        /// <returns>新增的班別 ID (大於 0 表示成功)</returns>
        /// <remarks>
        /// POST: /api/WorkShift/Add
        /// 需要傳入完整的班別資料模型，系統會自動記錄操作者資訊。
        /// </remarks>
        [HttpPost]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public int Add(WorkShift workShift)
        {
            string connectionString = Configuration.GetSecuredConnectionString("Workday");
            string userName = HttpContext.User.Identity.Name;
            string userId = Utility.GetValidUserID(userName);
            // 驗證使用者是否為管理員
            if (Utility.IsAdmin(userId, connectionString))
            {
                string ip = HttpContext.Connection.RemoteIpAddress.ToString();
                return workShiftDTO.Add(userId, ip, workShift);
            }
            else
            {
                // 如果不是管理員，則返回 -1 表示新增失敗
                return -1;
            }
        }

        /// <summary>
        /// 更新班別資料
        /// </summary>
        /// <param name="workShift">要更新的班別資料</param>
        /// <returns>更新成功返回 true，否則返回 false</returns>
        /// <remarks>
        /// PUT: /api/WorkShift/Update
        /// 需要傳入完整的班別資料模型，系統會自動記錄最後修改者資訊。
        /// </remarks>
        [HttpPut]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]")]
        public IActionResult Update(WorkShift workShift)
        {
            bool ret = false;
            if (HttpContext.User != null && HttpContext.User.Identity != null && HttpContext.User.Identity.Name != null)
            {
                string userName = HttpContext.User.Identity.Name;
                string userId = Utility.GetValidUserID(userName);
                string connectionString = Configuration.GetSecuredConnectionString("Workday");
                if (Utility.IsAdmin(userId, connectionString) == false)
                {
                    return Forbid("您沒有權限執行此操作");
                }

                if (HttpContext.Connection != null && HttpContext.Connection.RemoteIpAddress != null)
                {
                    string ip = HttpContext.Connection.RemoteIpAddress.ToString();
                    ret = workShiftDTO.Update(userId, ip, workShift);
                }
            }
            return Ok(ret);
        }

        /// <summary>
        /// 刪除指定的班別資料
        /// </summary>
        /// <param name="shiftId">要刪除的班別 ID</param>
        /// <returns>刪除成功返回 true，否則返回 false</returns>
        /// <remarks>
        /// DELETE: /api/WorkShift/Delete/{ShiftId}
        /// 此操作會從資料庫中刪除指定的班別記錄，並記錄操作者資訊。
        /// 請注意，此操作無法復原。
        /// </remarks>
        [HttpDelete]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [Route("/api/[controller]/[action]/{shiftId}")]
        public IActionResult Delete(int shiftId)
        {
            // 檢查使用者是否為管理員
            string userName = HttpContext.User.Identity?.Name ?? "";
            string userId = Utility.GetValidUserID(userName);
            string connectionString = Configuration.GetSecuredConnectionString("Workday");
            if (Utility.IsAdmin(userId, connectionString) == false)
            {
                return Forbid("您沒有權限執行此操作");
            }
            string ip = HttpContext.Connection.RemoteIpAddress.ToString();
            return Ok(workShiftDTO.Delete(userId, ip, shiftId));
        }
    }
}
