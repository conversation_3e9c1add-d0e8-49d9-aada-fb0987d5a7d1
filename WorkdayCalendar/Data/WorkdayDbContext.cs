using Microsoft.EntityFrameworkCore;
using WorkdayCalendar.Models;

namespace WorkdayCalendar.Data
{
    /// <summary>
    /// 工作日曆資料庫上下文類別
    /// 提供對工作日曆相關資料表的存取功能
    /// 使用 Entity Framework Core 與 SQL Server 進行資料操作
    /// </summary>
    public class WorkdayDbContext : DbContext
    {
        /// <summary>
        /// 建構函式
        /// 接收 DbContextOptions 參數以設定資料庫連接選項
        /// </summary>
        /// <param name="options">資料庫上下文選項設定</param>
        public WorkdayDbContext(DbContextOptions<WorkdayDbContext> options)
            : base(options)
        {
        }

        /// <summary>
        /// 工作班別資料表
        /// 定義各種工作班別的基本資訊和時間設定
        /// </summary>
        public DbSet<WorkShift> WorkShifts { get; set; }

        /// <summary>
        /// 工作日資料表
        /// 記錄特定日期的工作時間安排
        /// </summary>
        public DbSet<Workday> Workdays { get; set; }

        /// <summary>
        /// 員工工作日資料表
        /// 記錄員工在特定日期的個人工作安排
        /// </summary>
        public DbSet<EmpWorkday> EmpWorkdays { get; set; }

        /// <summary>
        /// 員工工作班別資料表
        /// 記錄員工所屬的工作班別資訊
        /// </summary>
        public DbSet<EmpWorkShift> EmpWorkShifts { get; set; }

        /// <summary>
        /// 班別週間工作日資料表
        /// 定義各班別在週間不同日期的工作時間設定
        /// </summary>
        public DbSet<ShiftWeekday> ShiftWeekdays { get; set; }

        /// <summary>
        /// 假日資料表
        /// 記錄國定假日和特殊假日資訊
        /// </summary>
        public DbSet<Holiday> Holidays { get; set; }

    }
} 