{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "experimentalDecorators": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "baseUrl": ".", "types": ["node"], "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["ClientApp/src/**/*.ts", "ClientApp/src/**/*.tsx", "ClientApp/src/**/*.vue", "ClientApp/tests/**/*.ts", "ClientApp/tests/**/*.tsx", "ClientApp/src/components/service/CalendarService.js"], "exclude": ["node_modules", "dist", "public"]}