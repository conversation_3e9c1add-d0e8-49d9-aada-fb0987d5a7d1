using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace WorkdayCalendar.Tests
{
    /// <summary>
    /// 獨立的 MarkHolidays 效能測試
    /// 不依賴於主項目，可以直接運行
    /// </summary>
    public class StandaloneMarkHolidaysTest
    {
        // 簡化的測試用資料結構
        public class TestHoliday
        {
            public string Date { get; set; }
            public string Name { get; set; }
            public bool IsLunar { get; set; }
        }

        public class TestWorkday
        {
            public short ShiftId { get; set; }
            public DateTime WorkDate { get; set; }
            public byte WeekDay { get; set; }
            public short DayType { get; set; }
            public double WorkHours { get; set; }
            public string Comment { get; set; } = "";
        }

        public class TestWorkdayParam
        {
            public int Year { get; set; }
            public int ShiftId { get; set; }
            public DateTime StartDate { get; set; }
            public TestHoliday[] Holidays { get; set; }
        }

        /// <summary>
        /// 執行獨立效能測試
        /// </summary>
        public static void RunStandaloneTest()
        {
            Console.WriteLine("=== 獨立 MarkHolidays 效能測試 ===\n");

            // 準備測試資料
            var (param, holidays, holidayLookup, workdays) = PrepareTestData();

            Console.WriteLine($"測試資料準備完成:");
            Console.WriteLine($"- 假日數量: {holidays.Length}");
            Console.WriteLine($"- 工作日數量: {workdays.Count}");

            // 執行多次測試
            const int testRuns = 100;
            Console.WriteLine($"- 測試執行次數: {testRuns} 次\n");

            var linqResults = new List<TimeSpan>();
            var dictResults = new List<TimeSpan>();

            // 預熱 JIT
            Console.WriteLine("JIT 預熱中...");
            TestLinqVersion(param, holidays, workdays.Take(10).ToList());
            TestDictionaryVersion(param, holidayLookup, workdays.Take(10).ToList());
            Console.WriteLine("預熱完成\n");

            // 測試 LINQ 版本
            Console.WriteLine("測試 LINQ 版本...");
            for (int i = 0; i < testRuns; i++)
            {
                var result = TestLinqVersion(param, holidays, workdays);
                linqResults.Add(result.elapsed);
                if (i % 10 == 0) Console.Write(".");
            }
            Console.WriteLine(" 完成");

            // 測試 Dictionary 版本
            Console.WriteLine("測試 Dictionary 版本...");
            for (int i = 0; i < testRuns; i++)
            {
                var result = TestDictionaryVersion(param, holidayLookup, workdays);
                dictResults.Add(result.elapsed);
                if (i % 10 == 0) Console.Write(".");
            }
            Console.WriteLine(" 完成");

            // 顯示結果
            DisplayResults(linqResults, dictResults, workdays.Count);

            // 驗證正確性
            VerifyCorrectness(param, holidays, holidayLookup, workdays);
        }

        /// <summary>
        /// 準備測試資料
        /// </summary>
        private static (TestWorkdayParam param, DateTime[] holidays, Dictionary<DateTime, TestHoliday> holidayLookup, List<TestWorkday> workdays) PrepareTestData()
        {
            // 2024年台灣國定假日
            var holidayData = new TestHoliday[]
            {
                new TestHoliday { Date = "2024-01-01", Name = "元旦", IsLunar = false },
                new TestHoliday { Date = "2024-02-08", Name = "農曆除夕", IsLunar = true },
                new TestHoliday { Date = "2024-02-09", Name = "農曆新年初一", IsLunar = true },
                new TestHoliday { Date = "2024-02-10", Name = "農曆新年初二", IsLunar = true },
                new TestHoliday { Date = "2024-02-11", Name = "農曆新年初三", IsLunar = true },
                new TestHoliday { Date = "2024-02-12", Name = "農曆新年初四", IsLunar = true },
                new TestHoliday { Date = "2024-02-28", Name = "和平紀念日", IsLunar = false },
                new TestHoliday { Date = "2024-04-04", Name = "兒童節", IsLunar = false },
                new TestHoliday { Date = "2024-04-05", Name = "清明節", IsLunar = false },
                new TestHoliday { Date = "2024-05-01", Name = "勞動節", IsLunar = false },
                new TestHoliday { Date = "2024-06-10", Name = "端午節", IsLunar = true },
                new TestHoliday { Date = "2024-09-17", Name = "中秋節", IsLunar = true },
                new TestHoliday { Date = "2024-10-10", Name = "國慶日", IsLunar = false },
            };

            var param = new TestWorkdayParam
            {
                Year = 2024,
                ShiftId = 1,
                StartDate = new DateTime(2024, 1, 1),
                Holidays = holidayData
            };

            var holidays = holidayData.Select(h => DateTime.Parse(h.Date)).ToArray();

            var holidayLookup = new Dictionary<DateTime, TestHoliday>();
            for (int i = 0; i < holidays.Length; i++)
            {
                holidayLookup[holidays[i].Date] = holidayData[i];
            }

            // 建立整年的工作日資料
            var workdays = new List<TestWorkday>();
            var startDate = new DateTime(2024, 1, 1);
            var endDate = new DateTime(2024, 12, 31);

            for (var date = startDate; date <= endDate; date = date.AddDays(1))
            {
                workdays.Add(new TestWorkday
                {
                    ShiftId = 1,
                    WorkDate = date,
                    WeekDay = (byte)date.DayOfWeek,
                    DayType = 1,
                    WorkHours = 8.0,
                    Comment = ""
                });
            }

            return (param, holidays, holidayLookup, workdays);
        }

        /// <summary>
        /// 測試 LINQ 版本
        /// </summary>
        private static (TimeSpan elapsed, int holidaysFound) TestLinqVersion(
            TestWorkdayParam param, DateTime[] holidays, List<TestWorkday> workdays)
        {
            var testWorkdays = workdays.Select(w => CloneWorkday(w)).ToList();
            int holidaysFound = 0;

            var stopwatch = Stopwatch.StartNew();

            foreach (var workday in testWorkdays)
            {
                // LINQ 版本邏輯
                var holidayIndex = holidays
                    .Select((date, index) => new { Date = date, Index = index })
                    .FirstOrDefault(h => h.Date.Date == workday.WorkDate.Date)?.Index;

                if (holidayIndex.HasValue)
                {
                    int index = holidayIndex.Value;
                    workday.Comment = param.Holidays[index].Name;
                    workday.WorkHours = 0;
                    SetHolidayDayType(workday);
                    holidaysFound++;
                }
            }

            stopwatch.Stop();
            return (stopwatch.Elapsed, holidaysFound);
        }

        /// <summary>
        /// 測試 Dictionary 版本
        /// </summary>
        private static (TimeSpan elapsed, int holidaysFound) TestDictionaryVersion(
            TestWorkdayParam param, Dictionary<DateTime, TestHoliday> holidayLookup, List<TestWorkday> workdays)
        {
            var testWorkdays = workdays.Select(w => CloneWorkday(w)).ToList();
            int holidaysFound = 0;

            var stopwatch = Stopwatch.StartNew();

            foreach (var workday in testWorkdays)
            {
                // Dictionary 版本邏輯
                if (holidayLookup.TryGetValue(workday.WorkDate.Date, out TestHoliday holiday))
                {
                    workday.Comment = holiday.Name;
                    workday.WorkHours = 0;
                    SetHolidayDayType(workday);
                    holidaysFound++;
                }
            }

            stopwatch.Stop();
            return (stopwatch.Elapsed, holidaysFound);
        }

        /// <summary>
        /// 複製工作日物件
        /// </summary>
        private static TestWorkday CloneWorkday(TestWorkday original)
        {
            return new TestWorkday
            {
                ShiftId = original.ShiftId,
                WorkDate = original.WorkDate,
                WeekDay = original.WeekDay,
                DayType = original.DayType,
                WorkHours = original.WorkHours,
                Comment = original.Comment
            };
        }

        /// <summary>
        /// 設定假日日期類型
        /// </summary>
        private static void SetHolidayDayType(TestWorkday workday)
        {
            switch (workday.WeekDay)
            {
                case 0: workday.DayType = 6; break; // 週日國定假日
                case 1: case 2: case 3: case 4: case 5: workday.DayType = 4; break; // 週間國定假日
                case 6: workday.DayType = 5; break; // 週六國定假日
            }
        }

        /// <summary>
        /// 顯示測試結果
        /// </summary>
        private static void DisplayResults(List<TimeSpan> linqResults, List<TimeSpan> dictResults, int totalWorkdays)
        {
            var linqTimes = linqResults.Select(t => t.TotalMilliseconds).ToArray();
            var dictTimes = dictResults.Select(t => t.TotalMilliseconds).ToArray();

            Console.WriteLine("\n=== 效能測試結果 ===");
            
            Console.WriteLine($"LINQ 版本 (O(n)):");
            Console.WriteLine($"  平均時間: {linqTimes.Average():F4} ms");
            Console.WriteLine($"  最快時間: {linqTimes.Min():F4} ms");
            Console.WriteLine($"  最慢時間: {linqTimes.Max():F4} ms");
            Console.WriteLine($"  標準差: {CalculateStandardDeviation(linqTimes):F4} ms");

            Console.WriteLine($"\nDictionary 版本 (O(1)):");
            Console.WriteLine($"  平均時間: {dictTimes.Average():F4} ms");
            Console.WriteLine($"  最快時間: {dictTimes.Min():F4} ms");
            Console.WriteLine($"  最慢時間: {dictTimes.Max():F4} ms");
            Console.WriteLine($"  標準差: {CalculateStandardDeviation(dictTimes):F4} ms");

            double improvement = linqTimes.Average() / dictTimes.Average();
            Console.WriteLine($"\n=== 效能提升 ===");
            Console.WriteLine($"Dictionary 版本比 LINQ 版本快 {improvement:F2} 倍");
            Console.WriteLine($"效能提升: {((improvement - 1) * 100):F1}%");
            Console.WriteLine($"時間節省: {(linqTimes.Average() - dictTimes.Average()):F4} ms");
        }

        /// <summary>
        /// 計算標準差
        /// </summary>
        private static double CalculateStandardDeviation(double[] values)
        {
            if (values.Length <= 1) return 0;
            double avg = values.Average();
            double sumOfSquares = values.Sum(x => Math.Pow(x - avg, 2));
            return Math.Sqrt(sumOfSquares / (values.Length - 1));
        }

        /// <summary>
        /// 驗證結果正確性
        /// </summary>
        private static void VerifyCorrectness(TestWorkdayParam param, DateTime[] holidays, 
            Dictionary<DateTime, TestHoliday> holidayLookup, List<TestWorkday> workdays)
        {
            Console.WriteLine("\n=== 正確性驗證 ===");
            
            var result1 = TestLinqVersion(param, holidays, workdays);
            var result2 = TestDictionaryVersion(param, holidayLookup, workdays);

            if (result1.holidaysFound == result2.holidaysFound)
            {
                Console.WriteLine($"✅ 驗證通過：兩個版本都找到 {result1.holidaysFound} 個假日");
            }
            else
            {
                Console.WriteLine($"❌ 驗證失敗：LINQ版本找到 {result1.holidaysFound} 個假日，Dictionary版本找到 {result2.holidaysFound} 個假日");
            }
        }
    }
}
