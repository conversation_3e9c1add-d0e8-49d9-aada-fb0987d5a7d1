using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using WorkdayCalendar.Models;

namespace TestWorkdayCalendar
{
    /// <summary>
    /// MarkHolidays 控制台效能測試
    /// 可以直接運行查看效能測試結果
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class MarkHolidaysConsoleTest
    {
        /// <summary>
        /// 執行控制台效能測試
        /// </summary>
        public static void RunConsoleTest()
        {
            Console.WriteLine("=== MarkHolidays 方法效能測試 ===\n");

            // 準備測試資料
            var testData = PrepareTestData();
            var param = testData.param;
            var holidays = testData.holidays;
            var holidayLookup = testData.holidayLookup;
            var workdays = testData.workdays;

            Console.WriteLine($"測試資料準備完成:");
            Console.WriteLine($"- 假日數量: {holidays.Length}");
            Console.WriteLine($"- 工作日數量: {workdays.Count}");

            // 執行多次測試以獲得更準確的結果
            const int testRuns = 100;
            Console.WriteLine($"- 測試執行次數: {testRuns} 次\n");

            var linqResults = new List<TimeSpan>();
            var dictResults = new List<TimeSpan>();

            // JIT 預熱
            Console.WriteLine("JIT 預熱中...");
            TestLinqVersion(param, holidays, workdays.Take(10).ToList());
            TestDictionaryVersion(param, holidayLookup, workdays.Take(10).ToList());
            Console.WriteLine("預熱完成\n");

            // 測試 LINQ 版本
            Console.WriteLine("測試 LINQ 版本...");
            for (int i = 0; i < testRuns; i++)
            {
                var result = TestLinqVersion(param, holidays, workdays);
                linqResults.Add(result.elapsed);
                if (i % 10 == 0) Console.Write(".");
            }
            Console.WriteLine(" 完成");

            // 測試 Dictionary 版本
            Console.WriteLine("測試 Dictionary 版本...");
            for (int i = 0; i < testRuns; i++)
            {
                var result = TestDictionaryVersion(param, holidayLookup, workdays);
                dictResults.Add(result.elapsed);
                if (i % 10 == 0) Console.Write(".");
            }
            Console.WriteLine(" 完成");

            // 顯示結果
            DisplayResults(linqResults, dictResults, workdays.Count);

            // 驗證正確性
            VerifyCorrectness(param, holidays, holidayLookup, workdays);
        }

        /// <summary>
        /// 準備測試資料
        /// </summary>
        private static (WorkdayParam param, DateTime[] holidays, Dictionary<DateTime, Holiday> holidayLookup, List<Workday> workdays) PrepareTestData()
        {
            // 2024年台灣國定假日
            var holidayData = new Holiday[]
            {
                new Holiday { Date = "2024-01-01", Name = "元旦", IsLunar = false },
                new Holiday { Date = "2024-02-08", Name = "農曆除夕", IsLunar = true },
                new Holiday { Date = "2024-02-09", Name = "農曆新年初一", IsLunar = true },
                new Holiday { Date = "2024-02-10", Name = "農曆新年初二", IsLunar = true },
                new Holiday { Date = "2024-02-11", Name = "農曆新年初三", IsLunar = true },
                new Holiday { Date = "2024-02-12", Name = "農曆新年初四", IsLunar = true },
                new Holiday { Date = "2024-02-28", Name = "和平紀念日", IsLunar = false },
                new Holiday { Date = "2024-04-04", Name = "兒童節", IsLunar = false },
                new Holiday { Date = "2024-04-05", Name = "清明節", IsLunar = false },
                new Holiday { Date = "2024-05-01", Name = "勞動節", IsLunar = false },
                new Holiday { Date = "2024-06-10", Name = "端午節", IsLunar = true },
                new Holiday { Date = "2024-09-17", Name = "中秋節", IsLunar = true },
                new Holiday { Date = "2024-10-10", Name = "國慶日", IsLunar = false },
            };

            var param = new WorkdayParam
            {
                Year = 2024,
                ShiftId = 1,
                StartDate = new DateTime(2024, 1, 1),
                Holidays = holidayData
            };

            var holidays = holidayData.Select(h => DateTime.Parse(h.Date)).ToArray();

            var holidayLookup = new Dictionary<DateTime, Holiday>();
            for (int i = 0; i < holidays.Length; i++)
            {
                holidayLookup[holidays[i].Date] = holidayData[i];
            }

            // 建立整年的工作日資料
            var workdays = new List<Workday>();
            var startDate = new DateTime(2024, 1, 1);
            var endDate = new DateTime(2024, 12, 31);

            for (var date = startDate; date <= endDate; date = date.AddDays(1))
            {
                workdays.Add(new Workday
                {
                    ShiftId = 1,
                    WorkDate = date,
                    WeekDay = (byte)date.DayOfWeek,
                    DayType = 1,
                    WorkHours = 8.0,
                    Comment = ""
                });
            }

            return (param, holidays, holidayLookup, workdays);
        }

        /// <summary>
        /// 測試 LINQ 版本
        /// </summary>
        private static (TimeSpan elapsed, int holidaysFound) TestLinqVersion(
            WorkdayParam param, DateTime[] holidays, List<Workday> workdays)
        {
            var testWorkdays = workdays.Select(w => CloneWorkday(w)).ToList();
            int holidaysFound = 0;

            var stopwatch = Stopwatch.StartNew();

            foreach (var workday in testWorkdays)
            {
                // LINQ 版本邏輯
                var holidayIndex = holidays
                    .Select((date, index) => new { Date = date, Index = index })
                    .FirstOrDefault(h => h.Date.Date == workday.WorkDate.Date)?.Index;

                if (holidayIndex.HasValue)
                {
                    int index = holidayIndex.Value;
                    workday.Comment = param.Holidays[index].Name;
                    workday.WorkHours = 0;
                    SetHolidayDayType(workday);
                    holidaysFound++;
                }
            }

            stopwatch.Stop();
            return (stopwatch.Elapsed, holidaysFound);
        }

        /// <summary>
        /// 測試 Dictionary 版本
        /// </summary>
        private static (TimeSpan elapsed, int holidaysFound) TestDictionaryVersion(
            WorkdayParam param, Dictionary<DateTime, Holiday> holidayLookup, List<Workday> workdays)
        {
            var testWorkdays = workdays.Select(w => CloneWorkday(w)).ToList();
            int holidaysFound = 0;

            var stopwatch = Stopwatch.StartNew();

            foreach (var workday in testWorkdays)
            {
                // Dictionary 版本邏輯
                if (holidayLookup.TryGetValue(workday.WorkDate.Date, out Holiday holiday))
                {
                    workday.Comment = holiday.Name;
                    workday.WorkHours = 0;
                    SetHolidayDayType(workday);
                    holidaysFound++;
                }
            }

            stopwatch.Stop();
            return (stopwatch.Elapsed, holidaysFound);
        }

        /// <summary>
        /// 複製工作日物件
        /// </summary>
        private static Workday CloneWorkday(Workday original)
        {
            return new Workday
            {
                ShiftId = original.ShiftId,
                WorkDate = original.WorkDate,
                WeekDay = original.WeekDay,
                DayType = original.DayType,
                WorkHours = original.WorkHours,
                Comment = original.Comment
            };
        }

        /// <summary>
        /// 設定假日日期類型
        /// </summary>
        private static void SetHolidayDayType(Workday workday)
        {
            switch (workday.WeekDay)
            {
                case 0: workday.DayType = 6; break; // 週日國定假日
                case 1: case 2: case 3: case 4: case 5: workday.DayType = 4; break; // 週間國定假日
                case 6: workday.DayType = 5; break; // 週六國定假日
            }
        }

        /// <summary>
        /// 顯示測試結果
        /// </summary>
        private static void DisplayResults(List<TimeSpan> linqResults, List<TimeSpan> dictResults, int totalWorkdays)
        {
            var linqTimes = linqResults.Select(t => t.TotalMilliseconds).ToArray();
            var dictTimes = dictResults.Select(t => t.TotalMilliseconds).ToArray();

            Console.WriteLine("\n=== 效能測試結果 ===");

            Console.WriteLine($"LINQ 版本 (O(n)):");
            Console.WriteLine($"  平均時間: {linqTimes.Average():F4} ms");
            Console.WriteLine($"  最快時間: {linqTimes.Min():F4} ms");
            Console.WriteLine($"  最慢時間: {linqTimes.Max():F4} ms");
            Console.WriteLine($"  標準差: {CalculateStandardDeviation(linqTimes):F4} ms");
            Console.WriteLine($"  平均每次查找: {(linqTimes.Average() * 1000) / totalWorkdays:F2} μs");

            Console.WriteLine($"\nDictionary 版本 (O(1)):");
            Console.WriteLine($"  平均時間: {dictTimes.Average():F4} ms");
            Console.WriteLine($"  最快時間: {dictTimes.Min():F4} ms");
            Console.WriteLine($"  最慢時間: {dictTimes.Max():F4} ms");
            Console.WriteLine($"  標準差: {CalculateStandardDeviation(dictTimes):F4} ms");
            Console.WriteLine($"  平均每次查找: {(dictTimes.Average() * 1000) / totalWorkdays:F2} μs");

            double improvement = linqTimes.Average() / dictTimes.Average();
            Console.WriteLine($"\n=== 效能提升 ===");
            Console.WriteLine($"Dictionary 版本比 LINQ 版本快 {improvement:F2} 倍");
            Console.WriteLine($"效能提升: {((improvement - 1) * 100):F1}%");
            Console.WriteLine($"時間節省: {(linqTimes.Average() - dictTimes.Average()):F4} ms");
        }

        /// <summary>
        /// 計算標準差
        /// </summary>
        private static double CalculateStandardDeviation(double[] values)
        {
            if (values.Length <= 1) return 0;
            double avg = values.Average();
            double sumOfSquares = values.Sum(x => Math.Pow(x - avg, 2));
            return Math.Sqrt(sumOfSquares / (values.Length - 1));
        }

        /// <summary>
        /// 驗證結果正確性 - 檢查所有欄位
        /// </summary>
        private static void VerifyCorrectness(WorkdayParam param, DateTime[] holidays,
            Dictionary<DateTime, Holiday> holidayLookup, List<Workday> workdays)
        {
            Console.WriteLine("\n=== 正確性驗證 ===");

            var result1 = TestLinqVersion(param, holidays, workdays);
            var result2 = TestDictionaryVersion(param, holidayLookup, workdays);

            Console.WriteLine($"LINQ版本找到假日: {result1.holidaysFound} 個");
            Console.WriteLine($"Dictionary版本找到假日: {result2.holidaysFound} 個");
            Console.WriteLine($"預期假日數量: {holidays.Length} 個");

            // 檢查假日數量是否一致
            if (result1.holidaysFound != result2.holidaysFound)
            {
                Console.WriteLine($"❌ 假日數量不一致：LINQ={result1.holidaysFound}, Dictionary={result2.holidaysFound}");
                return;
            }

            // 詳細檢查每個工作日的所有欄位
            bool isIdentical = true;
            int differences = 0;

            // 重新執行測試以獲得處理後的工作日資料
            var testWorkdays1 = workdays.Select(w => CloneWorkday(w)).ToList();
            var testWorkdays2 = workdays.Select(w => CloneWorkday(w)).ToList();

            // 執行 LINQ 版本處理
            foreach (var workday in testWorkdays1)
            {
                var holidayIndex = holidays
                    .Select((date, index) => new { Date = date, Index = index })
                    .FirstOrDefault(h => h.Date.Date == workday.WorkDate.Date)?.Index;

                if (holidayIndex.HasValue)
                {
                    int index = holidayIndex.Value;
                    workday.Comment = param.Holidays[index].Name;
                    workday.WorkHours = 0;
                    SetHolidayDayType(workday);
                }
            }

            // 執行 Dictionary 版本處理
            foreach (var workday in testWorkdays2)
            {
                if (holidayLookup.TryGetValue(workday.WorkDate.Date, out Holiday holiday))
                {
                    workday.Comment = holiday.Name;
                    workday.WorkHours = 0;
                    SetHolidayDayType(workday);
                }
            }

            for (int i = 0; i < testWorkdays1.Count; i++)
            {
                var w1 = testWorkdays1[i];
                var w2 = testWorkdays2[i];

                // 檢查所有欄位是否完全相同
                bool fieldsMatch =
                    w1.ShiftId == w2.ShiftId &&
                    w1.WorkDate == w2.WorkDate &&
                    w1.WeekDay == w2.WeekDay &&
                    w1.DayType == w2.DayType &&
                    w1.ArrivalTime == w2.ArrivalTime &&
                    w1.DepartureTime == w2.DepartureTime &&
                    w1.FlexibleArrivalBefore == w2.FlexibleArrivalBefore &&
                    w1.FlexibleArrivalAfter == w2.FlexibleArrivalAfter &&
                    w1.FlexibleDepartureBefore == w2.FlexibleDepartureBefore &&
                    w1.FlexibleDepartureAfter == w2.FlexibleDepartureAfter &&
                    w1.MiddayBreakStart == w2.MiddayBreakStart &&
                    w1.MiddayBreakEnd == w2.MiddayBreakEnd &&
                    w1.MorningRestStart == w2.MorningRestStart &&
                    w1.MorningRestEnd == w2.MorningRestEnd &&
                    w1.AfternoonRestStart == w2.AfternoonRestStart &&
                    w1.AfternoonRestEnd == w2.AfternoonRestEnd &&
                    Math.Abs(w1.WorkHours - w2.WorkHours) < 0.001 &&
                    string.Equals(w1.Comment ?? "", w2.Comment ?? "", StringComparison.Ordinal);

                if (!fieldsMatch)
                {
                    isIdentical = false;
                    differences++;

                    if (differences <= 3) // 只顯示前 3 個差異
                    {
                        Console.WriteLine($"\n差異 {differences}: {w1.WorkDate:yyyy-MM-dd}");
                        Console.WriteLine($"  LINQ版本: ShiftId={w1.ShiftId}, DayType={w1.DayType}, WorkHours={w1.WorkHours:F3}, Comment='{w1.Comment ?? ""}'");
                        Console.WriteLine($"  Dict版本: ShiftId={w2.ShiftId}, DayType={w2.DayType}, WorkHours={w2.WorkHours:F3}, Comment='{w2.Comment ?? ""}'");

                        // 顯示具體的欄位差異
                        if (w1.ShiftId != w2.ShiftId) Console.WriteLine($"    ShiftId差異: {w1.ShiftId} ≠ {w2.ShiftId}");
                        if (w1.DayType != w2.DayType) Console.WriteLine($"    DayType差異: {w1.DayType} ≠ {w2.DayType}");
                        if (Math.Abs(w1.WorkHours - w2.WorkHours) >= 0.001) Console.WriteLine($"    WorkHours差異: {w1.WorkHours:F3} ≠ {w2.WorkHours:F3}");
                        if (!string.Equals(w1.Comment ?? "", w2.Comment ?? "", StringComparison.Ordinal)) Console.WriteLine($"    Comment差異: '{w1.Comment ?? ""}' ≠ '{w2.Comment ?? ""}'");
                        if (w1.ArrivalTime != w2.ArrivalTime) Console.WriteLine($"    ArrivalTime差異: {w1.ArrivalTime} ≠ {w2.ArrivalTime}");
                        if (w1.DepartureTime != w2.DepartureTime) Console.WriteLine($"    DepartureTime差異: {w1.DepartureTime} ≠ {w2.DepartureTime}");
                    }
                }
            }

            if (isIdentical)
            {
                Console.WriteLine("✅ 完整驗證通過：兩個版本的所有欄位都完全一致");
                Console.WriteLine($"   總共驗證了 {testWorkdays1.Count} 個工作日的所有欄位");

                if (result1.holidaysFound == holidays.Length)
                {
                    Console.WriteLine("✅ 所有假日都被正確識別和標記");
                }
                else
                {
                    Console.WriteLine($"⚠️  警告：預期 {holidays.Length} 個假日，但只找到 {result1.holidaysFound} 個");
                }
            }
            else
            {
                Console.WriteLine($"❌ 驗證失敗：發現 {differences} 個欄位差異");
                if (differences > 3)
                {
                    Console.WriteLine($"   (只顯示前 3 個差異，實際共有 {differences} 個差異)");
                }
            }
        }
    }
}
