using System;
using System.Data;
using System.Diagnostics.CodeAnalysis;

using Microsoft.Extensions.Configuration;

using WorkdayCalendar;
using WorkdayCalendar.DTO;

using Xunit;

namespace TestWorkdayCalendar
{
    /// <summary>
    /// 
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class ShiftWeekdayControllerTests
    {

        private static ShiftWeekdayDto _shiftWeekdayDTO;
        private IConfiguration _configuration;

        public ShiftWeekdayControllerTests()//ShiftWeekdayDTO shiftWeekdayDTO)
        {
            _configuration = new ConfigurationBuilder().
                    AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).
                    Build();
            _shiftWeekdayDTO = new ShiftWeekdayDto(_configuration.GetSecuredConnectionString("Workday"));
        }

        [Fact]
        public void HolidaysTest()
        {
            DateTime solarDate = WorkdayCalendar.Utility.LunarToSolar(2022, 8, 15);
            Assert.Equal("0910", solarDate.ToString("MMdd"));
            solarDate = WorkdayCalendar.Utility.LunarToSolar(2024, 8, 15);
            Assert.Equal("0917", solarDate.ToString("MMdd"));
            solarDate = WorkdayCalendar.Utility.LunarToSolar(2023, 8, 15);
            Assert.Equal("0929", solarDate.ToString("MMdd"));

            DataTable dtHolidays = _shiftWeekdayDTO.Holidays(2022);
            Assert.NotNull(dtHolidays);
            Assert.Equal(14, dtHolidays.Rows.Count);
            string name = (string)dtHolidays.Rows[12]["Name"];
            Assert.Equal("中秋節", name);
            DateTime date = (DateTime)dtHolidays.Rows[12]["Date"];
            string dateString = date.ToString("MMdd");
            Assert.Equal("0910", dateString);

            dtHolidays = _shiftWeekdayDTO.Holidays(2023);
            name = (string)dtHolidays.Rows[12]["Name"];
            Assert.Equal("中秋節", name);
            date = (DateTime)dtHolidays.Rows[12]["Date"];
            dateString = date.ToString("MMdd");
            Assert.Equal("0929", dateString);
        }

    }
}