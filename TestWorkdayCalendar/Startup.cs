﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using System.Diagnostics.CodeAnalysis;

namespace TestWorkdayCalendar
{
    [ExcludeFromCodeCoverage]
    public class Startup
    {
        private IConfiguration _configuration;
        public void ConfigureHost(IHostBuilder hostBuilder)
        {
            _configuration = new ConfigurationBuilder().
            AddJsonFile("AppSettings.json", optional: true, reloadOnChange: true).
            Build();
        }

    }
}
