using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Linq;

using WorkdayCalendar.Models;

using Xunit;
using Xunit.Abstractions;

namespace TestWorkdayCalendar
{
    /// <summary>
    /// MarkHolidays 方法效能測試類別
    /// 測試兩個版本的 MarkHolidays 方法的執行效率和正確性
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class MarkHolidaysPerformanceTest
    {
        private readonly ITestOutputHelper _output;

        public MarkHolidaysPerformanceTest(ITestOutputHelper output)
        {
            _output = output;
        }

        /// <summary>
        /// 測試兩個版本的 MarkHolidays 方法效能
        /// </summary>
        [Fact]
        public void TestMarkHolidaysPerformance()
        {
            _output.WriteLine("=== MarkHolidays 方法效能測試 ===");

            // 準備測試資料
            var testData = PrepareTestData();
            var param = testData.param;
            var holidays = testData.holidays;
            var holidayLookup = testData.holidayLookup;
            var workdays = testData.workdays;

            _output.WriteLine($"測試資料準備完成:");
            _output.WriteLine($"- 假日數量: {holidays.Length}");
            _output.WriteLine($"- 工作日數量: {workdays.Count}");

            // 執行多次測試以獲得更準確的結果
            const int testRuns = 50;
            _output.WriteLine($"- 測試執行次數: {testRuns} 次");

            var linqResults = new List<(TimeSpan elapsed, int holidaysFound)>();
            var dictResults = new List<(TimeSpan elapsed, int holidaysFound)>();

            // JIT 預熱
            TestLinqVersion(param, holidays, workdays.Take(10).ToList());
            TestDictionaryVersion(param, holidayLookup, workdays.Take(10).ToList());

            // 多次測試 LINQ 版本
            for (int i = 0; i < testRuns; i++)
            {
                var result = TestLinqVersion(param, holidays, workdays);
                linqResults.Add((result.elapsed, result.holidaysFound));
            }

            // 多次測試 Dictionary 版本
            for (int i = 0; i < testRuns; i++)
            {
                var result = TestDictionaryVersion(param, holidayLookup, workdays);
                dictResults.Add((result.elapsed, result.holidaysFound));
            }

            // 顯示統計結果
            DisplayStatisticalResults(linqResults, dictResults, workdays.Count);

            // 驗證結果正確性
            VerifyResults(param, holidays, holidayLookup, workdays);

            // 斷言效能提升
            var linqAvg = linqResults.Select(r => r.elapsed.TotalMilliseconds).Average();
            var dictAvg = dictResults.Select(r => r.elapsed.TotalMilliseconds).Average();
            var improvement = linqAvg / dictAvg;

            Assert.True(improvement > 1.0, $"Dictionary 版本應該比 LINQ 版本更快，但實際提升倍數為 {improvement:F2}");
            Assert.True(linqResults.All(r => r.holidaysFound == dictResults[0].holidaysFound), "兩個版本應該找到相同數量的假日");
        }

        /// <summary>
        /// 準備測試資料
        /// </summary>
        private (WorkdayParam param, DateTime[] holidays, Dictionary<DateTime, Holiday> holidayLookup, List<Workday> workdays) PrepareTestData()
        {
            // 建立測試用的假日資料 (模擬台灣 2024 年國定假日)
            var holidayData = new[]
            {
                new Holiday { Date = "2024-01-01", Name = "元旦", IsLunar = false },
                new Holiday { Date = "2024-02-08", Name = "農曆除夕", IsLunar = true },
                new Holiday { Date = "2024-02-09", Name = "農曆新年初一", IsLunar = true },
                new Holiday { Date = "2024-02-10", Name = "農曆新年初二", IsLunar = true },
                new Holiday { Date = "2024-02-11", Name = "農曆新年初三", IsLunar = true },
                new Holiday { Date = "2024-02-12", Name = "農曆新年初四", IsLunar = true },
                new Holiday { Date = "2024-02-28", Name = "和平紀念日", IsLunar = false },
                new Holiday { Date = "2024-04-04", Name = "兒童節", IsLunar = false },
                new Holiday { Date = "2024-04-05", Name = "清明節", IsLunar = false },
                new Holiday { Date = "2024-05-01", Name = "勞動節", IsLunar = false },
                new Holiday { Date = "2024-06-10", Name = "端午節", IsLunar = true },
                new Holiday { Date = "2024-09-17", Name = "中秋節", IsLunar = true },
                new Holiday { Date = "2024-10-10", Name = "國慶日", IsLunar = false },
            };

            // 建立 WorkdayParam
            var param = new WorkdayParam
            {
                Year = 2024,
                ShiftId = 1,
                StartDate = new DateTime(2024, 1, 1),
                Holidays = holidayData
            };

            // 轉換假日為 DateTime 陣列
            var holidays = holidayData.Select(h => DateTime.Parse(h.Date)).ToArray();

            // 建立假日查找字典
            var holidayLookup = new Dictionary<DateTime, Holiday>();
            for (int i = 0; i < holidays.Length; i++)
            {
                holidayLookup[holidays[i].Date] = holidayData[i];
            }

            // 建立測試用的工作日資料 (整年 366 天，2024是閏年)
            var workdays = new List<Workday>();
            var startDate = new DateTime(2024, 1, 1);
            var endDate = new DateTime(2024, 12, 31);

            for (var date = startDate; date <= endDate; date = date.AddDays(1))
            {
                workdays.Add(new Workday
                {
                    ShiftId = 1,
                    WorkDate = date,
                    WeekDay = (byte)date.DayOfWeek,
                    DayType = 1, // 預設為工作日
                    WorkHours = 8.0, // 預設工作 8 小時
                    Comment = ""
                });
            }

            return (param, holidays, holidayLookup, workdays);
        }

        /// <summary>
        /// 測試 LINQ 版本的 MarkHolidays 方法
        /// </summary>
        private (TimeSpan elapsed, List<Workday> processedWorkdays, int holidaysFound) TestLinqVersion(
            WorkdayParam param, DateTime[] holidays, List<Workday> workdays)
        {
            var processedWorkdays = workdays.Select(w => CloneWorkday(w)).ToList();
            int holidaysFound = 0;

            var stopwatch = Stopwatch.StartNew();

            foreach (var workday in processedWorkdays)
            {
                // 模擬原本的 LINQ 版本邏輯
                var holidayIndex = holidays
                    .Select((date, index) => new { Date = date, Index = index })
                    .FirstOrDefault(h => h.Date.Date == workday.WorkDate.Date)?.Index;

                if (holidayIndex.HasValue)
                {
                    int index = holidayIndex.Value;
                    workday.Comment = param.Holidays[index].Name;
                    workday.WorkHours = 0;
                    SetHolidayDayType(workday);
                    holidaysFound++;
                }
            }

            stopwatch.Stop();
            return (stopwatch.Elapsed, processedWorkdays, holidaysFound);
        }

        /// <summary>
        /// 測試 Dictionary 版本的 MarkHolidays 方法
        /// </summary>
        private (TimeSpan elapsed, List<Workday> processedWorkdays, int holidaysFound) TestDictionaryVersion(
            WorkdayParam param, Dictionary<DateTime, Holiday> holidayLookup, List<Workday> workdays)
        {
            var processedWorkdays = workdays.Select(w => CloneWorkday(w)).ToList();
            int holidaysFound = 0;

            var stopwatch = Stopwatch.StartNew();

            foreach (var workday in processedWorkdays)
            {
                // 模擬 Dictionary 版本邏輯
                if (holidayLookup.TryGetValue(workday.WorkDate.Date, out Holiday holiday))
                {
                    workday.Comment = holiday.Name;
                    workday.WorkHours = 0;
                    SetHolidayDayType(workday);
                    holidaysFound++;
                }
            }

            stopwatch.Stop();
            return (stopwatch.Elapsed, processedWorkdays, holidaysFound);
        }

        /// <summary>
        /// 複製 Workday 物件
        /// </summary>
        private static Workday CloneWorkday(Workday original)
        {
            return new Workday
            {
                ShiftId = original.ShiftId,
                WorkDate = original.WorkDate,
                WeekDay = original.WeekDay,
                DayType = original.DayType,
                WorkHours = original.WorkHours,
                Comment = original.Comment,
                ArrivalTime = original.ArrivalTime,
                DepartureTime = original.DepartureTime,
                FlexibleArrivalBefore = original.FlexibleArrivalBefore,
                FlexibleArrivalAfter = original.FlexibleArrivalAfter,
                FlexibleDepartureBefore = original.FlexibleDepartureBefore,
                FlexibleDepartureAfter = original.FlexibleDepartureAfter,
                MiddayBreakStart = original.MiddayBreakStart,
                MiddayBreakEnd = original.MiddayBreakEnd,
                MorningRestStart = original.MorningRestStart,
                MorningRestEnd = original.MorningRestEnd,
                AfternoonRestStart = original.AfternoonRestStart,
                AfternoonRestEnd = original.AfternoonRestEnd
            };
        }

        /// <summary>
        /// 根據星期幾設定國定假日的日期類型
        /// </summary>
        private static void SetHolidayDayType(Workday workday)
        {
            switch (workday.WeekDay)
            {
                case 0: // 週日國定假日
                    workday.DayType = 6;
                    break;
                case 1: // 週一到週五的國定假日
                case 2:
                case 3:
                case 4:
                case 5:
                    workday.DayType = 4; // 週間國定假日
                    break;
                case 6: // 週六國定假日
                    workday.DayType = 5;
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// 顯示統計測試結果
        /// </summary>
        private void DisplayStatisticalResults(
            List<(TimeSpan elapsed, int holidaysFound)> linqResults,
            List<(TimeSpan elapsed, int holidaysFound)> dictResults,
            int totalWorkdays)
        {
            _output.WriteLine("\n=== 統計測試結果 ===");

            // 計算 LINQ 版本統計數據
            var linqTimes = linqResults.Select(r => r.elapsed.TotalMilliseconds).ToArray();
            var linqAvg = linqTimes.Average();
            var linqMin = linqTimes.Min();
            var linqMax = linqTimes.Max();
            var linqStdDev = CalculateStandardDeviation(linqTimes);

            _output.WriteLine($"LINQ 版本 (O(n)) - {linqResults.Count} 次測試:");
            _output.WriteLine($"  平均執行時間: {linqAvg:F4} ms");
            _output.WriteLine($"  最快時間: {linqMin:F4} ms");
            _output.WriteLine($"  最慢時間: {linqMax:F4} ms");
            _output.WriteLine($"  標準差: {linqStdDev:F4} ms");
            _output.WriteLine($"  平均每次查找: {(linqAvg * 1000) / totalWorkdays:F2} μs");
            _output.WriteLine($"  找到假日: {linqResults[0].holidaysFound} 個");

            // 計算 Dictionary 版本統計數據
            var dictTimes = dictResults.Select(r => r.elapsed.TotalMilliseconds).ToArray();
            var dictAvg = dictTimes.Average();
            var dictMin = dictTimes.Min();
            var dictMax = dictTimes.Max();
            var dictStdDev = CalculateStandardDeviation(dictTimes);

            _output.WriteLine($"\nDictionary 版本 (O(1)) - {dictResults.Count} 次測試:");
            _output.WriteLine($"  平均執行時間: {dictAvg:F4} ms");
            _output.WriteLine($"  最快時間: {dictMin:F4} ms");
            _output.WriteLine($"  最慢時間: {dictMax:F4} ms");
            _output.WriteLine($"  標準差: {dictStdDev:F4} ms");
            _output.WriteLine($"  平均每次查找: {(dictAvg * 1000) / totalWorkdays:F2} μs");
            _output.WriteLine($"  找到假日: {dictResults[0].holidaysFound} 個");

            // 計算效能提升
            double improvement = linqAvg / dictAvg;
            _output.WriteLine($"\n=== 效能比較 ===");
            _output.WriteLine($"Dictionary 版本比 LINQ 版本快 {improvement:F2} 倍");
            _output.WriteLine($"平均時間節省: {(linqAvg - dictAvg):F4} ms");
            _output.WriteLine($"效能提升百分比: {((improvement - 1) * 100):F1}%");

            // 顯示效能提升的穩定性
            var improvementRatios = linqTimes.Zip(dictTimes, (linq, dict) => linq / dict).ToArray();
            var improvementAvg = improvementRatios.Average();
            var improvementStdDev = CalculateStandardDeviation(improvementRatios);

            _output.WriteLine($"\n效能提升穩定性:");
            _output.WriteLine($"  平均提升倍數: {improvementAvg:F2}");
            _output.WriteLine($"  提升倍數標準差: {improvementStdDev:F4}");
            _output.WriteLine($"  最小提升倍數: {improvementRatios.Min():F2}");
            _output.WriteLine($"  最大提升倍數: {improvementRatios.Max():F2}");
        }

        /// <summary>
        /// 計算標準差
        /// </summary>
        private static double CalculateStandardDeviation(double[] values)
        {
            if (values.Length <= 1) return 0;

            double avg = values.Average();
            double sumOfSquares = values.Sum(x => Math.Pow(x - avg, 2));
            return Math.Sqrt(sumOfSquares / (values.Length - 1));
        }

        /// <summary>
        /// 驗證兩個版本的結果是否一致
        /// </summary>
        private void VerifyResults(WorkdayParam param, DateTime[] holidays,
            Dictionary<DateTime, Holiday> holidayLookup, List<Workday> workdays)
        {
            _output.WriteLine("\n=== 結果驗證 ===");

            var result1 = TestLinqVersion(param, holidays, workdays);
            var result2 = TestDictionaryVersion(param, holidayLookup, workdays);

            bool isIdentical = true;
            int differences = 0;

            for (int i = 0; i < result1.processedWorkdays.Count; i++)
            {
                var w1 = result1.processedWorkdays[i];
                var w2 = result2.processedWorkdays[i];

                // 檢查所有欄位是否完全相同
                bool fieldsMatch =
                    w1.ShiftId == w2.ShiftId &&
                    w1.WorkDate == w2.WorkDate &&
                    w1.WeekDay == w2.WeekDay &&
                    w1.DayType == w2.DayType &&
                    w1.ArrivalTime == w2.ArrivalTime &&
                    w1.DepartureTime == w2.DepartureTime &&
                    w1.FlexibleArrivalBefore == w2.FlexibleArrivalBefore &&
                    w1.FlexibleArrivalAfter == w2.FlexibleArrivalAfter &&
                    w1.FlexibleDepartureBefore == w2.FlexibleDepartureBefore &&
                    w1.FlexibleDepartureAfter == w2.FlexibleDepartureAfter &&
                    w1.MiddayBreakStart == w2.MiddayBreakStart &&
                    w1.MiddayBreakEnd == w2.MiddayBreakEnd &&
                    w1.MorningRestStart == w2.MorningRestStart &&
                    w1.MorningRestEnd == w2.MorningRestEnd &&
                    w1.AfternoonRestStart == w2.AfternoonRestStart &&
                    w1.AfternoonRestEnd == w2.AfternoonRestEnd &&
                    Math.Abs(w1.WorkHours - w2.WorkHours) < 0.001 &&
                    string.Equals(w1.Comment ?? "", w2.Comment ?? "", StringComparison.Ordinal);

                if (!fieldsMatch)
                {
                    isIdentical = false;
                    differences++;

                    if (differences <= 5) // 顯示前 5 個差異
                    {
                        _output.WriteLine($"差異 {differences}: {w1.WorkDate:yyyy-MM-dd}");
                        _output.WriteLine($"  LINQ版本:");
                        _output.WriteLine($"    ShiftId={w1.ShiftId}, WeekDay={w1.WeekDay}, DayType={w1.DayType}");
                        _output.WriteLine($"    WorkHours={w1.WorkHours:F3}, Comment='{w1.Comment ?? ""}'");
                        _output.WriteLine($"    ArrivalTime={w1.ArrivalTime:HH:mm:ss}, DepartureTime={w1.DepartureTime:HH:mm:ss}");
                        _output.WriteLine($"    FlexibleArrival: {w1.FlexibleArrivalBefore:HH:mm:ss} ~ {w1.FlexibleArrivalAfter:HH:mm:ss}");
                        _output.WriteLine($"    FlexibleDeparture: {w1.FlexibleDepartureBefore:HH:mm:ss} ~ {w1.FlexibleDepartureAfter:HH:mm:ss}");
                        _output.WriteLine($"    MiddayBreak: {w1.MiddayBreakStart:HH:mm:ss} ~ {w1.MiddayBreakEnd:HH:mm:ss}");
                        _output.WriteLine($"    MorningRest: {w1.MorningRestStart:HH:mm:ss} ~ {w1.MorningRestEnd:HH:mm:ss}");
                        _output.WriteLine($"    AfternoonRest: {w1.AfternoonRestStart:HH:mm:ss} ~ {w1.AfternoonRestEnd:HH:mm:ss}");

                        _output.WriteLine($"  Dict版本:");
                        _output.WriteLine($"    ShiftId={w2.ShiftId}, WeekDay={w2.WeekDay}, DayType={w2.DayType}");
                        _output.WriteLine($"    WorkHours={w2.WorkHours:F3}, Comment='{w2.Comment ?? ""}'");
                        _output.WriteLine($"    ArrivalTime={w2.ArrivalTime:HH:mm:ss}, DepartureTime={w2.DepartureTime:HH:mm:ss}");
                        _output.WriteLine($"    FlexibleArrival: {w2.FlexibleArrivalBefore:HH:mm:ss} ~ {w2.FlexibleArrivalAfter:HH:mm:ss}");
                        _output.WriteLine($"    FlexibleDeparture: {w2.FlexibleDepartureBefore:HH:mm:ss} ~ {w2.FlexibleDepartureAfter:HH:mm:ss}");
                        _output.WriteLine($"    MiddayBreak: {w2.MiddayBreakStart:HH:mm:ss} ~ {w2.MiddayBreakEnd:HH:mm:ss}");
                        _output.WriteLine($"    MorningRest: {w2.MorningRestStart:HH:mm:ss} ~ {w2.MorningRestEnd:HH:mm:ss}");
                        _output.WriteLine($"    AfternoonRest: {w2.AfternoonRestStart:HH:mm:ss} ~ {w2.AfternoonRestEnd:HH:mm:ss}");

                        // 詳細比較每個欄位
                        _output.WriteLine($"  欄位差異詳細:");
                        if (w1.ShiftId != w2.ShiftId) _output.WriteLine($"    ShiftId: {w1.ShiftId} ≠ {w2.ShiftId}");
                        if (w1.WorkDate != w2.WorkDate) _output.WriteLine($"    WorkDate: {w1.WorkDate} ≠ {w2.WorkDate}");
                        if (w1.WeekDay != w2.WeekDay) _output.WriteLine($"    WeekDay: {w1.WeekDay} ≠ {w2.WeekDay}");
                        if (w1.DayType != w2.DayType) _output.WriteLine($"    DayType: {w1.DayType} ≠ {w2.DayType}");
                        if (Math.Abs(w1.WorkHours - w2.WorkHours) >= 0.001) _output.WriteLine($"    WorkHours: {w1.WorkHours:F3} ≠ {w2.WorkHours:F3}");
                        if (!string.Equals(w1.Comment ?? "", w2.Comment ?? "", StringComparison.Ordinal)) _output.WriteLine($"    Comment: '{w1.Comment ?? ""}' ≠ '{w2.Comment ?? ""}'");
                        if (w1.ArrivalTime != w2.ArrivalTime) _output.WriteLine($"    ArrivalTime: {w1.ArrivalTime} ≠ {w2.ArrivalTime}");
                        if (w1.DepartureTime != w2.DepartureTime) _output.WriteLine($"    DepartureTime: {w1.DepartureTime} ≠ {w2.DepartureTime}");
                        if (w1.FlexibleArrivalBefore != w2.FlexibleArrivalBefore) _output.WriteLine($"    FlexibleArrivalBefore: {w1.FlexibleArrivalBefore} ≠ {w2.FlexibleArrivalBefore}");
                        if (w1.FlexibleArrivalAfter != w2.FlexibleArrivalAfter) _output.WriteLine($"    FlexibleArrivalAfter: {w1.FlexibleArrivalAfter} ≠ {w2.FlexibleArrivalAfter}");
                        if (w1.FlexibleDepartureBefore != w2.FlexibleDepartureBefore) _output.WriteLine($"    FlexibleDepartureBefore: {w1.FlexibleDepartureBefore} ≠ {w2.FlexibleDepartureBefore}");
                        if (w1.FlexibleDepartureAfter != w2.FlexibleDepartureAfter) _output.WriteLine($"    FlexibleDepartureAfter: {w1.FlexibleDepartureAfter} ≠ {w2.FlexibleDepartureAfter}");
                        if (w1.MiddayBreakStart != w2.MiddayBreakStart) _output.WriteLine($"    MiddayBreakStart: {w1.MiddayBreakStart} ≠ {w2.MiddayBreakStart}");
                        if (w1.MiddayBreakEnd != w2.MiddayBreakEnd) _output.WriteLine($"    MiddayBreakEnd: {w1.MiddayBreakEnd} ≠ {w2.MiddayBreakEnd}");
                        if (w1.MorningRestStart != w2.MorningRestStart) _output.WriteLine($"    MorningRestStart: {w1.MorningRestStart} ≠ {w2.MorningRestStart}");
                        if (w1.MorningRestEnd != w2.MorningRestEnd) _output.WriteLine($"    MorningRestEnd: {w1.MorningRestEnd} ≠ {w2.MorningRestEnd}");
                        if (w1.AfternoonRestStart != w2.AfternoonRestStart) _output.WriteLine($"    AfternoonRestStart: {w1.AfternoonRestStart} ≠ {w2.AfternoonRestStart}");
                        if (w1.AfternoonRestEnd != w2.AfternoonRestEnd) _output.WriteLine($"    AfternoonRestEnd: {w1.AfternoonRestEnd} ≠ {w2.AfternoonRestEnd}");

                        _output.WriteLine(""); // 空行分隔
                    }
                }
            }

            if (isIdentical)
            {
                _output.WriteLine("✅ 驗證通過：兩個版本的結果完全一致");
            }
            else
            {
                _output.WriteLine($"❌ 驗證失敗：發現 {differences} 個差異");
            }

            Assert.True(isIdentical, $"兩個版本的結果應該完全一致，但發現 {differences} 個差異");
        }

        /// <summary>
        /// 測試正確性 - 確保假日被正確標記
        /// </summary>
        [Fact]
        public void TestMarkHolidaysCorrectness()
        {
            var testData = PrepareTestData();
            var param = testData.param;
            var holidays = testData.holidays;
            var holidayLookup = testData.holidayLookup;
            var workdays = testData.workdays;

            var linqResult = TestLinqVersion(param, holidays, workdays);
            var dictResult = TestDictionaryVersion(param, holidayLookup, workdays);

            // 驗證找到的假日數量
            Assert.Equal(linqResult.holidaysFound, dictResult.holidaysFound);
            Assert.Equal(holidays.Length, linqResult.holidaysFound);

            // 驗證特定假日是否被正確標記
            var newYearWorkday = linqResult.processedWorkdays.First(w => w.WorkDate.Date == new DateTime(2024, 1, 1));
            Assert.Equal("元旦", newYearWorkday.Comment);
            Assert.Equal(0, newYearWorkday.WorkHours);
            Assert.Equal(4, newYearWorkday.DayType); // 週一國定假日

            var nationalDayWorkday = linqResult.processedWorkdays.First(w => w.WorkDate.Date == new DateTime(2024, 10, 10));
            Assert.Equal("國慶日", nationalDayWorkday.Comment);
            Assert.Equal(0, nationalDayWorkday.WorkHours);
            Assert.Equal(4, nationalDayWorkday.DayType); // 週四國定假日

            _output.WriteLine($"✅ 正確性測試通過：成功標記 {linqResult.holidaysFound} 個假日");
        }
    }
}
