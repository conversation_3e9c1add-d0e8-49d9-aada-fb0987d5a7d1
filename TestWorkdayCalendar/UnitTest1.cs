using System;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using WorkdayCalendar;
using Xunit;

namespace TestWorkdayCalendar
{
    [ExcludeFromCodeCoverage]
    public class UnitTest1
    {
        //private IConfiguration _configuration;

        public UnitTest1()
        {

        }

        /// <summary>
        /// 測試資料庫連線和基本查詢功能
        /// </summary>
        [Fact]
        public void GetDataTableTest()
        {
            string connectionString = "server=attendancedev.sinotech.org.tw;uid=sa;password=********;database=Workday;TrustServerCertificate=true;pooling=true;Application Name=WorkdayCalendar;";
            string strSql = "SELECT * FROM dbo.WorkShift;";
            DataTable dt = SqlUtil.GetDataTable(connectionString, strSql);
            Assert.NotNull(dt);
            Assert.True(dt.Rows.Count > 0);

            //SqlParameter parameter = new SqlParameter();
        }

        /// <summary>
        /// 執行 MarkHolidays 效能測試的控制台版本
        /// 這個測試會在輸出中顯示詳細的效能比較結果
        /// </summary>
        [Fact]
        public void RunMarkHolidaysConsoleTest()
        {
            // 重定向 Console 輸出到測試輸出
            var originalOut = Console.Out;
            try
            {
                using var stringWriter = new System.IO.StringWriter();
                Console.SetOut(stringWriter);

                // 執行控制台測試
                MarkHolidaysConsoleTest.RunConsoleTest();

                // 獲取輸出並顯示在測試結果中
                var output = stringWriter.ToString();

                // 恢復原始輸出
                Console.SetOut(originalOut);

                // 將結果輸出到測試日誌
                Console.WriteLine("=== MarkHolidays 效能測試結果 ===");
                Console.WriteLine(output);

                // 基本斷言確保測試執行成功
                Assert.Contains("效能測試結果", output);
                Assert.Contains("Dictionary 版本比 LINQ 版本快", output);
            }
            finally
            {
                Console.SetOut(originalOut);
            }
        }
    }
}
