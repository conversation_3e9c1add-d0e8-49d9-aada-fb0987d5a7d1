# MarkHolidays 效能測試說明

本測試專案包含了對 `MarkHolidays` 方法兩個版本的效能測試和正確性驗證。

## 測試檔案說明

### 1. MarkHolidaysPerformanceTest.cs

- **用途**: xUnit 單元測試，包含詳細的效能分析和正確性驗證
- **測試方法**:
  - `TestMarkHolidaysPerformance()`: 主要效能測試，執行多次測試並提供統計分析
  - `TestMarkHolidaysCorrectness()`: 正確性測試，驗證兩個版本的結果是否一致

### 2. MarkHolidaysConsoleTest.cs

- **用途**: 控制台版本的效能測試，提供更直觀的輸出結果
- **主要方法**: `RunConsoleTest()` - 執行完整的效能測試並顯示結果

### 3. UnitTest1.cs (已修改)

- 新增了 `RunMarkHolidaysConsoleTest()` 方法，可以在 xUnit 測試中執行控制台版本的測試

## 測試的兩個版本

### 版本一：LINQ 版本 (O(n))

```csharp
var holidayIndex = holidays
    .Select((date, index) => new { Date = date, Index = index })
    .FirstOrDefault(h => h.Date.Date == workday.WorkDate.Date)?.Index;
```

- **時間複雜度**: O(n) - 需要遍歷假日陣列
- **優點**: 程式碼簡潔易讀
- **缺點**: 對於每個工作日都需要線性搜尋假日陣列

### 版本二：Dictionary 版本 (O(1))

```csharp
if (holidayLookup.TryGetValue(workday.WorkDate.Date, out Holiday holiday))
{
    // 處理假日邏輯
}
```

- **時間複雜度**: O(1) - 字典查找
- **優點**: 查找效率高，特別適合大量工作日處理
- **缺點**: 需要額外的記憶體建立字典

## 完整欄位驗證

測試會檢查 `Workday` 物件的所有欄位是否完全一致：

### 基本欄位

- `ShiftId` - 班別編號
- `WorkDate` - 工作日期
- `WeekDay` - 星期幾
- `DayType` - 日期類型
- `WorkHours` - 工作時數
- `Comment` - 備註（假日名稱）

### 時間欄位

- `ArrivalTime` - 到班時間
- `DepartureTime` - 下班時間
- `FlexibleArrivalBefore` - 彈性到班時間（之前）
- `FlexibleArrivalAfter` - 彈性到班時間（之後）
- `FlexibleDepartureBefore` - 彈性下班時間（之前）
- `FlexibleDepartureAfter` - 彈性下班時間（之後）
- `MiddayBreakStart` - 午休開始時間
- `MiddayBreakEnd` - 午休結束時間
- `MorningRestStart` - 上午休息開始時間
- `MorningRestEnd` - 上午休息結束時間
- `AfternoonRestStart` - 下午休息開始時間
- `AfternoonRestEnd` - 下午休息結束時間

**驗證標準**: 所有欄位必須完全相同，包括時間精確度和字串比較（區分大小寫）

## 如何運行測試

### 方法一：使用 Visual Studio Test Explorer

1. 在 Visual Studio 中開啟 Test Explorer
2. 建置 TestWorkdayCalendar 專案
3. 運行以下測試：
   - `TestMarkHolidaysPerformance` - 詳細效能分析
   - `TestMarkHolidaysCorrectness` - 正確性驗證
   - `RunMarkHolidaysConsoleTest` - 控制台版本測試

### 方法二：使用命令列

```bash
cd TestWorkdayCalendar
dotnet test --logger "console;verbosity=detailed"
```

### 方法三：運行特定測試

```bash
# 只運行效能測試
dotnet test --filter "TestMarkHolidaysPerformance"

# 只運行正確性測試
dotnet test --filter "TestMarkHolidaysCorrectness"

# 運行控制台版本測試
dotnet test --filter "RunMarkHolidaysConsoleTest"
```

## 測試資料

測試使用 2024 年台灣國定假日作為測試資料：

- 元旦 (2024-01-01)
- 農曆新年 (2024-02-08 ~ 2024-02-12)
- 和平紀念日 (2024-02-28)
- 兒童節 (2024-04-04)
- 清明節 (2024-04-05)
- 勞動節 (2024-05-01)
- 端午節 (2024-06-10)
- 中秋節 (2024-09-17)
- 國慶日 (2024-10-10)

總共 13 個假日，測試對象為整年 366 天的工作日資料（2024 年是閏年）。

## 預期測試結果

根據理論分析，Dictionary 版本應該比 LINQ 版本有顯著的效能提升：

- **LINQ 版本**: 每次查找需要 O(n) 時間，總體複雜度 O(366 × 13)
- **Dictionary 版本**: 每次查找需要 O(1) 時間，總體複雜度 O(366 + 13)

預期 Dictionary 版本會比 LINQ 版本快 2-5 倍，具體提升幅度取決於硬體效能。

## 測試輸出說明

### 效能測試結果

- 平均執行時間
- 最快/最慢執行時間
- 標準差（穩定性指標）
- 平均每次查找時間
- 效能提升倍數和百分比

### 正確性驗證結果

- 假日數量比較
- 完整欄位比較（所有 Workday 屬性）
- 詳細差異報告（如果有差異）
- 具體欄位差異列表

### 驗證失敗時的詳細輸出

如果發現差異，測試會顯示：

- 差異的工作日日期
- 兩個版本的完整欄位值
- 具體哪些欄位不同
- 差異的詳細比較（例如：`WorkHours: 8.000 ≠ 0.000`）

## 注意事項

1. 測試結果可能會因為 JIT 編譯、垃圾回收等因素而有所變動
2. 建議多次運行測試以獲得更穩定的結果
3. 測試包含 JIT 預熱步驟以減少初次執行的影響
4. 所有測試都包含正確性驗證，確保兩個版本產生相同的結果
