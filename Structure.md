# 中興社工作日曆專案結構

## 專案概述

中興工程顧問社內部專用的工作日曆系統，基於 .NET Core 8.0 C# 與 Vue 3.2 開發，採用前後端分離架構。

## 專案目錄架構說明

### 根目錄

- **WorkdayCalendar/**: 主要後端專案 (.NET Core Web API)
- **TestWorkdayCalendar/**: 單元測試專案 (xUnit)
- **WorkdayCalendar.sln**: Visual Studio 解決方案檔案

### 後端架構 (WorkdayCalendar/)

- **App_Code/**: 核心業務邏輯與資料存取層
- **Controllers/**: Web API 控制器
- **DTO/**: 資料傳輸物件
- **Models/**: 資料模型
- **ClientApp/**: Vue.js 前端應用程式

### 前端架構 (ClientApp/)

- **src/components/**: Vue 元件
- **src/pages/**: 頁面元件
- **src/router/**: 路由設定
- **src/store/**: 狀態管理
- **src/assets/**: 靜態資源

## 完整檔案結構

WorkCalendar/
├── [.gitignore](.gitignore)
├── [WorkdayCalendar.sln](WorkdayCalendar.sln)
├── WorkdayCalendar/
│   ├── **核心設定檔**
│   ├── [Program.cs](WorkdayCalendar/Program.cs)
│   ├── [Startup.cs](WorkdayCalendar/Startup.cs)
│   ├── [WorkdayCalendar.csproj](WorkdayCalendar/WorkdayCalendar.csproj)
│   ├── [appsettings.json](WorkdayCalendar/appsettings.json)
│   ├── [appsettings.Development.json](WorkdayCalendar/appsettings.Development.json)
│   ├── [tsconfig.json](WorkdayCalendar/tsconfig.json)
│   ├── [package.json](WorkdayCalendar/package.json)
│   ├── [vetur.config.js](WorkdayCalendar/vetur.config.js)
│   ├── [web.config](WorkdayCalendar/web.config)
│   │
│   ├── **業務邏輯層 (App_Code/)**
│   ├── App_Code/
│   │   ├── [CNDate.cs](WorkdayCalendar/App_Code/CNDate.cs)
│   │   ├── [ChinaDate.cs](WorkdayCalendar/App_Code/ChinaDate.cs)
│   │   ├── [Employee.cs](WorkdayCalendar/App_Code/Employee.cs)
│   │   ├── [EmployeeDAO.cs](WorkdayCalendar/App_Code/EmployeeDAO.cs)
│   │   ├── [IEmployee.cs](WorkdayCalendar/App_Code/IEmployee.cs)
│   │   ├── [JobDAO.cs](WorkdayCalendar/App_Code/JobDAO.cs)
│   │   ├── [SingletonUserName.cs](WorkdayCalendar/App_Code/SingletonUserName.cs)
│   │   ├── [SinotechEmployee.cs](WorkdayCalendar/App_Code/SinotechEmployee.cs)
│   │   ├── [SqlUtil.cs](WorkdayCalendar/App_Code/SqlUtil.cs)
│   │   ├── [Utility.cs](WorkdayCalendar/App_Code/Utility.cs)
│   │   ├── [ViteHelper.cs](WorkdayCalendar/App_Code/ViteHelper.cs)
│   │   └── [WaitedTask.cs](WorkdayCalendar/App_Code/WaitedTask.cs)
│   │
│   ├── **API 控制器 (Controllers/)**
│   ├── Controllers/
│   │   ├── [LogonUserController.cs](WorkdayCalendar/Controllers/LogonUserController.cs)
│   │   ├── [ShiftWeekdayController.cs](WorkdayCalendar/Controllers/ShiftWeekdayController.cs)
│   │   ├── [UserController.cs](WorkdayCalendar/Controllers/UserController.cs)
│   │   ├── [WorkShiftController.cs](WorkdayCalendar/Controllers/WorkShiftController.cs)
│   │   └── [WorkdayController.cs](WorkdayCalendar/Controllers/WorkdayController.cs)
│   │
│   ├── **資料傳輸物件 (DTO/)**
│   ├── DTO/
│   │   ├── [ShiftWeekdayDTO.cs](WorkdayCalendar/DTO/ShiftWeekdayDTO.cs)
│   │   ├── [WorkShiftDTO.cs](WorkdayCalendar/DTO/WorkShiftDTO.cs)
│   │   └── [WorkdayDTO.cs](WorkdayCalendar/DTO/WorkdayDTO.cs)
│   │
│   ├── **資料模型 (Models/)**
│   ├── Models/
│   │   ├── [Holiday.cs](WorkdayCalendar/Models/Holiday.cs)
│   │   ├── [ShiftWeekday.cs](WorkdayCalendar/Models/ShiftWeekday.cs)
│   │   ├── [WorkShift.cs](WorkdayCalendar/Models/WorkShift.cs)
│   │   └── [Workday.cs](WorkdayCalendar/Models/Workday.cs)
│   │
│   ├── **專案設定**
│   ├── Properties/
│   │   └── [launchSettings.json](WorkdayCalendar/Properties/launchSettings.json)
│   │
│   └── **前端應用程式 (ClientApp/)**
│   └── ClientApp/
│       ├── **前端設定檔**
│       ├── [package.json](WorkdayCalendar/ClientApp/package.json)
│       ├── [package-lock.json](WorkdayCalendar/ClientApp/package-lock.json)
│       ├── [vite.config.ts](WorkdayCalendar/ClientApp/vite.config.ts)
│       ├── [vue.config.js](WorkdayCalendar/ClientApp/vue.config.js)
│       ├── [tsconfig.json](WorkdayCalendar/ClientApp/tsconfig.json)
│       ├── [serverOption.ts](WorkdayCalendar/ClientApp/serverOption.ts)
│       ├── [.eslintrc.js](WorkdayCalendar/ClientApp/.eslintrc.js)
│       ├── [.prettierrc.js](WorkdayCalendar/ClientApp/.prettierrc.js)
│       ├── [babel.config.js](WorkdayCalendar/ClientApp/babel.config.js)
│       ├── [index.html](WorkdayCalendar/ClientApp/index.html)
│       ├── [README.md](WorkdayCalendar/ClientApp/README.md)
│       ├── [LICENSE](WorkdayCalendar/ClientApp/LICENSE)
│       ├── [.gitignore](WorkdayCalendar/ClientApp/.gitignore)
│       │
│       └── src/
│           ├── **主要應用程式檔案**
│           ├── [main.ts](WorkdayCalendar/ClientApp/src/main.ts)
│           ├── [App.vue](WorkdayCalendar/ClientApp/src/App.vue)
│           ├── [shims-vue.d.ts](WorkdayCalendar/ClientApp/src/shims-vue.d.ts)
│           │
│           ├── **Vue 元件 (components/)**
│           ├── components/
│           │   ├── [api.ts](WorkdayCalendar/ClientApp/src/components/api.ts)
│           │   ├── [Employee.ts](WorkdayCalendar/ClientApp/src/components/Employee.ts)
│           │   ├── [ChooseShift.vue](WorkdayCalendar/ClientApp/src/components/ChooseShift.vue)
│           │   ├── [ChooseUser.vue](WorkdayCalendar/ClientApp/src/components/ChooseUser.vue)
│           │   ├── [MyDropdown.vue](WorkdayCalendar/ClientApp/src/components/MyDropdown.vue)
│           │   ├── [NavMenu.vue](WorkdayCalendar/ClientApp/src/components/NavMenu.vue)
│           │   ├── [ShiftWeekday.vue](WorkdayCalendar/ClientApp/src/components/ShiftWeekday.vue)
│           │   └── [WorkShift.vue](WorkdayCalendar/ClientApp/src/components/WorkShift.vue)
│           │
│           ├── **頁面元件 (pages/)**
│           ├── pages/
│           │   ├── [Shifts.vue](WorkdayCalendar/ClientApp/src/pages/Shifts.vue)
│           │   ├── [StaffShift.vue](WorkdayCalendar/ClientApp/src/pages/StaffShift.vue)
│           │   └── [Workday.vue](WorkdayCalendar/ClientApp/src/pages/Workday.vue)
│           │
│           ├── **路由設定 (router/)**
│           ├── router/
│           │   └── [index.ts](WorkdayCalendar/ClientApp/src/router/index.ts)
│           │
│           ├── **狀態管理 (store/)**
│           ├── store/
│           │   └── [index.ts](WorkdayCalendar/ClientApp/src/store/index.ts)
│           │
│           └── **靜態資源 (assets/)**
│           └── assets/
│               ├── [Logo64.png](WorkdayCalendar/ClientApp/src/assets/Logo64.png)
│               ├── [calendar.png](WorkdayCalendar/ClientApp/src/assets/calendar.png)
│               ├── [info-circle.svg](WorkdayCalendar/ClientApp/src/assets/info-circle.svg)
│               ├── [info-circle.white.svg](WorkdayCalendar/ClientApp/src/assets/info-circle.white.svg)
│               ├── [logo.png](WorkdayCalendar/ClientApp/src/assets/logo.png)
│               ├── [primevue-logo.png](WorkdayCalendar/ClientApp/src/assets/primevue-logo.png)
│               ├── [sinosign_black.png](WorkdayCalendar/ClientApp/src/assets/sinosign_black.png)
│               ├── [sinosign_blue.svg](WorkdayCalendar/ClientApp/src/assets/sinosign_blue.svg)
│               ├── [sinosign_dark.svg](WorkdayCalendar/ClientApp/src/assets/sinosign_dark.svg)
│               ├── [sinosign_green.png](WorkdayCalendar/ClientApp/src/assets/sinosign_green.png)
│               ├── [sinosign_green.svg](WorkdayCalendar/ClientApp/src/assets/sinosign_green.svg)
│               ├── [sinosign_light.svg](WorkdayCalendar/ClientApp/src/assets/sinosign_light.svg)
│               ├── [sinosign_white.png](WorkdayCalendar/ClientApp/src/assets/sinosign_white.png)
│               ├── [user-circle.svg](WorkdayCalendar/ClientApp/src/assets/user-circle.svg)
│               ├── [user-circle.white.svg](WorkdayCalendar/ClientApp/src/assets/user-circle.white.svg)
│               ├── [user.svg](WorkdayCalendar/ClientApp/src/assets/user.svg)
│               └── [workflow.png](WorkdayCalendar/ClientApp/src/assets/workflow.png)
│
└── **測試專案 (TestWorkdayCalendar/)**
└── TestWorkdayCalendar/
    ├── [TestWorkdayCalendar.csproj](TestWorkdayCalendar/TestWorkdayCalendar.csproj)
    ├── [Startup.cs](TestWorkdayCalendar/Startup.cs)
    ├── [appsettings.json](TestWorkdayCalendar/appsettings.json)
    ├── [appsettings.Development.json](TestWorkdayCalendar/appsettings.Development.json)
    ├── [UnitTest1.cs](TestWorkdayCalendar/UnitTest1.cs)
    └── [ShiftWeekdayControllerTests.cs](TestWorkdayCalendar/ShiftWeekdayControllerTests.cs)

## 技術架構說明

### 後端 (.NET Core 8.0)

- **Program.cs**: 應用程式進入點
- **Startup.cs**: 服務設定與中介軟體配置
- **Controllers/**: RESTful API 端點
- **Models/**: 資料庫實體模型
- **DTO/**: API 資料傳輸格式
- **App_Code/**: 業務邏輯、資料存取層、工具類別

### 前端 (Vue 3.2 + TypeScript)

- **main.ts**: Vue 應用程式進入點
- **App.vue**: 根元件
- **components/**: 可重用的 Vue 元件
- **pages/**: 頁面級元件
- **router/**: Vue Router 路由配置
- **store/**: Vuex/Pinia 狀態管理
- **vite.config.ts**: Vite 建置工具配置

### 測試

- **TestWorkdayCalendar/**: xUnit 單元測試專案
- 使用 FakeItEasy 和 Moq 進行模擬測試

### 主要功能模組

1. **工作日管理**: 記錄與管理工作日類型
2. **班別管理**: 支援多種班別設定
3. **員工管理**: Windows Authentication 整合
4. **加班計算**: 根據工作日類型計算加班時數與倍率
