# 中興社工作日曆系統 - 潛在問題分析報告 (更新版)

## 執行摘要

本報告針對中興社工作日曆系統進行了全面的程式碼審查和架構分析，識別出多個潛在問題領域。主要問題集中在安全性、程式碼品質、架構設計、未使用程式碼和效能優化方面。

## 1. 安全性問題 (🔴 高風險)

### 1.1 資料庫連線字串安全

**問題描述**：

- 資料庫連線字串包含明碼密碼，存在於 `appsettings.json` 中
- 雖然有 RSA 解密機制，但私鑰硬編碼在程式中

**風險等級**：🔴 高風險

**受影響檔案**：

- `WorkdayCalendar/appsettings.json`
- `WorkdayCalendar/App_Code/RsaCrypto.cs` (第23行)
- `TestWorkdayCalendar/appsettings.json`

**程式碼範例**：

```csharp
// RsaCrypto.cs 第23行 - 私鑰硬編碼
const string privateKey = @"<RSAKeyValue><Modulus>0e3VQagf8UBiqTdlIXZX0Lt/4e6xEqsd2tUul7sJ9HoIcEb71U4T7Lt1zbkyR0k+C/rGY5e/xNGQ+bMByLqDZmRuMfAldONmmkDJBPIQHXklIypEz1vzPEnYjTbUv/EPuWJ7XTYMULJ8kwtW7euORPhZQ6fBavobNFfOqBg0wDyfVNl2TirtJMSuLJItlpfAkJ/MFc5wyvgmmU+ej11pdHaG8rLvWtAj0isSQ3zZ7jiJU22iRfoBXe9GlVXYmu+O9zaZXjflVpEtwTbIoiLROXxvcItj6ks58vqXtntN26QVxOw/aPrvrZ3+XQH+Q7JFnfCBhyRRf1ObVCZ64JW9granNG6j/VYcRdw6tywFFuzbGbVJCpzf7MHI+Br6n3lh/bKOpdDyaFeiWsutc4HT5HCQjEN+PF0ODoJsLNDnmEqCK25ej2YPWH";
```

**建議解決方案**：

1. 使用 Azure Key Vault 或 Windows Certificate Store 儲存私鑰
2. 使用環境變數管理敏感設定
3. 實作 IConfiguration 的安全擴充方法

### 1.2 權限控制不足

**問題描述**：

- 部分 API 端點缺乏細緻的權限控制
- 僅依賴 Windows Authentication，缺乏角色基礎存取控制

**建議解決方案**：

1. 實作角色基礎存取控制 (RBAC)
2. 加強 API 端點的權限驗證

## 2. 程式碼品質問題 (🟡 中等風險)

### 2.1 未使用的程式碼和檔案

**問題描述**：
以下程式碼和檔案在專案中存在但未被使用或已過時：

#### 2.1.1 已註解但未移除的程式碼檔案

- `WorkdayCalendar/App_Code/ChinaDate.cs` - 在 csproj 中被排除編譯 (第61行)
- `WorkdayCalendar/App_Code/CNDate.cs` - 在 csproj 中被排除編譯 (第62行)

#### 2.1.2 未使用的變數和方法

**檔案**: `WorkdayCalendar/App_Code/ChinaDate.cs`

```csharp
// 第344行 - 計算了但未使用的變數
bool isLeapMonth = iMonth == leapMonth; // 雖然計算了isLeapMonth，但在此方法中未使用
```

**檔案**: `WorkdayCalendar/App_Code/CNDate.cs`

```csharp
// 第312行 - 未使用的變數
Y = m_Date.Year; // 年份 (未使用於此邏輯)
```

**檔案**: `WorkdayCalendar/App_Code/JobDAO.cs`

```csharp
// 第179行 - 註解的除錯程式碼
//#if DEBUG
```

#### 2.1.3 未使用的介面方法

**檔案**: `WorkdayCalendar/App_Code/IEmployee.cs`

```csharp
// 第9行 - 已註解的方法
// string getUserID(string username); // 已註解的方法：根據使用者名稱取得使用者ID
```

#### 2.1.4 未使用的靜態屬性和方法

**檔案**: `WorkdayCalendar/App_Code/Utility.cs`

```csharp
// 第152行 - 可能未使用的瀏覽器類型
return BrowserType.NETSCAPE; // 現代瀏覽器中可能不再需要
```

#### 2.1.5 前端未使用的程式碼

**檔案**: `WorkdayCalendar/ClientApp/src/pages/Workday.vue`

```javascript
// 第911行 - 未使用的註解
* 用於日曆相關計算（目前未使用，但保留供未來擴展）

// 第1017行 - 未使用的 CSS 樣式
/* 補班日樣式 - 綠色文字（目前未使用） */
```

### 2.2 靜態變數的執行緒安全問題

**問題描述**：
多個控制器使用靜態變數儲存 DTO 實例，可能造成執行緒安全問題：

**受影響檔案**：

- `WorkdayCalendar/Controllers/ShiftWeekdayController.cs` (第24行)
- `WorkdayCalendar/Controllers/WorkdayController.cs` (第33行)

```csharp
// ShiftWeekdayController.cs 第24行
private static ShiftWeekdayDto shiftWeekdayDTO { get; set; }

// WorkdayController.cs 第33行  
private static WorkdayDto workdayDTO { get; set; }
```

**建議解決方案**：

1. 使用依賴注入替代靜態變數
2. 實作執行緒安全的單例模式
3. 使用 IServiceScope 管理物件生命週期

### 2.3 混合的資料存取模式

**問題描述**：
專案同時使用 Entity Framework Core 和傳統 ADO.NET DTO 模式，造成架構不一致：

**EF Core 使用**：

- `WorkdayCalendar/Controllers/EmpWorkShiftController.cs`
- `WorkdayCalendar/Data/WorkdayDbContext.cs`

**傳統 DTO 使用**：

- `WorkdayCalendar/Controllers/ShiftWeekdayController.cs`
- `WorkdayCalendar/Controllers/WorkShiftController.cs`
- `WorkdayCalendar/Controllers/WorkdayController.cs`

### 2.4 重複的程式碼

**問題描述**：
多個 DTO 類別中存在相似的方法實作：

**相似的假日標記方法**：

- `WorkdayCalendar/DTO/WorkdayDTO.cs` (第376行) - `MarkHolidays`
- `WorkdayCalendar/DTO/EmpWorkdayDTO.cs` (第254行) - `MarkHolidays`

**相似的日期類型設定方法**：

- `WorkdayCalendar/DTO/WorkdayDTO.cs` (第395行) - `SetHolidayDayType`
- `WorkdayCalendar/DTO/EmpWorkdayDTO.cs` (第273行) - `SetHolidayDayType`

## 3. 效能問題 (🟡 中等風險)

### 3.1 潛在的 N+1 查詢問題

**問題描述**：
Entity Framework Core 查詢中可能存在 N+1 問題，特別是在關聯資料載入時。

**建議解決方案**：

1. 使用 `Include()` 方法預先載入關聯資料
2. 使用 `AsNoTracking()` 提升查詢效能
3. 實作查詢最佳化策略

### 3.2 大量資料處理效能

**問題描述**：
假日標記功能在處理大量工作日資料時可能效能不佳。

**測試檔案顯示的效能問題**：

- `TestWorkdayCalendar/MarkHolidaysPerformanceTest.cs`
- `TestWorkdayCalendar/MarkHolidaysConsoleTest.cs`

## 4. 架構設計問題 (🟡 中等風險)

### 4.1 單例模式的過度使用

**問題描述**：
多個類別使用單例模式，可能造成測試困難和記憶體洩漏：

**受影響檔案**：

- `WorkdayCalendar/App_Code/SingletonUserName.cs`
- `WorkdayCalendar/App_Code/JobDAO.cs` (SingletonJob 類別)

### 4.2 緊耦合的資料存取層

**問題描述**：
DTO 類別直接依賴 SqlUtil 和連線字串，造成緊耦合。

**建議解決方案**：

1. 實作 Repository 模式
2. 使用依賴注入解耦
3. 建立統一的資料存取介面

## 5. 相依性管理問題 (🟢 低風險)

### 5.1 套件版本管理

**問題描述**：
部分 NuGet 套件可能有更新版本可用。

**建議解決方案**：

1. 定期更新套件至最新穩定版本
2. 建立套件更新策略
3. 使用 Dependabot 自動監控套件更新

## 6. 測試覆蓋率問題 (🟢 低風險)

### 6.1 測試檔案中的重複程式碼

**問題描述**：
測試專案中存在大量重複的輔助方法：

**重複的方法**：

- `CloneWorkday` 方法在多個測試檔案中重複實作
- `SetHolidayDayType` 方法在多個測試檔案中重複實作
- `CalculateStandardDeviation` 方法重複實作

**受影響檔案**：

- `TestWorkdayCalendar/MarkHolidaysConsoleTest.cs`
- `TestWorkdayCalendar/MarkHolidaysPerformanceTest.cs`
- `TestWorkdayCalendar/StandaloneMarkHolidaysTest.cs`

## 7. 前端程式碼問題 (🟢 低風險)

### 7.1 未使用的 CSS 和 JavaScript

**問題描述**：
前端程式碼中存在未使用的樣式和功能。

**建議解決方案**：

1. 清理未使用的 CSS 樣式
2. 移除註解的 JavaScript 程式碼
3. 使用工具分析未使用的程式碼

## 8. 建議改善優先順序

### 🔴 高優先級 (立即處理)

1. **安全性強化**：移除硬編碼的 RSA 私鑰
2. **連線字串安全**：使用安全的設定管理方式
3. **靜態變數問題**：修正控制器中的執行緒安全問題

### 🟡 中優先級 (短期內處理)

1. **程式碼清理**：移除未使用的程式碼和檔案
2. **架構統一**：統一資料存取模式
3. **重複程式碼**：重構重複的方法實作
4. **效能優化**：解決 N+1 查詢問題

### 🟢 低優先級 (長期規劃)

1. **測試改善**：重構測試程式碼，減少重複
2. **套件更新**：建立套件更新策略
3. **前端優化**：清理未使用的前端程式碼
4. **文件維護**：更新技術文件

## 9. 具體改善建議

### 9.1 立即可執行的改善

1. **移除未使用檔案**：

   ```bash
   # 可以安全移除的檔案 (已在 csproj 中排除)
   rm WorkdayCalendar/App_Code/ChinaDate.cs
   rm WorkdayCalendar/App_Code/CNDate.cs
   ```

2. **清理未使用變數**：
   - 移除 `ChinaDate.cs` 中未使用的 `isLeapMonth` 變數
   - 移除 `CNDate.cs` 中未使用的 `Y` 變數賦值

3. **修正靜態變數問題**：

   ```csharp
   // 將靜態變數改為實例變數，使用依賴注入
   private readonly ShiftWeekdayDto _shiftWeekdayDTO;
   ```

### 9.2 中期改善計畫

1. **實作 Repository 模式**
2. **統一使用 Entity Framework Core**
3. **建立共用的測試輔助類別**
4. **實作更細緻的權限控制**

### 9.3 長期架構改善

1. **微服務架構評估**
2. **快取策略實作**
3. **監控和日誌系統強化**
4. **自動化測試覆蓋率提升**

## 10. 風險評估總結

| 風險類別 | 風險等級 | 影響範圍 | 建議處理時程 |
|---------|---------|---------|-------------|
| 安全性問題 | 🔴 高 | 整個系統 | 立即 |
| 程式碼品質 | 🟡 中 | 開發維護 | 1-2 個月 |
| 效能問題 | 🟡 中 | 使用者體驗 | 2-3 個月 |
| 架構設計 | 🟡 中 | 長期維護 | 3-6 個月 |
| 相依性管理 | 🟢 低 | 安全性更新 | 持續進行 |
| 測試覆蓋率 | 🟢 低 | 程式碼品質 | 持續改善 |

---

**報告生成時間**: 2025-07-09  
**分析工具**: 靜態程式碼分析 + 人工審查  
**分析範圍**: 完整專案程式碼庫  
**建議審查週期**: 每季度一次

**注意**: 本報告基於當前程式碼狀態進行分析，建議在實施改善措施前進行充分的測試和備份。
