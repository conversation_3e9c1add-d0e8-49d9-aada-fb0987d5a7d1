# AGENTS Configuration

## Commands
- **Build backend**: `dotnet build` (in WorkdayCalendar)
- **Run backend**: `dotnet run`
- **Install frontend deps**: `npm install` (in ClientApp)
- **Dev frontend**: `npm run dev`
- **Build frontend**: `npm run build`
- **Test all**: `dotnet test && cd ClientApp && npm run test`
- **Test single**: `dotnet test --filter "FullyQualifiedName~TestName"`
- **Lint**: `npm run lint`
- **Format**: `npx prettier --write .`

## Code Style
- C#: PascalCase for classes/methods, camelCase for variables, UPPER_CASE for constants【F:docs/DEVELOPMENT.md†L00213-L00224】
- Vue/TS: Composition API, `<script setup lang="ts">`, relative imports, no semicolons【F:WorkdayCalendar/ClientApp/README.md†L00347-L00352】【F:.cursor/rules/code-style-guide.mdc†L00008-L00013】
- ESLint extends `plugin:vue/vue3-essential`, `eslint:recommended`, `@vue/typescript/recommended`【F:WorkdayCalendar/ClientApp/.eslintrc.js†L00006-L00010】
- Prettier: `semi: false`, `singleQuote: true`, `tabWidth: 2`, `trailingComma: 'es5'`【F:WorkdayCalendar/ClientApp/README.md†L00348-L00352】
- Comments: Chinese (Traditional) for business logic, XML-doc for C# methods【F:.cursor/rules/code-style-guide.mdc†L00008-L00012】【F:docs/DEVELOPMENT.md†L00229-L00237】

## Cursor Rules
Include code-style-guide and product-requirement-document rules:
```md
```@apply .cursor/rules/code-style-guide.mdc
```@apply .cursor/rules/product-requirement-document.mdc
```

## Copilot
_No copilot rules found_

*20 lines total*