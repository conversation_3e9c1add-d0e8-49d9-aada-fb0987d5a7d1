# 故障排除指南 (Troubleshooting Guide)

本文件提供中興社工作日曆系統常見問題的診斷和解決方案，幫助開發人員和系統管理員快速解決技術問題。

## 目錄

- [快速診斷](#快速診斷)
- [應用程式問題](#應用程式問題)
- [資料庫問題](#資料庫問題)
- [認證問題](#認證問題)
- [前端問題](#前端問題)
- [效能問題](#效能問題)
- [部署問題](#部署問題)
- [網路問題](#網路問題)
- [日誌分析](#日誌分析)
- [診斷工具](#診斷工具)

## 快速診斷

### 系統健康檢查清單

```powershell
# 1. 檢查 IIS 服務狀態
Get-Service W3SVC | Select-Object Name, Status

# 2. 檢查應用程式集區狀態
Import-Module WebAdministration
Get-IISAppPool | Where-Object {$_.Name -eq "WorkdayCalendarPool"} | Select-Object Name, State

# 3. 檢查網站狀態
Get-IISSite | Where-Object {$_.Name -eq "WorkdayCalendar"} | Select-Object Name, State

# 4. 檢查 SQL Server 連線
Test-NetConnection -ComputerName "sql-server" -Port 1433

# 5. 檢查應用程式回應
Invoke-WebRequest -Uri "https://localhost/api/User/GetCurrentUser" -UseDefaultCredentials
```

### 快速修復步驟

1. **重新啟動應用程式集區**
   ```powershell
   Restart-WebAppPool -Name "WorkdayCalendarPool"
   ```

2. **重新啟動 IIS**
   ```powershell
   iisreset
   ```

3. **清除瀏覽器快取**
   - 按 Ctrl+F5 強制重新整理
   - 清除瀏覽器快取和 Cookie

## 應用程式問題

### 問題 1: 應用程式無法啟動

#### 症狀
- HTTP 500 內部伺服器錯誤
- 白畫面或空白頁面
- "Application failed to start" 錯誤訊息

#### 診斷步驟

1. **檢查事件日誌**
   ```powershell
   Get-EventLog -LogName Application -Source "IIS AspNetCore Module V2" -Newest 10 | Format-Table -Wrap
   ```

2. **檢查 stdout 日誌**
   ```powershell
   Get-Content "C:\inetpub\wwwroot\WorkdayCalendar\logs\stdout*.log" -Tail 20
   ```

3. **檢查應用程式日誌**
   ```powershell
   Get-Content "C:\inetpub\wwwroot\WorkdayCalendar\logs\workday-*.log" -Tail 20
   ```

#### 常見原因與解決方案

| 原因 | 解決方案 |
|------|----------|
| .NET Runtime 未安裝 | 安裝 ASP.NET Core Runtime 8.0 |
| 設定檔錯誤 | 檢查 appsettings.json 語法 |
| 相依套件缺失 | 重新部署應用程式 |
| 權限不足 | 設定 IIS_IUSRS 權限 |

#### 詳細解決步驟

**1. 檢查 .NET Runtime**
```powershell
dotnet --list-runtimes | Where-Object {$_ -like "*Microsoft.AspNetCore.App 8.0*"}
```

**2. 修復權限問題**
```powershell
icacls "C:\inetpub\wwwroot\WorkdayCalendar" /grant "IIS_IUSRS:(OI)(CI)F" /T
icacls "C:\inetpub\wwwroot\WorkdayCalendar\logs" /grant "IIS_IUSRS:(OI)(CI)F" /T
```

**3. 驗證設定檔**
```powershell
# 使用 PowerShell 驗證 JSON 語法
$config = Get-Content "C:\inetpub\wwwroot\WorkdayCalendar\appsettings.json" | ConvertFrom-Json
Write-Host "設定檔語法正確"
```

### 問題 2: API 端點回傳 404 錯誤

#### 症狀
- API 呼叫回傳 404 Not Found
- Swagger UI 無法載入
- 路由無法正確解析

#### 診斷步驟

1. **檢查路由設定**
   ```csharp
   // 確認 Controller 有正確的屬性
   [ApiController]
   [Route("/api/[controller]/[action]")]
   public class WorkdayController : ControllerBase
   ```

2. **檢查 URL 重寫規則**
   ```xml
   <!-- web.config -->
   <system.webServer>
     <rewrite>
       <rules>
         <rule name="SPA Routes" stopProcessing="true">
           <match url=".*" />
           <conditions logicalGrouping="MatchAll">
             <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
             <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
             <add input="{REQUEST_URI}" pattern="^/(api)" negate="true" />
           </conditions>
           <action type="Rewrite" url="/" />
         </rule>
       </rules>
     </rewrite>
   </system.webServer>
   ```

#### 解決方案

1. **重新建置並部署應用程式**
2. **檢查 Controller 命名空間**
3. **驗證路由模板語法**

### 問題 3: 記憶體洩漏

#### 症狀
- 應用程式記憶體使用量持續增加
- 系統回應變慢
- OutOfMemoryException 錯誤

#### 診斷步驟

1. **監控記憶體使用量**
   ```powershell
   # 持續監控 w3wp 程序記憶體使用量
   while ($true) {
       Get-Process w3wp | Select-Object Id, ProcessName, @{Name="Memory(MB)";Expression={[math]::Round($_.WorkingSet/1MB,2)}}
       Start-Sleep 10
   }
   ```

2. **建立記憶體傾印**
   ```bash
   dotnet-dump collect -p <process-id> -o memory-dump.dmp
   ```

#### 解決方案

1. **檢查 Entity Framework 查詢**
   ```csharp
   // 使用 AsNoTracking 避免實體追蹤
   var workdays = await _context.Workdays
       .AsNoTracking()
       .Where(w => w.WorkDate >= startDate)
       .ToListAsync();
   ```

2. **正確釋放資源**
   ```csharp
   // 使用 using 語句確保資源釋放
   using var connection = new SqlConnection(connectionString);
   using var command = new SqlCommand(sql, connection);
   ```

## 資料庫問題

### 問題 1: 資料庫連線失敗

#### 症狀
- "Cannot open database" 錯誤
- 連線逾時
- 登入失敗

#### 診斷步驟

1. **測試基本連線**
   ```powershell
   Test-NetConnection -ComputerName "sql-server" -Port 1433
   ```

2. **檢查 SQL Server 服務**
   ```powershell
   Get-Service MSSQLSERVER | Select-Object Name, Status
   ```

3. **測試登入認證**
   ```sql
   -- 使用 SQLCMD 測試連線
   sqlcmd -S server-name -U username -P password -Q "SELECT @@VERSION"
   ```

#### 解決方案

1. **檢查連線字串**
   ```json
   {
     "ConnectionStrings": {
       "Workday": "Server=server-name;Database=Workday;User Id=username;Password=password;TrustServerCertificate=true;Connection Timeout=30;"
     }
   }
   ```

2. **啟用 TCP/IP 協定**
   - 開啟 SQL Server Configuration Manager
   - 啟用 TCP/IP 協定
   - 重新啟動 SQL Server 服務

3. **設定防火牆規則**
   ```powershell
   New-NetFirewallRule -DisplayName "SQL Server" -Direction Inbound -Protocol TCP -LocalPort 1433 -Action Allow
   ```

### 問題 2: 查詢效能緩慢

#### 症狀
- API 回應時間過長
- 資料庫 CPU 使用率高
- 查詢逾時錯誤

#### 診斷步驟

1. **檢查執行中的查詢**
   ```sql
   SELECT 
       s.session_id,
       r.status,
       r.command,
       r.cpu_time,
       r.total_elapsed_time,
       t.text
   FROM sys.dm_exec_requests r
   CROSS APPLY sys.dm_exec_sql_text(r.sql_handle) t
   JOIN sys.dm_exec_sessions s ON r.session_id = s.session_id
   WHERE r.total_elapsed_time > 5000;
   ```

2. **檢查索引使用情況**
   ```sql
   SELECT 
       OBJECT_NAME(s.object_id) AS TableName,
       i.name AS IndexName,
       s.user_seeks,
       s.user_scans,
       s.user_lookups,
       s.user_updates
   FROM sys.dm_db_index_usage_stats s
   JOIN sys.indexes i ON s.object_id = i.object_id AND s.index_id = i.index_id
   WHERE s.database_id = DB_ID();
   ```

#### 解決方案

1. **新增適當的索引**
   ```sql
   -- 為常用查詢欄位建立索引
   CREATE INDEX IX_Workday_WorkDate ON Workday(WorkDate);
   CREATE INDEX IX_Workday_ShiftId_WorkDate ON Workday(ShiftId, WorkDate);
   ```

2. **優化查詢語句**
   ```csharp
   // 避免 N+1 查詢問題
   var workdays = await _context.Workdays
       .Include(w => w.WorkShift)
       .Where(w => w.WorkDate >= startDate)
       .ToListAsync();
   ```

### 問題 3: 資料庫鎖定

#### 症狀
- 查詢被阻塞
- 應用程式無回應
- 死結錯誤

#### 診斷步驟

1. **檢查阻塞情況**
   ```sql
   SELECT 
       blocking_session_id,
       session_id,
       wait_type,
       wait_time,
       wait_resource
   FROM sys.dm_exec_requests
   WHERE blocking_session_id <> 0;
   ```

2. **檢查死結**
   ```sql
   -- 啟用死結追蹤
   DBCC TRACEON(1222, -1);
   
   -- 檢查錯誤日誌中的死結資訊
   EXEC sp_readerrorlog;
   ```

#### 解決方案

1. **終止阻塞的工作階段**
   ```sql
   KILL <session_id>;
   ```

2. **調整交易隔離等級**
   ```csharp
   using var transaction = await _context.Database.BeginTransactionAsync(IsolationLevel.ReadCommitted);
   ```

## 認證問題

### 問題 1: Windows Authentication 失敗

#### 症狀
- HTTP 401 Unauthorized 錯誤
- 重複要求輸入認證
- 無法取得使用者資訊

#### 診斷步驟

1. **檢查 IIS 認證設定**
   ```powershell
   Get-WebConfigurationProperty -Filter "system.webServer/security/authentication/windowsAuthentication" -Name "enabled" -PSPath "IIS:" -Location "WorkdayCalendar"
   ```

2. **檢查瀏覽器設定**
   - 確認網站在信任的網站清單中
   - 啟用整合式 Windows 驗證

#### 解決方案

1. **啟用 Windows Authentication**
   ```powershell
   Set-WebConfigurationProperty -Filter "system.webServer/security/authentication/windowsAuthentication" -Name "enabled" -Value "True" -PSPath "IIS:" -Location "WorkdayCalendar"
   Set-WebConfigurationProperty -Filter "system.webServer/security/authentication/anonymousAuthentication" -Name "enabled" -Value "False" -PSPath "IIS:" -Location "WorkdayCalendar"
   ```

2. **設定應用程式集區身分**
   ```powershell
   Set-ItemProperty -Path "IIS:\AppPools\WorkdayCalendarPool" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
   ```

### 問題 2: 跨網域認證問題

#### 症狀
- 不同網域使用者無法登入
- 認證在某些用戶端失敗

#### 解決方案

1. **設定 Kerberos 認證**
   ```powershell
   # 註冊 SPN
   setspn -A HTTP/workday.sinotech.org.tw DOMAIN\ServiceAccount
   setspn -A HTTP/workday DOMAIN\ServiceAccount
   ```

2. **設定委派**
   - 在 Active Directory 中設定服務帳戶的委派權限

## 前端問題

### 問題 1: 前端資源載入失敗

#### 症狀
- JavaScript 或 CSS 檔案 404 錯誤
- 頁面樣式異常
- 功能無法正常運作

#### 診斷步驟

1. **檢查建置輸出**
   ```bash
   cd WorkdayCalendar/ClientApp
   npm run build
   ```

2. **檢查檔案路徑**
   ```powershell
   Get-ChildItem "C:\inetpub\wwwroot\WorkdayCalendar\ClientApp\dist" -Recurse
   ```

#### 解決方案

1. **重新建置前端**
   ```bash
   cd WorkdayCalendar/ClientApp
   rm -rf node_modules
   npm install
   npm run build
   ```

2. **檢查 base URL 設定**
   ```javascript
   // vite.config.ts
   export default defineConfig({
     base: '/WorkdayCalendar/',
     // 其他設定...
   });
   ```

### 問題 2: API 呼叫失敗

#### 症狀
- CORS 錯誤
- 網路錯誤
- 認證失敗

#### 解決方案

1. **設定 CORS**
   ```csharp
   // Startup.cs
   public void ConfigureServices(IServiceCollection services)
   {
       services.AddCors(options =>
       {
           options.AddPolicy("AllowSpecificOrigin",
               builder => builder
                   .WithOrigins("https://localhost:3000")
                   .AllowAnyMethod()
                   .AllowAnyHeader()
                   .AllowCredentials());
       });
   }
   ```

2. **設定 axios 預設值**
   ```typescript
   // api/index.ts
   axios.defaults.withCredentials = true;
   axios.defaults.baseURL = '/api';
   ```

## 效能問題

### 問題 1: 頁面載入緩慢

#### 診斷步驟

1. **使用瀏覽器開發者工具**
   - 檢查 Network 標籤
   - 分析載入時間
   - 檢查資源大小

2. **檢查伺服器效能**
   ```powershell
   Get-Counter "\Processor(_Total)\% Processor Time" -SampleInterval 1 -MaxSamples 5
   Get-Counter "\Memory\Available MBytes"
   ```

#### 解決方案

1. **啟用壓縮**
   ```xml
   <!-- web.config -->
   <system.webServer>
     <httpCompression>
       <dynamicTypes>
         <add mimeType="application/json" enabled="true" />
         <add mimeType="application/javascript" enabled="true" />
       </dynamicTypes>
     </httpCompression>
   </system.webServer>
   ```

2. **設定快取標頭**
   ```csharp
   [ResponseCache(Duration = 3600)]
   public async Task<IActionResult> GetStaticData()
   {
       // 實作...
   }
   ```

## 部署問題

### 問題 1: 部署後功能異常

#### 診斷步驟

1. **比較設定檔**
   ```powershell
   Compare-Object (Get-Content "appsettings.Development.json") (Get-Content "appsettings.Production.json")
   ```

2. **檢查環境變數**
   ```powershell
   Get-ChildItem Env: | Where-Object {$_.Name -like "*ASPNETCORE*"}
   ```

#### 解決方案

1. **同步設定檔**
2. **驗證環境特定設定**
3. **檢查相依套件版本**

## 日誌分析

### 常見錯誤模式

#### 1. 記憶體不足
```
System.OutOfMemoryException: Exception of type 'System.OutOfMemoryException' was thrown.
```

#### 2. 資料庫連線逾時
```
Microsoft.Data.SqlClient.SqlException: Timeout expired. The timeout period elapsed prior to completion of the operation
```

#### 3. 認證失敗
```
System.UnauthorizedAccessException: Access to the path is denied.
```

### 日誌分析腳本

```powershell
# 分析錯誤日誌
$logPath = "C:\inetpub\wwwroot\WorkdayCalendar\logs"
$errorPattern = "ERROR|FATAL|Exception"

Get-ChildItem $logPath -Filter "*.log" | ForEach-Object {
    $errors = Select-String -Path $_.FullName -Pattern $errorPattern
    if ($errors) {
        Write-Host "檔案: $($_.Name)"
        $errors | ForEach-Object { Write-Host "  $($_.Line)" }
    }
}
```

## 診斷工具

### 1. 效能監控工具

```powershell
# 安裝 dotnet 診斷工具
dotnet tool install --global dotnet-counters
dotnet tool install --global dotnet-dump
dotnet tool install --global dotnet-trace

# 監控效能計數器
dotnet-counters monitor --process-id <pid>

# 建立效能追蹤
dotnet-trace collect --process-id <pid> --duration 00:00:30
```

### 2. 網路診斷

```powershell
# 檢查連接埠狀態
netstat -an | findstr :80
netstat -an | findstr :443
netstat -an | findstr :1433

# 測試 DNS 解析
nslookup workday.sinotech.org.tw

# 追蹤路由
tracert workday.sinotech.org.tw
```

### 3. IIS 診斷

```powershell
# 檢查 IIS 模組
Get-WebGlobalModule | Where-Object {$_.Name -like "*AspNetCore*"}

# 檢查應用程式集區回收事件
Get-EventLog -LogName System -Source "Microsoft-Windows-WAS" -Newest 10
```

## 預防措施

### 1. 監控設定

```json
// appsettings.json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.EntityFrameworkCore": "Warning"
    }
  },
  "HealthChecks": {
    "UI": {
      "HealthChecksUri": "/health"
    }
  }
}
```

### 2. 自動化檢查

```powershell
# 健康檢查腳本
$healthCheckUrl = "https://workday.sinotech.org.tw/health"
$response = Invoke-WebRequest -Uri $healthCheckUrl -UseDefaultCredentials

if ($response.StatusCode -eq 200) {
    Write-Host "系統正常運作"
} else {
    Write-Host "系統異常，狀態碼: $($response.StatusCode)"
    # 發送警告通知
}
```

## 聯絡支援

當遇到無法解決的問題時，請聯絡技術支援並提供以下資訊：

1. **錯誤描述**: 詳細描述問題症狀
2. **重現步驟**: 如何重現問題
3. **錯誤日誌**: 相關的錯誤日誌內容
4. **系統資訊**: 作業系統、.NET 版本、IIS 版本
5. **最近變更**: 最近的系統變更或更新

**聯絡方式**:
- **技術支援**: <EMAIL>
- **緊急聯絡**: <EMAIL>
- **內部分機**: 1234 (上班時間)