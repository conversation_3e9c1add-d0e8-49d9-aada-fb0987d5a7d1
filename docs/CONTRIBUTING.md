# 貢獻指南 (Contributing Guide)

歡迎參與中興社工作日曆系統的開發！本文件說明如何為專案做出貢獻，包含程式碼規範、開發流程和最佳實務。

## 目錄

- [開始貢獻](#開始貢獻)
- [開發環境設定](#開發環境設定)
- [程式碼規範](#程式碼規範)
- [提交規範](#提交規範)
- [Pull Request 流程](#pull-request-流程)
- [測試要求](#測試要求)
- [文件撰寫](#文件撰寫)
- [問題回報](#問題回報)

## 開始貢獻

### 貢獻類型

我們歡迎以下類型的貢獻：

- 🐛 **Bug 修復**: 修復已知問題
- ✨ **新功能**: 新增功能或改善現有功能
- 📚 **文件改善**: 改善文件內容或新增文件
- 🎨 **UI/UX 改善**: 改善使用者介面和體驗
- ⚡ **效能最佳化**: 提升系統效能
- 🧪 **測試**: 新增或改善測試覆蓋率
- 🔧 **工具改善**: 改善開發工具和流程

### 參與方式

1. **Fork 專案**: 在 GitHub 上 fork 此專案
2. **建立分支**: 為你的貢獻建立新的功能分支
3. **開發**: 進行開發並遵循程式碼規範
4. **測試**: 確保所有測試通過
5. **提交**: 提交你的變更
6. **Pull Request**: 建立 Pull Request

## 開發環境設定

### 必要軟體

- .NET 8.0 SDK
- Node.js 16+
- Git
- Visual Studio 2022 或 Visual Studio Code

### 設定步驟

1. **Fork 並複製專案**
   ```bash
   git clone https://github.com/your-username/WorkdayCalendar.git
   cd WorkdayCalendar
   ```

2. **設定上游遠端**
   ```bash
   git remote add upstream https://github.com/original-repo/WorkdayCalendar.git
   ```

3. **安裝相依套件**
   ```bash
   # 後端
   dotnet restore
   
   # 前端
   cd WorkdayCalendar/ClientApp
   npm install
   ```

4. **設定資料庫**
   ```bash
   sqlcmd -S localhost -E -i WorkdayCalendar/App_Data/CreateDb.sql
   ```

5. **執行應用程式**
   ```bash
   # 後端
   dotnet run --project WorkdayCalendar
   
   # 前端 (另一個終端機)
   cd WorkdayCalendar/ClientApp
   npm run dev
   ```

## 程式碼規範

### C# 後端規範

#### 命名規範

```csharp
// 類別: PascalCase
public class WorkdayController : ControllerBase

// 方法: PascalCase
public async Task<IActionResult> GetWorkdays()

// 屬性: PascalCase
public string ShiftName { get; set; }

// 變數: camelCase
private readonly IConfiguration configuration;

// 常數: UPPER_CASE
private const int MAX_WORK_HOURS = 12;

// 私有欄位: _camelCase
private readonly ILogger _logger;
```

#### 註解規範

```csharp
/// <summary>
/// 取得指定日期範圍內的工作日曆資料
/// </summary>
/// <param name="startDate">開始日期 (包含)</param>
/// <param name="endDate">結束日期 (包含)</param>
/// <param name="shiftId">班別編號，null 表示取得所有班別</param>
/// <returns>工作日曆資料列表</returns>
/// <exception cref="ArgumentException">當開始日期大於結束日期時拋出</exception>
/// <remarks>
/// 此方法會根據指定的日期範圍查詢工作日曆資料。
/// 如果提供班別編號，則只回傳該班別的資料。
/// 查詢結果會按照工作日期排序。
/// </remarks>
public async Task<List<WorkdayDto>> GetWorkdays(
    DateTime startDate, 
    DateTime endDate, 
    int? shiftId = null)
{
    // 驗證輸入參數
    if (startDate > endDate)
    {
        throw new ArgumentException("開始日期不能大於結束日期", nameof(startDate));
    }
    
    // 記錄查詢資訊
    _logger.LogInformation("查詢工作日曆: {StartDate} - {EndDate}, 班別: {ShiftId}", 
                          startDate, endDate, shiftId);
    
    // 建立查詢條件
    var query = _context.Workdays
        .Where(w => w.WorkDate >= startDate && w.WorkDate <= endDate);
    
    // 如果指定班別，加入班別篩選條件
    if (shiftId.HasValue)
    {
        query = query.Where(w => w.ShiftId == shiftId.Value);
    }
    
    // 執行查詢並排序
    return await query
        .OrderBy(w => w.WorkDate)
        .ToListAsync();
}
```

#### 錯誤處理

```csharp
try
{
    // 執行業務邏輯
    var result = await ProcessWorkday(workday);
    return Ok(result);
}
catch (ValidationException ex)
{
    // 記錄驗證錯誤
    _logger.LogWarning(ex, "工作日曆資料驗證失敗: {WorkDate}", workday.WorkDate);
    return BadRequest(new { message = ex.Message, field = ex.PropertyName });
}
catch (DbUpdateException ex)
{
    // 記錄資料庫錯誤
    _logger.LogError(ex, "資料庫更新失敗: {WorkDate}", workday.WorkDate);
    return StatusCode(500, new { message = "資料儲存失敗，請稍後再試" });
}
catch (Exception ex)
{
    // 記錄未預期的錯誤
    _logger.LogError(ex, "處理工作日曆時發生未預期的錯誤");
    return StatusCode(500, new { message = "系統暫時無法處理請求" });
}
```

### Vue.js 前端規範

#### 元件結構

```vue
<template>
  <!-- 使用語意化的 HTML 標籤 -->
  <section class="workday-calendar">
    <header class="calendar-header">
      <h2>{{ title }}</h2>
      <nav class="calendar-nav">
        <!-- 導航內容 -->
      </nav>
    </header>
    
    <main class="calendar-content">
      <!-- 主要內容 -->
    </main>
  </section>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import type { Workday } from '@/types/Workday'

// Props 定義 - 使用 interface 定義型別
interface Props {
  /** 日曆標題 */
  title: string
  /** 班別編號，用於篩選特定班別的工作日 */
  shiftId?: number
  /** 是否為唯讀模式 */
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  shiftId: undefined,
  readonly: false
})

// Emits 定義 - 明確定義所有事件
interface Emits {
  /** 當工作日資料更新時觸發 */
  (e: 'update', workday: Workday): void
  /** 當工作日被刪除時觸發 */
  (e: 'delete', workdayId: string): void
  /** 當載入狀態改變時觸發 */
  (e: 'loading-change', isLoading: boolean): void
}

const emit = defineEmits<Emits>()

// 響應式資料
const workdays = ref<Workday[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

// 計算屬性
const filteredWorkdays = computed(() => {
  if (!props.shiftId) return workdays.value
  return workdays.value.filter(w => w.shiftId === props.shiftId)
})

const hasWorkdays = computed(() => filteredWorkdays.value.length > 0)

// 監聽器
watch(() => props.shiftId, async (newShiftId) => {
  if (newShiftId) {
    await loadWorkdays()
  }
}, { immediate: true })

// 生命週期
onMounted(async () => {
  await loadWorkdays()
})

// 方法 - 使用 JSDoc 註解
/**
 * 載入工作日曆資料
 * @param force 是否強制重新載入，忽略快取
 */
async function loadWorkdays(force = false): Promise<void> {
  try {
    loading.value = true
    error.value = null
    emit('loading-change', true)
    
    // API 呼叫邏輯
    const response = await workdayApi.getAll({
      shiftId: props.shiftId,
      force
    })
    
    workdays.value = response.data
  } catch (err) {
    error.value = err instanceof Error ? err.message : '載入失敗'
    console.error('載入工作日曆失敗:', err)
  } finally {
    loading.value = false
    emit('loading-change', false)
  }
}

/**
 * 處理工作日更新
 * @param workday 要更新的工作日資料
 */
function handleWorkdayUpdate(workday: Workday): void {
  // 更新本地資料
  const index = workdays.value.findIndex(w => w.id === workday.id)
  if (index !== -1) {
    workdays.value[index] = { ...workday }
  }
  
  // 觸發事件
  emit('update', workday)
}
</script>

<style scoped>
/* 使用 CSS 變數和語意化的類別名稱 */
.workday-calendar {
  --primary-color: #007bff;
  --border-radius: 0.375rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  border-bottom: 1px solid #dee2e6;
}

.calendar-content {
  padding: var(--spacing-md);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .calendar-header {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}
</style>
```

#### TypeScript 型別定義

```typescript
// types/Workday.ts

/** 工作日類型枚舉 */
export enum DayType {
  /** 正常工作日 */
  Normal = 1,
  /** 假日 */
  Holiday = 2,
  /** 補班日 */
  MakeupWorkday = 3,
  /** 特殊工作日 */
  Special = 4
}

/** 工作日介面 */
export interface Workday {
  /** 工作日唯一識別碼 */
  id: string
  /** 班別編號 */
  shiftId: number
  /** 工作日期 (ISO 8601 格式) */
  workDate: string
  /** 工作日類型 */
  dayType: DayType
  /** 上班時間 (ISO 8601 格式) */
  arrivalTime: string
  /** 下班時間 (ISO 8601 格式) */
  departureTime: string
  /** 工作時數 */
  workHours: number
  /** 備註 */
  comment?: string
  /** 建立時間 */
  createdAt: string
  /** 最後更新時間 */
  updatedAt: string
}

/** API 回應包裝器 */
export interface ApiResponse<T> {
  /** 操作是否成功 */
  success: boolean
  /** 回應資料 */
  data: T
  /** 訊息 */
  message?: string
  /** 錯誤資訊 */
  error?: {
    code: string
    message: string
    details?: string
  }
}

/** 分頁資料 */
export interface PaginatedData<T> {
  /** 資料項目 */
  items: T[]
  /** 總項目數 */
  totalCount: number
  /** 目前頁數 (從 1 開始) */
  currentPage: number
  /** 每頁項目數 */
  pageSize: number
  /** 總頁數 */
  totalPages: number
  /** 是否有下一頁 */
  hasNextPage: boolean
  /** 是否有上一頁 */
  hasPreviousPage: boolean
}
```

## 提交規範

### Commit Message 格式

使用 [Conventional Commits](https://www.conventionalcommits.org/) 規範：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

#### 類型 (Type)

- `feat`: 新功能
- `fix`: Bug 修復
- `docs`: 文件變更
- `style`: 程式碼格式變更 (不影響功能)
- `refactor`: 重構 (不是新功能也不是 Bug 修復)
- `perf`: 效能改善
- `test`: 新增或修改測試
- `chore`: 建置流程或輔助工具變更

#### 範圍 (Scope)

- `api`: 後端 API
- `ui`: 前端使用者介面
- `db`: 資料庫相關
- `auth`: 認證相關
- `config`: 設定檔相關

#### 範例

```bash
feat(api): 新增批次新增工作日曆功能

新增 POST /api/Workday/AddBatch 端點，支援一次新增多個工作日。
包含以下功能：
- 日期範圍設定
- 工作日篩選
- 假日排除選項

Closes #123

fix(ui): 修復班別選擇器在 IE11 的顯示問題

- 修正 CSS flexbox 相容性問題
- 新增 polyfill 支援
- 更新瀏覽器相容性文件

docs: 更新 API 文件中的認證說明

- 新增 Windows Authentication 設定步驟
- 更新範例程式碼
- 修正錯誤的端點 URL
```

### 分支命名規範

```bash
# 功能分支
feature/add-batch-workday-creation
feature/improve-shift-management-ui

# Bug 修復分支
fix/workday-calendar-display-issue
fix/authentication-timeout-problem

# 文件分支
docs/update-api-documentation
docs/add-deployment-guide

# 重構分支
refactor/optimize-database-queries
refactor/modernize-vue-components
```

## Pull Request 流程

### 1. 準備 Pull Request

1. **確保分支是最新的**
   ```bash
   git checkout main
   git pull upstream main
   git checkout your-feature-branch
   git rebase main
   ```

2. **執行測試**
   ```bash
   # 後端測試
   dotnet test
   
   # 前端測試
   cd WorkdayCalendar/ClientApp
   npm run test
   npm run lint
   ```

3. **檢查程式碼品質**
   ```bash
   # 程式碼格式化
   dotnet format
   npm run format
   ```

### 2. 建立 Pull Request

#### PR 標題格式

```
<type>: <簡短描述>
```

範例：
- `feat: 新增批次新增工作日曆功能`
- `fix: 修復班別選擇器顯示問題`
- `docs: 更新部署指南`

#### PR 描述範本

```markdown
## 變更摘要
簡要描述此 PR 的主要變更內容。

## 變更類型
- [ ] Bug 修復
- [ ] 新功能
- [ ] 重構
- [ ] 文件更新
- [ ] 效能改善
- [ ] 測試

## 詳細說明
詳細描述變更的內容、原因和影響。

## 測試
- [ ] 單元測試通過
- [ ] 整合測試通過
- [ ] 手動測試完成
- [ ] 瀏覽器相容性測試

## 檢查清單
- [ ] 程式碼遵循專案規範
- [ ] 新增適當的測試
- [ ] 更新相關文件
- [ ] 無 breaking changes (或已在描述中說明)

## 相關 Issue
Closes #123
Related to #456

## 截圖 (如適用)
如果是 UI 變更，請提供前後對比截圖。

## 額外資訊
任何其他相關資訊。
```

### 3. Code Review 流程

#### 審查重點

1. **功能正確性**
   - 功能是否符合需求
   - 邊界條件處理
   - 錯誤處理

2. **程式碼品質**
   - 可讀性和可維護性
   - 遵循專案規範
   - 適當的註解

3. **效能考量**
   - 查詢效率
   - 記憶體使用
   - 網路請求最佳化

4. **安全性**
   - 輸入驗證
   - 權限檢查
   - SQL 注入防護

#### 審查回應

```markdown
# 建議變更
建議在第 45 行加入 null 檢查：

```csharp
if (workday?.ShiftId == null)
{
    throw new ArgumentNullException(nameof(workday));
}
```

# 讚賞
這個重構很好地改善了程式碼的可讀性！

# 問題
這個變更會影響現有的 API 相容性嗎？
```

## 測試要求

### 後端測試

#### 單元測試

```csharp
[Fact]
public async Task GetWorkdays_ValidDateRange_ReturnsCorrectWorkdays()
{
    // Arrange
    var startDate = new DateTime(2024, 1, 1);
    var endDate = new DateTime(2024, 1, 31);
    var expectedWorkdays = CreateTestWorkdays();
    
    _mockRepository.Setup(r => r.GetWorkdaysAsync(startDate, endDate, null))
                  .ReturnsAsync(expectedWorkdays);
    
    // Act
    var result = await _controller.GetWorkdays(startDate, endDate);
    
    // Assert
    var okResult = Assert.IsType<OkObjectResult>(result);
    var workdays = Assert.IsType<List<WorkdayDto>>(okResult.Value);
    Assert.Equal(expectedWorkdays.Count, workdays.Count);
}

[Theory]
[InlineData("2024-01-01", "2023-12-31")] // 開始日期大於結束日期
[InlineData("2024-13-01", "2024-12-31")] // 無效月份
public async Task GetWorkdays_InvalidDateRange_ThrowsArgumentException(
    string startDateStr, string endDateStr)
{
    // Arrange
    var startDate = DateTime.Parse(startDateStr);
    var endDate = DateTime.Parse(endDateStr);
    
    // Act & Assert
    await Assert.ThrowsAsync<ArgumentException>(
        () => _controller.GetWorkdays(startDate, endDate));
}
```

#### 整合測試

```csharp
public class WorkdayControllerIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public WorkdayControllerIntegrationTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task GetWorkdays_ReturnsSuccessAndCorrectContentType()
    {
        // Act
        var response = await _client.GetAsync("/api/Workday/GetAll?year=2024&month=1");

        // Assert
        response.EnsureSuccessStatusCode();
        Assert.Equal("application/json; charset=utf-8", 
                    response.Content.Headers.ContentType?.ToString());
    }
}
```

### 前端測試

#### 元件測試

```typescript
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import WorkdayCalendar from '@/components/WorkdayCalendar.vue'
import { workdayApi } from '@/components/api'

// Mock API
vi.mock('@/components/api', () => ({
  workdayApi: {
    getAll: vi.fn()
  }
}))

describe('WorkdayCalendar.vue', () => {
  it('renders loading state correctly', () => {
    const wrapper = mount(WorkdayCalendar, {
      props: {
        title: '工作日曆',
        shiftId: 1
      }
    })
    
    expect(wrapper.find('.loading').exists()).toBe(true)
  })

  it('loads workdays on mount', async () => {
    const mockWorkdays = [
      {
        id: '1',
        shiftId: 1,
        workDate: '2024-01-15',
        dayType: 1,
        arrivalTime: '08:00',
        departureTime: '17:00',
        workHours: 8
      }
    ]
    
    vi.mocked(workdayApi.getAll).mockResolvedValue({
      data: mockWorkdays
    })
    
    const wrapper = mount(WorkdayCalendar, {
      props: {
        title: '工作日曆',
        shiftId: 1
      }
    })
    
    await wrapper.vm.$nextTick()
    
    expect(workdayApi.getAll).toHaveBeenCalledWith({
      shiftId: 1,
      force: false
    })
  })

  it('emits update event when workday is modified', async () => {
    const wrapper = mount(WorkdayCalendar)
    const workday = {
      id: '1',
      shiftId: 1,
      workDate: '2024-01-15',
      dayType: 1,
      arrivalTime: '08:00',
      departureTime: '17:00',
      workHours: 8
    }
    
    await wrapper.vm.handleWorkdayUpdate(workday)
    
    expect(wrapper.emitted('update')).toBeTruthy()
    expect(wrapper.emitted('update')?.[0]).toEqual([workday])
  })
})
```

#### E2E 測試

```typescript
import { test, expect } from '@playwright/test'

test.describe('工作日曆管理', () => {
  test('應能成功新增工作日', async ({ page }) => {
    // 導航到工作日曆頁面
    await page.goto('/workday')
    
    // 點擊新增按鈕
    await page.click('[data-testid="add-workday-btn"]')
    
    // 填寫表單
    await page.fill('[data-testid="work-date"]', '2024-01-15')
    await page.selectOption('[data-testid="shift-select"]', '1')
    await page.fill('[data-testid="arrival-time"]', '08:00')
    await page.fill('[data-testid="departure-time"]', '17:00')
    
    // 提交表單
    await page.click('[data-testid="save-btn"]')
    
    // 驗證成功訊息
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
    
    // 驗證工作日出現在列表中
    await expect(page.locator('[data-testid="workday-list"]')).toContainText('2024-01-15')
  })
})
```

### 測試覆蓋率要求

- **後端**: 最低 80% 程式碼覆蓋率
- **前端**: 最低 70% 程式碼覆蓋率
- **關鍵業務邏輯**: 100% 覆蓋率

## 文件撰寫

### 文件類型

1. **API 文件**: 使用 Swagger/OpenAPI
2. **使用者文件**: Markdown 格式
3. **開發者文件**: 程式碼註解 + Markdown
4. **部署文件**: 詳細的部署步驟

### 文件規範

#### Markdown 格式

```markdown
# 標題使用 H1

## 主要章節使用 H2

### 子章節使用 H3

- 使用項目符號列表
- 保持一致的格式

1. 使用數字列表
2. 表示步驟順序

`程式碼片段使用反引號`

```csharp
// 程式碼區塊使用三個反引號
public class Example
{
    // 程式碼內容
}
```

> 重要資訊使用引用格式

| 欄位 | 類型 | 說明 |
|------|------|------|
| id | string | 唯一識別碼 |
```

#### 程式碼註解

```csharp
/// <summary>
/// 類別或方法的簡要說明
/// </summary>
/// <param name="parameter">參數說明</param>
/// <returns>回傳值說明</returns>
/// <exception cref="ExceptionType">例外情況說明</exception>
/// <example>
/// 使用範例：
/// <code>
/// var result = Method(parameter);
/// </code>
/// </example>
```

## 問題回報

### Bug 回報

使用 GitHub Issues 回報問題，請包含：

1. **問題描述**: 清楚描述問題
2. **重現步驟**: 詳細的重現步驟
3. **預期行為**: 應該發生什麼
4. **實際行為**: 實際發生什麼
5. **環境資訊**: 作業系統、瀏覽器、版本等
6. **錯誤訊息**: 完整的錯誤訊息和堆疊追蹤
7. **截圖**: 如果適用

### 功能請求

1. **功能描述**: 詳細描述建議的功能
2. **使用案例**: 說明為什麼需要此功能
3. **建議實作**: 如果有想法，請分享
4. **替代方案**: 其他可能的解決方案

## 社群準則

### 行為準則

- 保持專業和尊重
- 歡迎新貢獻者
- 建設性的回饋
- 包容不同的觀點

### 溝通管道

- **GitHub Issues**: Bug 回報和功能請求
- **GitHub Discussions**: 一般討論和問題
- **Pull Request**: 程式碼審查和討論
- **Email**: 私人或敏感問題

## 認可

我們感謝所有貢獻者的努力！貢獻者將會：

- 在 CONTRIBUTORS.md 中被列出
- 在發布說明中被提及
- 獲得專案維護者的感謝

## 聯絡資訊

如有任何問題或需要協助，請聯絡：

- **專案維護者**: [<EMAIL>]
- **技術支援**: [<EMAIL>]
- **GitHub**: 在相關 Issue 或 PR 中 @mention 維護者

感謝您對中興社工作日曆系統的貢獻！