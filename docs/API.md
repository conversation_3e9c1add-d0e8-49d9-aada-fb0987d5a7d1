# API 文件 (API Documentation)

本文件詳細說明中興社工作日曆系統的 RESTful API 端點、請求格式、回應格式和使用範例。

## 目錄

- [API 概述](#api-概述)
- [認證機制](#認證機制)
- [通用回應格式](#通用回應格式)
- [錯誤處理](#錯誤處理)
- [API 端點](#api-端點)
  - [工作日曆管理](#工作日曆管理)
  - [班別管理](#班別管理)
  - [週間班別設定](#週間班別設定)
  - [使用者管理](#使用者管理)
- [使用範例](#使用範例)

## API 概述

### 基本資訊

- **Base URL**: `https://your-domain.com/api`
- **API 版本**: v1
- **資料格式**: JSON
- **字元編碼**: UTF-8
- **認證方式**: Windows Authentication

### 端點命名規範

所有 API 端點遵循以下格式：
```
/api/[Controller]/[Action]
```

例如：
- `/api/Workday/GetAll` - 取得所有工作日曆
- `/api/WorkShift/Add` - 新增班別
- `/api/User/GetCurrentUser` - 取得目前使用者資訊

## 認證機制

系統使用 Windows Authentication 進行身分驗證，所有 API 端點都需要有效的 Windows 認證。

### 認證標頭

```http
Authorization: Negotiate <token>
```

### 權限等級

- **一般使用者**: 可查詢自己的工作日曆和班別資訊
- **管理員**: 可管理所有工作日曆、班別和使用者資訊

## 通用回應格式

### 成功回應

```json
{
  "success": true,
  "data": {
    // 實際資料內容
  },
  "message": "操作成功"
}
```

### 錯誤回應

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "錯誤訊息",
    "details": "詳細錯誤資訊"
  }
}
```

## 錯誤處理

### HTTP 狀態碼

| 狀態碼 | 說明 | 使用情境 |
|--------|------|----------|
| 200 | OK | 請求成功 |
| 201 | Created | 資源建立成功 |
| 400 | Bad Request | 請求參數錯誤 |
| 401 | Unauthorized | 未授權存取 |
| 403 | Forbidden | 權限不足 |
| 404 | Not Found | 資源不存在 |
| 500 | Internal Server Error | 伺服器內部錯誤 |

### 錯誤代碼

| 錯誤代碼 | 說明 |
|----------|------|
| `VALIDATION_ERROR` | 資料驗證失敗 |
| `DUPLICATE_ENTRY` | 重複資料 |
| `RESOURCE_NOT_FOUND` | 資源不存在 |
| `PERMISSION_DENIED` | 權限不足 |
| `DATABASE_ERROR` | 資料庫錯誤 |

## API 端點

## 工作日曆管理

### WorkdayController

#### 取得所有工作日曆

```http
GET /api/Workday/GetAll
```

**參數**
- `year` (int, optional): 年份，預設為當年
- `month` (int, optional): 月份，預設為當月
- `shiftId` (int, optional): 班別編號

**回應範例**
```json
{
  "success": true,
  "data": [
    {
      "shiftId": 1,
      "workDate": "2024-01-15",
      "dayType": 1,
      "arrivalTime": "2024-01-15T08:00:00",
      "departureTime": "2024-01-15T17:00:00",
      "workHours": 8.0,
      "weekDay": 1,
      "comment": "正常工作日"
    }
  ]
}
```

#### 新增工作日曆

```http
POST /api/Workday/Add
```

**請求內容**
```json
{
  "shiftId": 1,
  "workDate": "2024-01-15",
  "dayType": 1,
  "arrivalTime": "2024-01-15T08:00:00",
  "departureTime": "2024-01-15T17:00:00",
  "workHours": 8.0,
  "comment": "正常工作日"
}
```

**回應範例**
```json
{
  "success": true,
  "data": true,
  "message": "工作日曆新增成功"
}
```

#### 更新工作日曆

```http
PUT /api/Workday/Update
```

**請求內容**
```json
{
  "shiftId": 1,
  "workDate": "2024-01-15",
  "dayType": 1,
  "arrivalTime": "2024-01-15T08:00:00",
  "departureTime": "2024-01-15T17:00:00",
  "workHours": 8.0,
  "comment": "更新後的工作日"
}
```

#### 刪除工作日曆

```http
DELETE /api/Workday/Delete
```

**參數**
- `shiftId` (int): 班別編號
- `workDate` (string): 工作日期 (yyyy-MM-dd)

#### 批次新增工作日曆

```http
POST /api/Workday/AddBatch
```

**請求內容**
```json
{
  "shiftId": 1,
  "startDate": "2024-01-01",
  "endDate": "2024-01-31",
  "workdays": "1,2,3,4,5",
  "excludeHolidays": true
}
```

## 班別管理

### WorkShiftController

#### 取得所有班別

```http
GET /api/WorkShift/GetAll
```

**回應範例**
```json
{
  "success": true,
  "data": [
    {
      "shiftId": 1,
      "shiftName": "正常班",
      "workdays": "1,2,3,4,5",
      "arrivalTime": "08:00:00",
      "departureTime": "17:00:00",
      "flexibleArrivalBefore": "07:30:00",
      "flexibleArrivalAfter": "09:00:00",
      "flexibleDepartureBefore": "16:30:00",
      "flexibleDepartureAfter": "18:00:00",
      "middayBreakStart": "12:00:00",
      "middayBreakEnd": "13:00:00",
      "comment": "標準上班時間"
    }
  ]
}
```

#### 新增班別

```http
POST /api/WorkShift/Add
```

**請求內容**
```json
{
  "shiftName": "早班",
  "workdays": "1,2,3,4,5",
  "arrivalTime": "07:00:00",
  "departureTime": "16:00:00",
  "flexibleArrivalBefore": "06:30:00",
  "flexibleArrivalAfter": "08:00:00",
  "flexibleDepartureBefore": "15:30:00",
  "flexibleDepartureAfter": "17:00:00",
  "middayBreakStart": "12:00:00",
  "middayBreakEnd": "13:00:00",
  "comment": "早班時間"
}
```

#### 更新班別

```http
PUT /api/WorkShift/Update
```

#### 刪除班別

```http
DELETE /api/WorkShift/Delete/{shiftId}
```

**參數**
- `shiftId` (int): 班別編號

## 週間班別設定

### ShiftWeekdayController

#### 取得班別週間設定

```http
GET /api/ShiftWeekday/GetByShift/{shiftId}
```

**參數**
- `shiftId` (int): 班別編號

**回應範例**
```json
{
  "success": true,
  "data": [
    {
      "shiftId": 1,
      "weekDay": 1,
      "arrivalTime": "08:00:00",
      "departureTime": "17:00:00",
      "workHours": 8.0,
      "flexibleArrivalBefore": "07:30:00",
      "flexibleArrivalAfter": "09:00:00",
      "flexibleDepartureBefore": "16:30:00",
      "flexibleDepartureAfter": "18:00:00",
      "middayBreakStart": "12:00:00",
      "middayBreakEnd": "13:00:00",
      "morningRestStart": "10:00:00",
      "morningRestEnd": "10:15:00",
      "afternoonRestStart": "15:00:00",
      "afternoonRestEnd": "15:15:00"
    }
  ]
}
```

#### 更新週間班別設定

```http
PUT /api/ShiftWeekday/Update
```

**請求內容**
```json
{
  "shiftId": 1,
  "weekDay": 1,
  "arrivalTime": "08:00:00",
  "departureTime": "17:00:00",
  "workHours": 8.0,
  "flexibleArrivalBefore": "07:30:00",
  "flexibleArrivalAfter": "09:00:00",
  "flexibleDepartureBefore": "16:30:00",
  "flexibleDepartureAfter": "18:00:00",
  "middayBreakStart": "12:00:00",
  "middayBreakEnd": "13:00:00"
}
```

#### 取得假日資料

```http
GET /api/ShiftWeekday/Holidays/{year}
```

**參數**
- `year` (int): 年份

**回應範例**
```json
{
  "success": true,
  "data": [
    {
      "date": "2024-01-01",
      "name": "元旦",
      "isLunar": false,
      "dayOff": true
    },
    {
      "date": "2024-02-10",
      "name": "農曆新年",
      "isLunar": true,
      "dayOff": true
    }
  ]
}
```

## 使用者管理

### UserController

#### 取得目前使用者資訊

```http
GET /api/User/GetCurrentUser
```

**回應範例**
```json
{
  "success": true,
  "data": {
    "empNo": "12345",
    "empName": "王小明",
    "deptName": "資訊部",
    "isAdmin": false
  }
}
```

#### 取得所有使用者

```http
GET /api/User/GetAll
```

**權限要求**: 管理員

**回應範例**
```json
{
  "success": true,
  "data": [
    {
      "empNo": "12345",
      "empName": "王小明",
      "deptName": "資訊部",
      "isAdmin": false
    }
  ]
}
```

### LogonUserController

#### 取得登入使用者資訊

```http
GET /api/LogonUser/GetUserInfo
```

**回應範例**
```json
{
  "success": true,
  "data": {
    "userName": "DOMAIN\\username",
    "displayName": "王小明",
    "isAuthenticated": true,
    "roles": ["User"]
  }
}
```

## 使用範例

### JavaScript/TypeScript

#### 基本 API 呼叫

```typescript
// 使用 axios
import axios from 'axios'

const api = axios.create({
  baseURL: '/api',
  withCredentials: true // 重要：包含認證資訊
})

// 取得工作日曆
async function getWorkdays(year: number, month: number) {
  try {
    const response = await api.get('/Workday/GetAll', {
      params: { year, month }
    })
    return response.data
  } catch (error) {
    console.error('取得工作日曆失敗:', error)
    throw error
  }
}

// 新增工作日曆
async function addWorkday(workday: Workday) {
  try {
    const response = await api.post('/Workday/Add', workday)
    return response.data
  } catch (error) {
    console.error('新增工作日曆失敗:', error)
    throw error
  }
}
```

#### 錯誤處理

```typescript
// API 攔截器設定
api.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      // 未授權，重新導向到登入頁面
      window.location.href = '/login'
    } else if (error.response?.status === 403) {
      // 權限不足
      alert('您沒有權限執行此操作')
    } else if (error.response?.status >= 500) {
      // 伺服器錯誤
      alert('系統暫時無法處理請求，請稍後再試')
    }
    return Promise.reject(error)
  }
)
```

### C# 用戶端

```csharp
using System.Net.Http;
using System.Text.Json;

public class WorkdayApiClient
{
    private readonly HttpClient _httpClient;
    
    public WorkdayApiClient(HttpClient httpClient)
    {
        _httpClient = httpClient;
        _httpClient.BaseAddress = new Uri("https://your-domain.com/api/");
    }
    
    public async Task<List<Workday>> GetWorkdaysAsync(int year, int month)
    {
        var response = await _httpClient.GetAsync($"Workday/GetAll?year={year}&month={month}");
        response.EnsureSuccessStatusCode();
        
        var json = await response.Content.ReadAsStringAsync();
        var apiResponse = JsonSerializer.Deserialize<ApiResponse<List<Workday>>>(json);
        
        return apiResponse.Data;
    }
    
    public async Task<bool> AddWorkdayAsync(Workday workday)
    {
        var json = JsonSerializer.Serialize(workday);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        
        var response = await _httpClient.PostAsync("Workday/Add", content);
        response.EnsureSuccessStatusCode();
        
        var responseJson = await response.Content.ReadAsStringAsync();
        var apiResponse = JsonSerializer.Deserialize<ApiResponse<bool>>(responseJson);
        
        return apiResponse.Data;
    }
}
```

## 測試工具

### Swagger UI

開發環境可使用 Swagger UI 進行 API 測試：
- URL: `https://localhost:5001/swagger`
- 提供互動式 API 文件
- 可直接測試 API 端點

### Postman 設定

1. **建立新的 Collection**
2. **設定認證**
   - Type: NTLM
   - Username: 您的 Windows 使用者名稱
   - Password: 您的 Windows 密碼
   - Domain: 您的網域名稱

3. **環境變數**
   ```json
   {
     "baseUrl": "https://localhost:5001/api",
     "year": "2024",
     "month": "1"
   }
   ```

## 版本更新

### v1.0.3 (目前版本)
- 新增批次新增工作日曆功能
- 改善錯誤處理機制
- 優化查詢效能

### 即將推出
- v1.1.0: 新增員工個人排班功能
- v1.2.0: 支援多時區設定

## 支援與回饋

如有 API 使用問題或建議，請聯絡：
- **技術支援**: [<EMAIL>]
- **開發團隊**: [<EMAIL>]