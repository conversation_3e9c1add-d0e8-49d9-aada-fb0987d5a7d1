# 部署指南 (Deployment Guide)

本文件提供中興社工作日曆系統在不同環境下的完整部署指南，包含 IIS 部署、Docker 部署和雲端部署選項。

## 目錄

- [部署概述](#部署概述)
- [系統需求](#系統需求)
- [IIS 部署](#iis-部署)
- [Docker 部署](#docker-部署)
- [資料庫設定](#資料庫設定)
- [設定檔管理](#設定檔管理)
- [SSL/TLS 設定](#ssltls-設定)
- [監控與日誌](#監控與日誌)
- [備份策略](#備份策略)
- [故障排除](#故障排除)

## 部署概述

### 部署架構

```
Internet/Intranet
        ↓
    Load Balancer (Optional)
        ↓
    IIS Web Server
        ↓
    ASP.NET Core App
        ↓
    SQL Server Database
```

### 支援的部署環境

- **生產環境**: Windows Server 2019+ with IIS 10+
- **測試環境**: Windows 10+ with IIS Express
- **開發環境**: Visual Studio Development Server
- **容器化**: Docker on Windows/Linux

## 系統需求

### 硬體需求

#### 最低需求
- **CPU**: 2 核心 2.4 GHz
- **記憶體**: 4 GB RAM
- **硬碟**: 20 GB 可用空間
- **網路**: 100 Mbps

#### 建議需求
- **CPU**: 4 核心 3.0 GHz
- **記憶體**: 8 GB RAM
- **硬碟**: 100 GB SSD
- **網路**: 1 Gbps

### 軟體需求

#### 作業系統
- Windows Server 2019 或更新版本
- Windows 10 Pro/Enterprise (開發/測試環境)

#### 必要軟體
- .NET 8.0 Runtime (ASP.NET Core)
- IIS 10.0 或更新版本
- SQL Server 2019 或更新版本
- Visual C++ Redistributable 2022

#### 選用軟體
- SQL Server Management Studio (SSMS)
- Application Insights (監控)
- Redis (快取，可選)

## IIS 部署

### 1. 準備部署檔案

#### 建置應用程式

```bash
# 清理專案
dotnet clean

# 還原套件
dotnet restore

# 建置發行版本
dotnet publish -c Release -o ./publish --self-contained false

# 或使用 Visual Studio 發行設定檔
dotnet publish -p:PublishProfile=FolderProfile
```

#### 建置前端資源

```bash
cd WorkdayCalendar/ClientApp

# 安裝相依套件
npm install

# 建置生產版本
npm run build

# 檔案會輸出到 dist/ 目錄
```

### 2. IIS 設定

#### 安裝 ASP.NET Core Hosting Bundle

1. 下載 [ASP.NET Core Hosting Bundle](https://dotnet.microsoft.com/download/dotnet/8.0)
2. 執行安裝程式
3. 重新啟動 IIS

```powershell
# 重新啟動 IIS
iisreset
```

#### 建立應用程式集區

```powershell
# 使用 PowerShell 建立應用程式集區
Import-Module WebAdministration

New-WebAppPool -Name "WorkdayCalendarPool" -Force
Set-ItemProperty -Path "IIS:\AppPools\WorkdayCalendarPool" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
Set-ItemProperty -Path "IIS:\AppPools\WorkdayCalendarPool" -Name "managedRuntimeVersion" -Value ""
Set-ItemProperty -Path "IIS:\AppPools\WorkdayCalendarPool" -Name "enable32BitAppOnWin64" -Value $false
```

#### 建立網站

```powershell
# 建立網站
New-Website -Name "WorkdayCalendar" -Port 80 -PhysicalPath "C:\inetpub\wwwroot\WorkdayCalendar" -ApplicationPool "WorkdayCalendarPool"

# 設定 HTTPS 繫結 (如果有 SSL 憑證)
New-WebBinding -Name "WorkdayCalendar" -Protocol "https" -Port 443 -SslFlags 1
```

### 3. 設定 Windows Authentication

#### IIS 管理員設定

1. 開啟 IIS 管理員
2. 選擇 WorkdayCalendar 網站
3. 雙擊「驗證」功能
4. 停用「匿名驗證」
5. 啟用「Windows 驗證」

#### 使用 PowerShell 設定

```powershell
# 停用匿名驗證
Set-WebConfigurationProperty -Filter "system.webServer/security/authentication/anonymousAuthentication" -Name "enabled" -Value "False" -PSPath "IIS:" -Location "WorkdayCalendar"

# 啟用 Windows 驗證
Set-WebConfigurationProperty -Filter "system.webServer/security/authentication/windowsAuthentication" -Name "enabled" -Value "True" -PSPath "IIS:" -Location "WorkdayCalendar"
```

### 4. 部署檔案

#### 複製檔案

```powershell
# 複製發行檔案到 IIS 目錄
Copy-Item -Path ".\publish\*" -Destination "C:\inetpub\wwwroot\WorkdayCalendar" -Recurse -Force

# 設定檔案權限
icacls "C:\inetpub\wwwroot\WorkdayCalendar" /grant "IIS_IUSRS:(OI)(CI)F" /T
```

#### 設定 web.config

確保 web.config 包含正確的設定：

```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore 
        processPath="dotnet" 
        arguments=".\WorkdayCalendar.dll" 
        stdoutLogEnabled="true" 
        stdoutLogFile=".\logs\stdout" 
        hostingModel="inprocess">
        <environmentVariables>
          <environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Production" />
        </environmentVariables>
      </aspNetCore>
    </system.webServer>
  </location>
</configuration>
```

## Docker 部署

### 1. 建立 Dockerfile

```dockerfile
# WorkdayCalendar/Dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# 安裝 Node.js
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# 複製專案檔案
COPY ["WorkdayCalendar/WorkdayCalendar.csproj", "WorkdayCalendar/"]
COPY ["TestWorkdayCalendar/TestWorkdayCalendar.csproj", "TestWorkdayCalendar/"]
RUN dotnet restore "WorkdayCalendar/WorkdayCalendar.csproj"

# 複製所有檔案
COPY . .

# 建置前端
WORKDIR "/src/WorkdayCalendar/ClientApp"
RUN npm install
RUN npm run build

# 建置後端
WORKDIR "/src/WorkdayCalendar"
RUN dotnet build "WorkdayCalendar.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "WorkdayCalendar.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "WorkdayCalendar.dll"]
```

### 2. 建立 docker-compose.yml

```yaml
version: '3.8'

services:
  workday-calendar:
    build:
      context: .
      dockerfile: WorkdayCalendar/Dockerfile
    ports:
      - "8080:80"
      - "8443:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=https://+:443;http://+:80
      - ASPNETCORE_Kestrel__Certificates__Default__Password=your-cert-password
      - ASPNETCORE_Kestrel__Certificates__Default__Path=/https/aspnetapp.pfx
    volumes:
      - ~/.aspnet/https:/https:ro
      - ./logs:/app/logs
    depends_on:
      - sqlserver
    networks:
      - workday-network

  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourStrong@Passw0rd
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - sqlserver-data:/var/opt/mssql
    networks:
      - workday-network

volumes:
  sqlserver-data:

networks:
  workday-network:
    driver: bridge
```

### 3. 部署命令

```bash
# 建置並啟動容器
docker-compose up -d

# 檢視日誌
docker-compose logs -f workday-calendar

# 停止服務
docker-compose down

# 更新服務
docker-compose pull
docker-compose up -d --force-recreate
```

## 資料庫設定

### 1. SQL Server 安裝與設定

#### 安裝 SQL Server

```powershell
# 使用 Chocolatey 安裝 SQL Server Express
choco install sql-server-express

# 或下載並執行官方安裝程式
# https://www.microsoft.com/sql-server/sql-server-downloads
```

#### 設定 SQL Server

```sql
-- 啟用 TCP/IP 連線
EXEC sp_configure 'remote access', 1;
RECONFIGURE;

-- 建立登入帳戶
CREATE LOGIN [WorkdayAdmin] WITH PASSWORD = 'StrongPassword123!';

-- 建立資料庫使用者
USE [Workday];
CREATE USER [WorkdayAdmin] FOR LOGIN [WorkdayAdmin];
ALTER ROLE [db_owner] ADD MEMBER [WorkdayAdmin];
```

### 2. 資料庫初始化

```bash
# 執行資料庫建立腳本
sqlcmd -S localhost -U WorkdayAdmin -P StrongPassword123! -i WorkdayCalendar/App_Data/CreateDb.sql

# 或使用 Windows 驗證
sqlcmd -S localhost -E -i WorkdayCalendar/App_Data/CreateDb.sql
```

### 3. 連線字串設定

#### 生產環境連線字串

```json
{
  "ConnectionStrings": {
    "Workday": "Server=prod-sql-server;Database=Workday;User Id=WorkdayAdmin;Password=StrongPassword123!;TrustServerCertificate=true;Connection Timeout=30;",
    "MIS": "Server=mis-sql-server;Database=MIS;User Id=MISUser;Password=MISPassword123!;TrustServerCertificate=true;Connection Timeout=30;"
  }
}
```

#### 使用 Windows 驗證

```json
{
  "ConnectionStrings": {
    "Workday": "Server=prod-sql-server;Database=Workday;Integrated Security=true;TrustServerCertificate=true;Connection Timeout=30;",
    "MIS": "Server=mis-sql-server;Database=MIS;Integrated Security=true;TrustServerCertificate=true;Connection Timeout=30;"
  }
}
```

## 設定檔管理

### 1. 環境特定設定

#### appsettings.Production.json

```json
{
  "ConnectionStrings": {
    "Workday": "Server=prod-server;Database=Workday;Integrated Security=true;TrustServerCertificate=true;",
    "MIS": "Server=mis-server;Database=MIS;Integrated Security=true;TrustServerCertificate=true;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    },
    "EventLog": {
      "LogLevel": {
        "Default": "Warning"
      }
    }
  },
  "AllowedHosts": "*.sinotech.org.tw"
}
```

### 2. 敏感資訊管理

#### 使用 Azure Key Vault (可選)

```csharp
// Program.cs
public static IHostBuilder CreateHostBuilder(string[] args) =>
    Host.CreateDefaultBuilder(args)
        .ConfigureAppConfiguration((context, config) =>
        {
            if (context.HostingEnvironment.IsProduction())
            {
                var builtConfig = config.Build();
                config.AddAzureKeyVault(
                    $"https://{builtConfig["KeyVaultName"]}.vault.azure.net/",
                    builtConfig["AzureADApplicationId"],
                    builtConfig["AzureADPassword"]);
            }
        })
        .ConfigureWebHostDefaults(webBuilder =>
        {
            webBuilder.UseStartup<Startup>();
        });
```

#### 使用環境變數

```powershell
# 設定環境變數
[Environment]::SetEnvironmentVariable("ConnectionStrings__Workday", "Server=...;", "Machine")
[Environment]::SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "Production", "Machine")
```

## SSL/TLS 設定

### 1. 取得 SSL 憑證

#### 自簽憑證 (測試環境)

```powershell
# 建立自簽憑證
New-SelfSignedCertificate -DnsName "workday.sinotech.local" -CertStoreLocation "cert:\LocalMachine\My" -NotAfter (Get-Date).AddYears(2)
```

#### 商業憑證 (生產環境)

1. 向憑證授權機構 (CA) 申請憑證
2. 安裝憑證到伺服器
3. 在 IIS 中設定 HTTPS 繫結

### 2. IIS HTTPS 設定

```powershell
# 新增 HTTPS 繫結
New-WebBinding -Name "WorkdayCalendar" -Protocol "https" -Port 443 -SslFlags 1

# 設定憑證
$cert = Get-ChildItem -Path "Cert:\LocalMachine\My" | Where-Object {$_.Subject -like "*workday*"}
$binding = Get-WebBinding -Name "WorkdayCalendar" -Protocol "https"
$binding.AddSslCertificate($cert.GetCertHashString(), "My")
```

### 3. 強制 HTTPS

```csharp
// Startup.cs
public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
{
    if (!env.IsDevelopment())
    {
        app.UseHttpsRedirection();
        app.UseHsts();
    }
    
    // 其他中介軟體...
}
```

## 監控與日誌

### 1. 應用程式日誌

#### NLog 設定

```xml
<!-- nlog.config -->
<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  
  <targets>
    <target xsi:type="File" name="fileTarget"
            fileName="logs/workday-${shortdate}.log"
            layout="${longdate} ${uppercase:${level}} ${logger} ${message} ${exception:format=tostring}" />
    
    <target xsi:type="EventLog" name="eventlog"
            source="WorkdayCalendar"
            log="Application"
            layout="${message} ${exception:format=tostring}" />
  </targets>

  <rules>
    <logger name="*" minlevel="Info" writeTo="fileTarget" />
    <logger name="*" minlevel="Error" writeTo="eventlog" />
  </rules>
</nlog>
```

### 2. 效能監控

#### Application Insights 設定

```json
// appsettings.json
{
  "ApplicationInsights": {
    "InstrumentationKey": "your-instrumentation-key"
  }
}
```

```csharp
// Startup.cs
public void ConfigureServices(IServiceCollection services)
{
    services.AddApplicationInsightsTelemetry();
    // 其他服務...
}
```

### 3. 健康檢查

```csharp
// Startup.cs
public void ConfigureServices(IServiceCollection services)
{
    services.AddHealthChecks()
        .AddSqlServer(Configuration.GetConnectionString("Workday"))
        .AddCheck("self", () => HealthCheckResult.Healthy());
}

public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
{
    app.UseHealthChecks("/health");
}
```

## 備份策略

### 1. 資料庫備份

#### 自動備份腳本

```sql
-- 建立備份作業
BACKUP DATABASE [Workday] 
TO DISK = 'C:\Backup\Workday_Full.bak'
WITH FORMAT, INIT, 
NAME = 'Workday-Full Database Backup', 
SKIP, NOREWIND, NOUNLOAD, STATS = 10;

-- 建立差異備份
BACKUP DATABASE [Workday] 
TO DISK = 'C:\Backup\Workday_Diff.bak'
WITH DIFFERENTIAL, FORMAT, INIT,
NAME = 'Workday-Differential Database Backup',
SKIP, NOREWIND, NOUNLOAD, STATS = 10;
```

#### PowerShell 備份腳本

```powershell
# BackupDatabase.ps1
param(
    [string]$ServerName = "localhost",
    [string]$DatabaseName = "Workday",
    [string]$BackupPath = "C:\Backup"
)

$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$backupFile = "$BackupPath\${DatabaseName}_$timestamp.bak"

$query = @"
BACKUP DATABASE [$DatabaseName] 
TO DISK = '$backupFile'
WITH FORMAT, INIT, 
NAME = '$DatabaseName-Full Database Backup', 
SKIP, NOREWIND, NOUNLOAD, STATS = 10;
"@

Invoke-Sqlcmd -ServerInstance $ServerName -Query $query
Write-Host "備份完成: $backupFile"
```

### 2. 應用程式備份

```powershell
# BackupApplication.ps1
$sourceDir = "C:\inetpub\wwwroot\WorkdayCalendar"
$backupDir = "C:\Backup\Application"
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$backupPath = "$backupDir\WorkdayCalendar_$timestamp"

# 建立備份目錄
New-Item -ItemType Directory -Path $backupPath -Force

# 複製檔案
Copy-Item -Path "$sourceDir\*" -Destination $backupPath -Recurse -Force

# 壓縮備份
Compress-Archive -Path $backupPath -DestinationPath "$backupPath.zip"
Remove-Item -Path $backupPath -Recurse -Force

Write-Host "應用程式備份完成: $backupPath.zip"
```

## 故障排除

### 1. 常見問題

#### 應用程式無法啟動

```powershell
# 檢查事件日誌
Get-EventLog -LogName Application -Source "IIS AspNetCore Module V2" -Newest 10

# 檢查應用程式日誌
Get-Content "C:\inetpub\wwwroot\WorkdayCalendar\logs\stdout*.log" -Tail 50
```

#### 資料庫連線失敗

```sql
-- 測試連線
SELECT @@SERVERNAME, @@VERSION;

-- 檢查登入狀態
SELECT name, is_disabled FROM sys.sql_logins WHERE name = 'WorkdayAdmin';

-- 檢查使用者權限
SELECT 
    dp.name AS principal_name,
    dp.type_desc AS principal_type_desc,
    r.name AS role_name
FROM sys.database_role_members rm
JOIN sys.database_principals dp ON rm.member_principal_id = dp.principal_id
JOIN sys.database_principals r ON rm.role_principal_id = r.principal_id
WHERE dp.name = 'WorkdayAdmin';
```

### 2. 效能問題

#### 檢查 CPU 和記憶體使用率

```powershell
# 檢查 CPU 使用率
Get-Counter "\Processor(_Total)\% Processor Time" -SampleInterval 1 -MaxSamples 5

# 檢查記憶體使用率
Get-Counter "\Memory\Available MBytes" -SampleInterval 1 -MaxSamples 5

# 檢查 IIS 工作程序
Get-Process w3wp | Select-Object Id, ProcessName, CPU, WorkingSet
```

#### SQL Server 效能監控

```sql
-- 檢查長時間執行的查詢
SELECT 
    s.session_id,
    r.status,
    r.command,
    r.cpu_time,
    r.total_elapsed_time,
    t.text
FROM sys.dm_exec_requests r
CROSS APPLY sys.dm_exec_sql_text(r.sql_handle) t
JOIN sys.dm_exec_sessions s ON r.session_id = s.session_id
WHERE r.total_elapsed_time > 5000; -- 超過 5 秒的查詢

-- 檢查阻塞
SELECT 
    blocking_session_id,
    session_id,
    wait_type,
    wait_time,
    wait_resource
FROM sys.dm_exec_requests
WHERE blocking_session_id <> 0;
```

### 3. 診斷工具

#### 啟用詳細日誌

```json
// appsettings.json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft": "Debug",
      "Microsoft.EntityFrameworkCore": "Information"
    }
  }
}
```

#### 使用 dotnet-dump

```bash
# 安裝診斷工具
dotnet tool install --global dotnet-dump

# 建立記憶體傾印
dotnet-dump collect -p <process-id>

# 分析傾印檔案
dotnet-dump analyze <dump-file>
```

## 維護計畫

### 1. 定期維護

- **每日**: 檢查應用程式日誌和錯誤
- **每週**: 檢查系統效能和資源使用率
- **每月**: 更新安全性修補程式
- **每季**: 檢查和更新相依套件

### 2. 更新程序

1. **測試環境驗證**
2. **建立系統備份**
3. **部署新版本**
4. **驗證功能正常**
5. **監控系統穩定性**

### 3. 災難復原

1. **備份還原測試**
2. **容錯移轉程序**
3. **復原時間目標 (RTO): 4 小時**
4. **復原點目標 (RPO): 1 小時**

## 聯絡資訊

如有部署相關問題，請聯絡：
- **系統管理員**: [<EMAIL>]
- **技術支援**: [<EMAIL>]
- **緊急聯絡**: [<EMAIL>]