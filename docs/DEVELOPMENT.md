# 開發指南 (Development Guide)

本文件提供中興社工作日曆系統的完整開發指南，包含環境設定、開發流程、程式碼規範和最佳實務。

## 目錄

- [開發環境設定](#開發環境設定)
- [專案架構](#專案架構)
- [開發流程](#開發流程)
- [程式碼規範](#程式碼規範)
- [測試指南](#測試指南)
- [除錯技巧](#除錯技巧)
- [效能最佳化](#效能最佳化)

## 開發環境設定

### 必要軟體

1. **Visual Studio 2022** 或 **Visual Studio Code**
   - 安裝 C# 擴充套件
   - 安裝 Vue.js 擴充套件

2. **.NET 8.0 SDK**
   ```bash
   # 驗證安裝
   dotnet --version
   # 應顯示 8.0.x
   ```

3. **Node.js 16+**
   ```bash
   # 驗證安裝
   node --version
   npm --version
   ```

4. **SQL Server 2019+**
   - SQL Server Management Studio (SSMS)
   - 或 Azure Data Studio

### 開發環境配置

1. **複製專案**
   ```bash
   git clone <repository-url>
   cd WorkdayCalendar
   ```

2. **設定資料庫**
   ```sql
   -- 建立 Workday 資料庫
   sqlcmd -S localhost -E -i WorkdayCalendar/App_Data/CreateDb.sql
   ```

3. **設定連線字串**
   ```json
   // appsettings.Development.json
   {
     "ConnectionStrings": {
       "Workday": "server=localhost;database=Workday;Integrated Security=true;TrustServerCertificate=true;",
       "MIS": "server=localhost;database=MIS;Integrated Security=true;TrustServerCertificate=true;"
     }
   }
   ```

4. **安裝前端相依套件**
   ```bash
   cd WorkdayCalendar/ClientApp
   npm install
   ```

5. **啟動開發伺服器**
   ```bash
   # 終端機 1: 啟動後端 API
   cd WorkdayCalendar
   dotnet run
   
   # 終端機 2: 啟動前端開發伺服器
   cd WorkdayCalendar/ClientApp
   npm run dev
   ```

### IDE 設定

#### Visual Studio 2022

1. **安裝擴充套件**
   - Vue.js Pack
   - TypeScript and JavaScript Language Service

2. **專案設定**
   - 設定啟動專案為 WorkdayCalendar
   - 啟用 IIS Express 與 Windows Authentication

#### Visual Studio Code

1. **必要擴充套件**
   ```json
   {
     "recommendations": [
       "ms-dotnettools.csharp",
       "Vue.volar",
       "bradlc.vscode-tailwindcss",
       "ms-vscode.vscode-typescript-next"
     ]
   }
   ```

2. **工作區設定**
   ```json
   // .vscode/settings.json
   {
     "typescript.preferences.importModuleSpecifier": "relative",
     "vue.codeActions.enabled": true,
     "editor.formatOnSave": true
   }
   ```

## 專案架構

### 後端架構 (.NET Core)

```
WorkdayCalendar/
├── Controllers/          # API 控制器
│   ├── WorkdayController.cs
│   ├── WorkShiftController.cs
│   └── ...
├── Models/              # 資料模型
│   ├── Workday.cs
│   ├── WorkShift.cs
│   └── ...
├── DTO/                 # 資料傳輸物件
│   ├── WorkdayDTO.cs
│   └── ...
├── App_Code/            # 業務邏輯層
│   ├── SqlUtil.cs       # SQL 工具類別
│   ├── EmployeeDAO.cs   # 員工資料存取
│   └── ...
└── ClientApp/           # 前端應用程式
```

### 前端架構 (Vue.js)

```
ClientApp/src/
├── components/          # 可重用元件
│   ├── WorkShift.vue
│   ├── ShiftWeekday.vue
│   └── ...
├── pages/              # 頁面元件
│   ├── Workday.vue
│   ├── Shifts.vue
│   └── ...
├── router/             # 路由設定
├── store/              # 狀態管理 (Pinia)
├── types/              # TypeScript 型別定義
└── assets/             # 靜態資源
```

## 開發流程

### 新功能開發

1. **建立功能分支**
   ```bash
   git checkout -b feature/new-feature-name
   ```

2. **後端開發**
   - 建立或修改 Model
   - 建立或修改 DTO
   - 實作 Controller 方法
   - 撰寫單元測試

3. **前端開發**
   - 建立或修改 Vue 元件
   - 更新路由設定
   - 實作 API 呼叫
   - 撰寫元件測試

4. **整合測試**
   - 測試 API 端點
   - 測試前端功能
   - 跨瀏覽器測試

5. **提交變更**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   git push origin feature/new-feature-name
   ```

### 程式碼審查

1. **建立 Pull Request**
   - 提供清楚的功能描述
   - 包含測試結果截圖
   - 標註相關的 Issue

2. **審查重點**
   - 程式碼品質與可讀性
   - 安全性考量
   - 效能影響
   - 測試覆蓋率

## 程式碼規範

### C# 後端規範

#### 命名規範

```csharp
// 類別名稱: PascalCase
public class WorkdayController : ControllerBase

// 方法名稱: PascalCase
public async Task<IActionResult> GetWorkdays()

// 變數名稱: camelCase
private readonly IConfiguration configuration;

// 常數名稱: UPPER_CASE
private const int MAX_WORK_HOURS = 12;
```

#### 註解規範

```csharp
/// <summary>
/// 取得指定日期範圍內的工作日曆資料
/// </summary>
/// <param name="startDate">開始日期</param>
/// <param name="endDate">結束日期</param>
/// <param name="shiftId">班別編號，可選參數</param>
/// <returns>工作日曆資料列表</returns>
/// <remarks>
/// 此方法會根據指定的日期範圍查詢工作日曆資料，
/// 如果提供班別編號，則只回傳該班別的資料。
/// </remarks>
public async Task<List<WorkdayDto>> GetWorkdays(
    DateTime startDate, 
    DateTime endDate, 
    int? shiftId = null)
{
    // 驗證輸入參數
    if (startDate > endDate)
    {
        throw new ArgumentException("開始日期不能大於結束日期");
    }
    
    // 建立查詢條件
    var query = _context.Workdays
        .Where(w => w.WorkDate >= startDate && w.WorkDate <= endDate);
    
    // 如果指定班別，加入班別篩選條件
    if (shiftId.HasValue)
    {
        query = query.Where(w => w.ShiftId == shiftId.Value);
    }
    
    return await query.ToListAsync();
}
```

#### 錯誤處理

```csharp
try
{
    // 執行業務邏輯
    var result = await ProcessWorkday(workday);
    return Ok(result);
}
catch (ValidationException ex)
{
    // 記錄驗證錯誤
    _logger.LogWarning(ex, "工作日曆資料驗證失敗: {Message}", ex.Message);
    return BadRequest(ex.Message);
}
catch (SqlException ex)
{
    // 記錄資料庫錯誤
    _logger.LogError(ex, "資料庫操作失敗");
    return StatusCode(500, "系統暫時無法處理請求，請稍後再試");
}
```

### Vue.js 前端規範

#### 元件結構

```vue
<template>
  <!-- 模板內容 -->
  <div class="workday-calendar">
    <h2>{{ title }}</h2>
    <!-- 其他內容 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { Workday } from '@/types/Workday'

// Props 定義
interface Props {
  title: string
  shiftId?: number
}

const props = withDefaults(defineProps<Props>(), {
  shiftId: 1
})

// Emits 定義
interface Emits {
  (e: 'update', workday: Workday): void
  (e: 'delete', id: number): void
}

const emit = defineEmits<Emits>()

// 響應式資料
const workdays = ref<Workday[]>([])
const loading = ref(false)

// 計算屬性
const filteredWorkdays = computed(() => {
  return workdays.value.filter(w => w.shiftId === props.shiftId)
})

// 生命週期
onMounted(async () => {
  await loadWorkdays()
})

/**
 * 載入工作日曆資料
 */
async function loadWorkdays(): Promise<void> {
  try {
    loading.value = true
    // API 呼叫邏輯
  } catch (error) {
    console.error('載入工作日曆失敗:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.workday-calendar {
  /* 樣式定義 */
}
</style>
```

#### TypeScript 型別定義

```typescript
// types/Workday.ts
export interface Workday {
  shiftId: number
  workDate: string
  dayType: number
  arrivalTime: string
  departureTime: string
  workHours: number
  comment?: string
}

export interface WorkShift {
  shiftId: number
  shiftName: string
  workdays: string
  arrivalTime: string
  departureTime: string
}

// API 回應型別
export interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
}
```

## 測試指南

### 後端單元測試

```csharp
[Fact]
public async Task GetWorkdays_ValidDateRange_ReturnsWorkdays()
{
    // Arrange
    var startDate = new DateTime(2024, 1, 1);
    var endDate = new DateTime(2024, 1, 31);
    var expectedCount = 5;
    
    // 設定模擬資料
    var mockWorkdays = CreateMockWorkdays(expectedCount);
    _mockRepository.Setup(r => r.GetWorkdaysAsync(startDate, endDate))
                  .ReturnsAsync(mockWorkdays);
    
    // Act
    var result = await _controller.GetWorkdays(startDate, endDate);
    
    // Assert
    var okResult = Assert.IsType<OkObjectResult>(result);
    var workdays = Assert.IsType<List<WorkdayDto>>(okResult.Value);
    Assert.Equal(expectedCount, workdays.Count);
}
```

### 前端元件測試

```typescript
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import WorkShift from '@/components/WorkShift.vue'

describe('WorkShift.vue', () => {
  it('renders shift name correctly', () => {
    const wrapper = mount(WorkShift, {
      props: {
        shift: {
          shiftId: 1,
          shiftName: '正常班',
          arrivalTime: '08:00',
          departureTime: '17:00'
        }
      }
    })
    
    expect(wrapper.text()).toContain('正常班')
  })
  
  it('emits update event when shift is modified', async () => {
    const wrapper = mount(WorkShift)
    
    await wrapper.find('input[name="shiftName"]').setValue('新班別')
    await wrapper.find('form').trigger('submit')
    
    expect(wrapper.emitted('update')).toBeTruthy()
  })
})
```

## 除錯技巧

### 後端除錯

1. **使用 Swagger UI**
   - 開發環境: `https://localhost:5001/swagger`
   - 測試 API 端點
   - 檢視請求/回應格式

2. **日誌記錄**
   ```csharp
   _logger.LogInformation("處理工作日曆請求: {StartDate} - {EndDate}", 
                         startDate, endDate);
   ```

3. **SQL 查詢除錯**
   ```csharp
   // 啟用 EF Core 查詢日誌
   protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
   {
       optionsBuilder.LogTo(Console.WriteLine, LogLevel.Information);
   }
   ```

### 前端除錯

1. **Vue DevTools**
   - 安裝瀏覽器擴充套件
   - 檢視元件狀態
   - 監控 Pinia Store

2. **網路請求除錯**
   ```typescript
   // 使用 axios 攔截器
   axios.interceptors.request.use(config => {
     console.log('API Request:', config)
     return config
   })
   
   axios.interceptors.response.use(
     response => {
       console.log('API Response:', response)
       return response
     },
     error => {
       console.error('API Error:', error)
       return Promise.reject(error)
     }
   )
   ```

## 效能最佳化

### 後端最佳化

1. **資料庫查詢最佳化**
   ```csharp
   // 使用 AsNoTracking 提升查詢效能
   var workdays = await _context.Workdays
       .AsNoTracking()
       .Where(w => w.WorkDate >= startDate)
       .ToListAsync();
   
   // 使用投影減少資料傳輸
   var workdayDtos = await _context.Workdays
       .Select(w => new WorkdayDto
       {
           ShiftId = w.ShiftId,
           WorkDate = w.WorkDate,
           WorkHours = w.WorkHours
       })
       .ToListAsync();
   ```

2. **快取策略**
   ```csharp
   // 記憶體快取
   [ResponseCache(Duration = 3600)] // 快取 1 小時
   public async Task<IActionResult> GetHolidays(int year)
   {
       var cacheKey = $"holidays_{year}";
       if (!_cache.TryGetValue(cacheKey, out List<Holiday> holidays))
       {
           holidays = await _holidayService.GetHolidaysAsync(year);
           _cache.Set(cacheKey, holidays, TimeSpan.FromHours(24));
       }
       return Ok(holidays);
   }
   ```

### 前端最佳化

1. **元件懶載入**
   ```typescript
   // router/index.ts
   const routes = [
     {
       path: '/workday',
       component: () => import('@/pages/Workday.vue')
     }
   ]
   ```

2. **API 請求最佳化**
   ```typescript
   // 使用防抖減少 API 呼叫
   import { debounce } from 'lodash-es'
   
   const searchWorkdays = debounce(async (keyword: string) => {
     const result = await api.searchWorkdays(keyword)
     workdays.value = result.data
   }, 300)
   ```

## 常見問題

### Q: 如何新增新的 API 端點？

A: 
1. 在對應的 Controller 中新增方法
2. 加上適當的 HTTP 動詞屬性 (`[HttpGet]`, `[HttpPost]` 等)
3. 加上 `[Authorize]` 屬性確保需要驗證
4. 撰寫完整的 XML 註解
5. 新增對應的單元測試

### Q: 如何處理跨元件通訊？

A: 
1. 父子元件: 使用 props 和 emits
2. 兄弟元件: 使用 Pinia store
3. 深層巢狀: 使用 provide/inject

### Q: 如何處理表單驗證？

A: 
1. 後端: 使用 Data Annotations 或 FluentValidation
2. 前端: 使用 VeeValidate 或自定義驗證邏輯

## 相關資源

- [Vue.js 官方文件](https://vuejs.org/)
- [.NET Core 官方文件](https://docs.microsoft.com/dotnet/core/)
- [PrimeVue 元件庫](https://primefaces.org/primevue/)
- [TypeScript 手冊](https://www.typescriptlang.org/docs/)